package com.bsc.intg.svcs.core.common.services;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.vfs2.FileSystemException;
import org.apache.commons.vfs2.FileSystemOptions;
import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestination;
import com.bsc.intg.svcs.core.BSCIntegrationServiceBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.vfs.BSCDefaultVFSProvider;
import com.bsc.intg.svcs.core.vfs.BSCVFSObjectInfo;
import com.bsc.intg.svcs.core.vfs.BSCVFSProvider;

public class BSCFxferTriggerService extends BSCIntegrationServiceBase {

	protected List<BSCDestination> destList = new ArrayList<BSCDestination>();
	private boolean batch = false;
	private String triggerFileSuffix = null;

	public BSCFxferTriggerService(BSCComponentType componentType, BSCComponent parentComponent, String configName,
			String instance) {
		super(componentType, parentComponent, configName, instance);
	}

	@Override
	public BSCMessage service(BSCMessage inMessage) throws BSCServiceException {
		logger.info("Executing FxferTriggerService Service");
		
		BSCMessage outMessage = null;
		BSCVFSProvider vfs = null;
		BSCComponent listener = null;
		FileSystemOptions options = null;
		List<BSCVFSObjectInfo> matchedFiles = null;
		List<BSCVFSObjectInfo> tmpMatchedFiles = null;

		String triggerFileName = inMessage.getProperty("VFS.FILE.NAME");
		String fileURL = inMessage.getProperty("VFS.FILE.URL");
		String fileURI = fileURL.substring(0, fileURL.lastIndexOf("/") + 1);
		List<String> eventFileList = new ArrayList<>();
		try {
			listener = inMessage.getMessageOwner();
			vfs = new BSCDefaultVFSProvider(logger, (String)  listener.getProperty("protocol"), listener);
			vfs.init();
			options = BSCDefaultVFSProvider.createFileSystemOptions(listener.getProperty("protocol", "file"), listener);
			
			//Trigger File initiates delivery of multiple files
			if (batch) {
				logger.debug("Trigger file based processing started in btach mode");
				tmpMatchedFiles = vfs.listFiles(fileURI, options);
				int publishSize = destList.size();
				int destCtr=0;
				for(BSCDestination publish : destList){	
					matchedFiles = new ArrayList<BSCVFSObjectInfo>(tmpMatchedFiles);
					destCtr++;
					for (Iterator<BSCVFSObjectInfo> fileItr = matchedFiles.iterator(); fileItr.hasNext();) {
						BSCVFSObjectInfo file = fileItr.next();
						String fileName = file.getFileName();
						//Filter out Trigger file from files to be delivered
						if (!fileName.contains(triggerFileName)) {
							fileURL = fileURI + fileName;
							inMessage.setProperty("VFS.FILE.URL", fileURL);
							inMessage.setProperty("VFS.FILE.NAME", fileName);
							
							logger.info("Delivery intiated for file {} to destination {} ", fileName,publish.getConfigName());
							outMessage = publish.send(inMessage);	
							logger.info("Delivered file {} to destination {} ", fileName,publish.getConfigName());
							if(destCtr == publishSize)
								vfs.delete(fileURL, options);
							eventFileList.add(outMessage.getStringMessage());
						}					
						fileItr.remove();
					}
				}
			} else {
				//Trigger File initiates delivery of single file with filename part of trigger file
				logger.debug("Trigger file based processing started in non-btach mode");
				String fileName = triggerFileName.substring(0, triggerFileName.lastIndexOf(triggerFileSuffix));
				fileURL = fileURL.substring(0, fileURL.lastIndexOf(triggerFileName)) + fileName;
				inMessage.setProperty("VFS.FILE.URL", fileURL);
				inMessage.setProperty("VFS.FILE.NAME", fileName);
				
				for(BSCDestination publish : destList){
					logger.info("Delivery intiated for file {} to destination {} ", fileName,publish.getConfigName());
					outMessage = publish.send(inMessage);	
					logger.info("Delivered file {} to destination {} ", fileName,publish.getConfigName());
					vfs.delete(fileURL, options);
					eventFileList.add(outMessage.getStringMessage());
				}
			}

			outMessage = inMessage.cloneMessage(false);
			outMessage.setProperty("include.properties.prefix", inMessage.getMessageOwner().getConfigName() + ".Meta./" ); 
			outMessage.loadMessage(eventFileList.stream().map(x -> x).collect(Collectors.joining(" \n ")));

		} catch (BSCDestinationException | BSCComponentInitializationException | Exception | BSCMessageException e) {
			throw new BSCServiceException(this, e, BSCErrorLevel.ROLLBACK, "Failed to execute the app service");
		} finally {
			if (vfs != null) {
				try {
					vfs.close();
				} catch (FileSystemException e) {
					e.printStackTrace();
				}
			}
		}

		return outMessage;
	}

	@Override
	public void initialize() throws BSCComponentInitializationException {

		super.initialize();

		valid = true;
		batch = this.getBooleanProperty("batch",false);
		triggerFileSuffix = this.getProperty("trigger.file.suffix",".done");
		String destinations = this.getProperty("destinations");
		List<String> destList = Arrays.asList(destinations.split("\\s*,\\s*"));

		for(String dest : destList){			
			this.destList.add(this.getDestination(dest));
		}

	}

}
