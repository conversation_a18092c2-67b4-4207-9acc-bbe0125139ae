package com.bsc.intg.svcs.core.test;

/**
 * 
 * <PERSON><PERSON> helper to Connect/Publish to JMS queues
 * 
 * 
 * <AUTHOR>
 *         <p>
 *         $Revision: 1.2 $
 *         <p>
 */

import java.io.File;
import java.io.FileReader;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Collections;
import java.util.Map;
import java.util.Properties;
import java.util.Map.Entry;
import java.util.concurrent.atomic.AtomicReference;
import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.QueueBrowser;
import javax.jms.Session;
import javax.jms.TextMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jms.connection.CachingConnectionFactory;
import org.springframework.jms.core.BrowserCallback;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.jms.core.MessageCreator;
import com.ibm.mq.jms.MQQueueConnectionFactory;

public class JMSTestHelper {


	private Logger logger = null;

	JmsTemplate jmsTemplate = new JmsTemplate();

	MQQueueConnectionFactory mqConnFactory = new MQQueueConnectionFactory();
	CachingConnectionFactory cacheFactory = new CachingConnectionFactory();

	String host,qmgr;	
	int port;
	String channel;
	int transport;
	boolean isccdt = false;
	String ccdtURL = "";
	
	public JMSTestHelper(String prefix, String testCase) {

		logger = LoggerFactory.getLogger(testCase + ".JMSTestHelper");	

		try{

			//Load application prop file
			Properties props = new Properties();
			props.load(new FileReader(new File("src/main/resources/application-int.properties")));
			
			ccdtURL = System.getProperty("mq.ccdt.url");
			
			if (props.getProperty( prefix + "." + "MQGW.ccdt.url")!=null) {
				qmgr = props.getProperty( prefix + "." + "MQGW.qmgr");
				isccdt =true;
			}else{
				host = props.getProperty(prefix + ".MQGW.host");
				port = Integer.parseInt(props.getProperty(prefix + ".MQGW.port"));
				channel = props.getProperty(prefix + ".MQGW.channel");
				transport = Integer.parseInt(props.getProperty(prefix + ".MQGW.transport"));			
			}
			
			//Construct JMS Template
			createJmsTemplate();
		}

		catch(Exception e){
			logger.error("Exception occured while creating JMS Template - " + e.toString());	
		}

	}

	public void createMQConnectionFactory() throws Exception{

		if (isccdt) {

			URL url = null;
			try {
				url = new URL(ccdtURL);
			} catch (MalformedURLException e) {
				e.printStackTrace();
			}
			mqConnFactory.setCCDTURL(url);
			mqConnFactory.setQueueManager(qmgr);
			logger.info("MQConnectionFactory details: CCDTURL - {}, QueueManager - {}", ccdtURL, qmgr);
			
		}else{

			//mqConnFactory.setUseConnectionPooling(true);
			mqConnFactory.setHostName(host);
			mqConnFactory.setPort(port);
			mqConnFactory.setChannel(channel);
			mqConnFactory.setTransportType(transport);
			logger.info("MQConnectionFactory details: Host - {}, port - {}, channel - {}, transport - {}", host, port, channel, transport);
		}

	}

	public void createCachingConnFactory() throws Exception{

		cacheFactory.setTargetConnectionFactory(mqConnFactory);
		cacheFactory.setCacheProducers(true);
		cacheFactory.setSessionCacheSize(10);

		logger.info("CachingConnectionFactory - {}", cacheFactory);

	}

	public void createJmsTemplate() throws Exception{	

		createMQConnectionFactory();
		createCachingConnFactory();
		jmsTemplate.setConnectionFactory(cacheFactory);

		logger.info("JMS Template - {}",jmsTemplate);
	}

	public int getMessageCount(String destination) throws Exception
	{		
		return jmsTemplate.browse(destination,new BrowserCallback<Integer>() {
			@SuppressWarnings("unchecked")
			@Override			
			public Integer doInJms(Session s, QueueBrowser qb) throws JMSException
			{  
				return Collections.list(qb.getEnumeration()).size();
			}
		});
	}

	public void purgeQueue(String destination) throws Exception {

		int queueDepth = getMessageCount(destination);
		logger.info("Queue depth on {} is - {}" ,destination, queueDepth);

		for(int i=1; i<=queueDepth; i++){
			jmsTemplate.setReceiveTimeout(3000);
			jmsTemplate.receive(destination);			
		}

		logger.info("Successfully deleted {} messages from queue - {}" ,queueDepth, destination);

	}

	public String publishMessage(final String messageContent, String destination, String tcName, String correlID) throws Exception {

		final AtomicReference<TextMessage> arJMSMessage = new AtomicReference<TextMessage>();
		jmsTemplate.send(destination,new MessageCreator() {

			public Message createMessage(Session session) throws JMSException {

				TextMessage jmsTextMessage;
				jmsTextMessage = session.createTextMessage(messageContent);
				jmsTextMessage.setJMSCorrelationID(correlID);
				jmsTextMessage.setStringProperty("TestCase", tcName);
				arJMSMessage.set(jmsTextMessage);

				return arJMSMessage.get();
			}
		});
		logger.info("Message is published to {} with JMS properties - CORRELATIONID - {} , TestCase - {}" , destination, correlID, tcName);
		return arJMSMessage.get().getJMSMessageID();
	}

	public String publishMessage(final String messageContent, String destination, String tcName, String correlID,Map<String,String> userPropMap) throws Exception {

		final AtomicReference<TextMessage> arJMSMessage = new AtomicReference<TextMessage>();
		jmsTemplate.send(destination,new MessageCreator() {

			public Message createMessage(Session session) throws JMSException {

				TextMessage jmsTextMessage;
				jmsTextMessage = session.createTextMessage(messageContent);
				jmsTextMessage.setJMSCorrelationID(correlID);
				jmsTextMessage.setStringProperty("TestCase", tcName);
				for(Entry<String,String> pp: userPropMap.entrySet()) {
					jmsTextMessage.setStringProperty(pp.getKey(), (String)pp.getValue());
				}
				arJMSMessage.set(jmsTextMessage);

				return arJMSMessage.get();
			}
		});
		logger.info("Message is published to {} with JMS properties - CORRELATIONID - {} , TestCase - {}" , destination, correlID, tcName);
		return arJMSMessage.get().getJMSMessageID();
	}

}
