
package com.bsc.intg.svcs.core.test;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.atomic.AtomicInteger;
import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentSubType;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestinationBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCPropertyHelper;

public class BSCEmailDestinationStub extends BSCDestinationBase {

	public BSCEmailDestinationStub(BSCComponentType componentType, BSCComponent parentComponent, String configName) {
		super(componentType, parentComponent, configName);
	}

	AtomicInteger EmailDstcount = new AtomicInteger(0);
	ArrayList<String> tcList = new ArrayList<String>();

	public void initialize() throws BSCComponentInitializationException {

		super.initialize();

		logger.info("Initializing Email Stub");

		logger.info("Initial Email stub destination count - {}", EmailDstcount.get());

	}

	@Override
	public BSCMessage onSend(BSCMessage message) throws BSCDestinationException {

		logger.info("Executing onSend method - Email Stub");

		logger.info(
				"EmailFrom - {}, EmailTo - {}, EmailCC - {}, EmailBCC - {}, EmailSubject - {}, Application - {}, Priority - {} ",
				getProperty("email.from"), getProperty("email.to"), getProperty("email.cc"), getProperty("email.bcc"),
				getProperty("email.subject"), getProperty("email.application"), getProperty("email.priority"));

		String serviceStatus = message.getProperty("JMS_CORRELATION_ID", "Up");

		boolean isRestController = message.getMessageOwner().getComponentSubType()
				.equals(BSCComponentSubType.REST_CONTROLLER);
		//String tcName = message.getProperty("JMS.USER.TestCase");
		String    tcName    = System.getProperty("user.testcase","Default");
		
		if (isRestController) {
			tcName = message.getProperty("HTTP.testcase");
			serviceStatus = message.getProperty("HTTP.servicestatus");
		}

		String sep = File.separator;
		String workingDir = System.getProperty("user.dir");
		String outputDir = workingDir + sep + "test" + sep + "output" + sep;

		logger.info("Test case - {}", tcName);
		logger.info("Service status - {}", serviceStatus);
		logger.info("Output file location - {}", outputDir);
		logger.info("isRestController - {}", isRestController);
		logger.info("isRestController - {}", message);

		boolean matchFound = false;

		for (int idx = 0; idx < tcList.size(); idx++) {
			if (tcList.get(idx).equals(tcName)) {
				matchFound = true;
			}
		}

		if (!matchFound) {
			EmailDstcount.set(0);
			tcList.add(tcName);
		}

		String fileName = configName + "_" + tcName + "_" + String.format("%02d", EmailDstcount.incrementAndGet())
				+ ".txt";
		logger.info("Output file - {} ", fileName);

		String deliveryCount = message.getProperty("JMS.USER.JMSXDeliveryCount");

		logger.info("deliveryCount  - {} ", deliveryCount);

		try {
			if (!new File(outputDir).exists()) {
				new File(outputDir).mkdir();
			}

			String filePath = outputDir + fileName;

			if (serviceStatus.equals("Down")) {
				if (deliveryCount == null || Integer.parseInt(deliveryCount) < 3) {

					logger.error("EmailDestinationStub is down");
					writeToFile(message.getProperties(), "EmailDestinationStub is down", filePath);
					throw new BSCDestinationException(this, BSCErrorLevel.CRITICAL, "EmailDestinationStub is down");
				}
			} else {
				writeToFile(message.getProperties(), message.toString(), filePath);
			}

		}

		catch (BSCDestinationException | IOException e) {
			logger.error("Failed to process the request" + e.toString());
			message.setException(e);
			throw new BSCDestinationException(this, e, BSCErrorLevel.CRITICAL, "Failed to process the request");
		}

		return message;
	}

	public void writeToFile(Map<String, Object> props, String data, String filePath) throws IOException {

		BufferedWriter writer = new BufferedWriter(new FileWriter(filePath));
		Map<String, Object> httpHeaders = (Map<String, Object>) BSCPropertyHelper.getPropertiesStartingWith(props,
				"HTTP", false);
		for (Entry<String, Object> p : httpHeaders.entrySet()) {
			writer.write(p + "\n");
		}
		Map<String, Object> jmsHeaders = (Map<String, Object>) BSCPropertyHelper.getPropertiesStartingWith(props, "JMS",
				false);
		for (Entry<String, Object> p : jmsHeaders.entrySet()) {
			writer.write(p + "\n");
		}
		
		writer.write(data + "\n");
		writer.close();

	}

	@Override
	public List<BSCMessage> onSend(List<BSCMessage> message) throws BSCDestinationException {
		// TODO Auto-generated method stub
		return null;
	}

}