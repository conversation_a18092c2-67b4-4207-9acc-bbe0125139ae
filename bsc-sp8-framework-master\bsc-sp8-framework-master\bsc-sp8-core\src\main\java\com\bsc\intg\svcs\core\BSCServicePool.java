package com.bsc.intg.svcs.core;

import org.apache.commons.pool2.PooledObjectFactory;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;

public class BSCServicePool extends GenericObjectPool<BSCService>{

 public BSCServicePool(PooledObjectFactory<BSCService> factory) {
        super(factory);
    }
    public BSCServicePool(PooledObjectFactory<BSCService> factory , GenericObjectPoolConfig config, BSCServiceContainer serviceContainer, String serviceName) {
        super(factory, config);
        
        BSCServiceContainerBase containerBase= ((BSCServiceContainerBase)serviceContainer);
       
        try {
        	containerBase.getLogger().info("Preparing the service pool  {}", serviceName );
			this.preparePool();
			containerBase.getLogger().info("Service pool {} is prepared successfully ", serviceName );
		} catch (Exception e) {
			containerBase.getLogger().error("Error occured initializing service pool {}", serviceName );
			e.printStackTrace();
		}
    }
}

