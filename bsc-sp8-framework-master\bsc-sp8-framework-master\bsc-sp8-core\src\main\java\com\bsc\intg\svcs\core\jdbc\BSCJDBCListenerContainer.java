package com.bsc.intg.svcs.core.jdbc;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;

import javax.transaction.Transactional;

import org.json.JSONArray;
import org.slf4j.Logger;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.PreparedStatementSetter;

import com.bsc.intg.svcs.core.boot.BSCScheduler;
import com.bsc.intg.svcs.core.boot.BSCSchedulerRuntime;
import com.bsc.intg.svcs.core.boot.BSCSchedulerTask;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.locking.LockService;
import com.bsc.intg.svcs.core.locking.LockServiceFactory;
import com.bsc.intg.svcs.core.locking.ServiceLock;
import com.bsc.intg.svcs.core.locking.ServiceLockException;

public class BSCJDBCListenerContainer implements BSCSchedulerTask {
	private Logger logger = null;
	private boolean running, outMsgPerRow, lock = false;
	private BSCJDBCListener jdbcListener;
	private JdbcTemplate jdbcTemplate;
	protected BSCScheduler scheduler = null;
	protected LockService lockService = null;
	protected ServiceLock svcsLock = null;
	private String cron, outMsgFormat, datacolumn, queryCommand, inProgressCommand, commitCommand, rollbackCommand, keycolumn,
			delimiter, lockDir = null;
	private int lockTimeOut = 0;
	private List<String> pstValues = null;
	private List<String> skipcolumns = null;

	public void initialize() throws Throwable {
		logger.info("Initializing JDBC SQL Container");

		BSCSchedulerRuntime sr = ((BSCSchedulerRuntime) jdbcListener.getServiceContainer().getApplicationService()
				.getApplicationServiceConfig()
				.getRuntimeService(jdbcListener.getProperty("scheduler.run.time", "quartz")));

		scheduler = sr.getScheduler();

		scheduler.scheduleTask(jdbcListener.getConfigName(), jdbcListener.getServiceContainer().getConfigName(), cron,
				this, logger);

		if (isLock()) {
			initializeLock();
		}

		logger.debug("Intialization complete");

	}

	@Override
	public void process() {

		try {
			if (isLock()) {
				logger.info("Accquiring lock before processing");
				lock();
			}

			List<Map<String, Object>> queryResults = null;
			if (queryCommand.contains("?")) {
				queryResults = jdbcTemplate.queryForList(queryCommand, new PreparedStatementSetter() {
					int i = 1;

					@Override
					public void setValues(PreparedStatement pst) throws SQLException {

						pstValues.forEach((param) -> {
							try {
								pst.setObject(i, param);
								i++;
							} catch (SQLException e) {
								throw new RuntimeException(e);
							}

						});

					}

				});
			} else {
				queryResults = jdbcTemplate.queryForList(queryCommand);
			}
			if (!queryResults.isEmpty()) {
				if (outMsgPerRow) {
					// Process each row as individual message
					for (Map<String, Object> queryResult : queryResults) {
						String row = processRow(queryResult);
						processMessage(row, queryResult);
					}
				} else {
					// Process all rows as one message
					String msg = processAllRows(queryResults);
					processMessage(msg, null);
				}
			}else{
				logger.info("queryResults is empty. No records found in database");				
			}
		} catch (DataAccessException dae) {
			logger.error("Failure occurred in reading from database", dae);
			this.setRunning(false);
			throw new RuntimeException(dae);
		} catch (Throwable e) {
			logger.error("Failure occurred in read cycle", e);
			this.setRunning(false);
			throw new RuntimeException(e);

		} finally {
			if (isLock()) {
				try {
					logger.info("Releasing lock after processing is done");
					unlock();
				} catch (Exception e) {
					logger.error("Error releasing lock. ", e);
					e.printStackTrace();
				}
			}
		}
	}

	@Transactional
	private void processMessage(String message, Map<String, Object> queryResult) throws BSCServiceException {
		executeInProgressCommand(queryResult);
		try {
			jdbcListener.onMessage(message);
			executeCommitCommand(queryResult);
		} catch (BSCServiceException e) {
			if (e.getLevel() == BSCErrorLevel.ROLLBACK) {
				logger.error("Rolling back the message", e);
				executeRollbackCommand(queryResult);
				throw e;
			}
		}
	}

	private String processAllRows(List<Map<String, Object>> queryResults) {
		switch (outMsgFormat) {
		case "json":
			JSONArray jsonArr = new JSONArray();
			queryResults.forEach(value -> jsonArr.put(BSCJDBCUtils.mapJDBCMapResponseToJSON(value, skipcolumns)));
			return jsonArr.toString(4);
		case "xml":
			StringBuffer xmlMsg = new StringBuffer();
			xmlMsg.append("<ResultSet>");
			queryResults.forEach(value -> xmlMsg
					.append("<Row>" + BSCJDBCUtils.mapJDBCMapResponseToXML(value, skipcolumns) + "</Row>"));
			xmlMsg.append("</ResultSet>");
			return xmlMsg.toString();
		case "csv":
			StringJoiner csvMsg = new StringJoiner(System.lineSeparator());
			csvMsg.add(BSCJDBCUtils.colNamesToDelimFileHeader(queryResults.get(0).keySet(), delimiter, skipcolumns));
			if(queryResults !=null && queryResults.size() > 0)
				queryResults.forEach(
					value -> csvMsg.add(BSCJDBCUtils.mapJDBCMapResponseToDelimtedFile(value, delimiter, skipcolumns)));
			return csvMsg.toString();
		default:
			JSONArray jsonArr1 = new JSONArray();
			queryResults.forEach(value -> jsonArr1.put(BSCJDBCUtils.mapJDBCMapResponseToJSON(value, skipcolumns)));
			return jsonArr1.toString(4);
		}
	}

	private void executeRollbackCommand(Map<String, Object> queryResult) {
		if (rollbackCommand != null) {
			if (rollbackCommand.contains("?") && queryResult != null) {
				jdbcTemplate.update(rollbackCommand, queryResult.get(keycolumn));
			} else {
				jdbcTemplate.update(rollbackCommand);
			}
		}
	}

	private void executeCommitCommand(Map<String, Object> queryResult) {
		if (commitCommand != null) {
			if (commitCommand.contains("?") && queryResult != null) {
				jdbcTemplate.update(commitCommand, queryResult.get(keycolumn));
			} else {
				jdbcTemplate.update(commitCommand);
			}
		}
	}

	private void executeInProgressCommand(Map<String, Object> queryResult) {
		if (inProgressCommand != null) {
			if (inProgressCommand.contains("?") && queryResult != null) {
				jdbcTemplate.update(inProgressCommand, queryResult.get(keycolumn));
			} else {
				jdbcTemplate.update(inProgressCommand);
			}
		}
	}

	private String processRow(Map<String, Object> row) {

		switch (outMsgFormat) {
		case "json":
			return BSCJDBCUtils.mapJDBCMapResponseToJSON(row, skipcolumns);
		case "xml":
			return BSCJDBCUtils.mapJDBCMapResponseToXML(row, skipcolumns);
		case "csv":
			StringJoiner joiner = new StringJoiner(System.lineSeparator());
			joiner.add(BSCJDBCUtils.colNamesToDelimFileHeader(row.keySet(), delimiter, skipcolumns));
			joiner.add(BSCJDBCUtils.mapJDBCMapResponseToDelimtedFile(row, delimiter, skipcolumns));
			return joiner.toString();
		case "none":
			return (String) row.get(datacolumn);
		default:
			return BSCJDBCUtils.mapJDBCMapResponseToJSON(row, skipcolumns);
		}
	}

	@Override
	public void setRunning(boolean running) {
		this.running = running;

	}

	@Override
	public boolean isRunning() {
		return running;

	}

	public BSCJDBCListenerContainer(Logger logger) {
		this.logger = logger;
	}

	public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
		this.jdbcTemplate = jdbcTemplate;
	}

	public void setCron(String cron) {
		this.cron = cron;
	}

	public void setJdbcSqlListener(BSCJDBCListener jdbcSqlListener) {
		this.jdbcListener = jdbcSqlListener;
	}

	public void setDatacolumn(String datacolumn) {
		this.datacolumn = datacolumn;
	}

	public void setSkipcolumns(List<String> skipcolumns) {
		this.skipcolumns = skipcolumns;
	}

	public void setQueryCommand(String query) {
		this.queryCommand = query;
	}

	public void setKeycolumn(String keycolumn) {
		this.keycolumn = keycolumn;
	}

	public void setDelimiter(String delimiter) {
		this.delimiter = delimiter;
	}

	public boolean isLock() {
		return lock;
	}

	protected void lock() throws ServiceLockException {

		logger.debug(" Locking");

		svcsLock = lockService.acquire(lockTimeOut, this.getLockDir());

		logger.debug(" Locked at  {}", svcsLock.toString());
	}

	protected boolean unlock() throws Exception {

		if (svcsLock != null) {

			logger.debug("Removing lock:  {}", svcsLock.toString());

			lockService.release(svcsLock);

			logger.debug(" Unlocked");

			svcsLock = null;

			return true;

		} else {
			return false;
		}

	}

	/**
	 * initializes the lock directory.
	 * 
	 */
	protected void initializeLock() throws Throwable {

		lockService = LockServiceFactory.getDefaultLockService();

	}

	public String getLockDir() {
		return lockDir;
	}

	public void setLock(boolean lockRequired) {
		this.lock = lockRequired;

	}

	public void setLockTimeOut(int lockTimeOut) {
		this.lockTimeOut = lockTimeOut;
	}

	public void setLockDir(String lockDir) {
		this.lockDir = lockDir;
	}

	public void setPstValues(List<String> pstValues) {
		this.pstValues = pstValues;
	}

	public void setOutMsgPerRow(boolean outMsgPerRow) {
		this.outMsgPerRow = outMsgPerRow;
	}

	public void setOutMsgFormat(String outMsgFormat) {
		this.outMsgFormat = outMsgFormat;
	}

	public void setInProgressCommand(String inProgressCommand) {
		this.inProgressCommand = inProgressCommand;
	}

	public void setCommitCommand(String commitCommand) {
		this.commitCommand = commitCommand;
	}

	public void setRollbackCommand(String rollbackCommand) {
		this.rollbackCommand = rollbackCommand;
	}

}
