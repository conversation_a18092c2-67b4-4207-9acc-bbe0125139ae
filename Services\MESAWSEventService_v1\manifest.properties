service.name=MESAWSEventService_v1
service.bundle=../EventService_v1/EventService_v1-8.0.0.jar
service.context.uri=/MESAWSEventService/v1
service.port=true
service.args=--bsc.config.name=EventService_v1
runtime.args=-Xmx4096m -Dbsc.service.ext.js.common=config/EventService_v1/etc -Dcom.atomikos.icatch.no_file=true -Dcom.atomikos.icatch.service=com.atomikos.icatch.standalone.UserTransactionServiceFactory -Dcom.atomikos.icatch.output_dir=logs/MESAWSEventService_v1.1/ -Dcom.atomikos.icatch.log_base_dir=logs/MESAWSEventService_v1.1/ -Dcom.atomikos.icatch.max_timeout=600000 -Dcom.atomikos.icatch.default_jta_timeout=600000
service.platform=SP
service.target=AWS/FGW
service.groups=CORP
service.framework=sp8
service.context.uri.override=true
service.pool.size=10

JMSMGW.connection.name=MES

JMSEVENT.connection.name=MES

JMSMGWXA.connection.name=MES

JMSMI50GW.cached=true
JMSMI50GW.connection.name=MI50
JMSMI50GW.class=com.bsc.intg.svcs.core.jms.BSCJMSConnectionResource

JMSD525GW.cached=true
JMSD525GW.connection.name=D525
JMSD525GW.class=com.bsc.intg.svcs.core.jms.BSCJMSConnectionResource

EventService.resources=JMSMGW,JMSEVENT,JMSMGWXA,JMSMI50GW,JMSD525GW

EventServiceContainer.listeners=MGW_BSEE_Listener,MGW_BEE_Listener,MGW_MSE_Listener,MI50_BSEE_Listener,MI50_BEE_Listener,MI50_MSE_Listener,D525_BSEE_Listener,D525_BEE_Listener,D525_MSE_Listener
EventServiceProcessContainer.listeners=EventMdataListener,EventPayloadListener,EventNotifyListener,EventMdataErrorListener,EventMESMdataListener,EventMESPayloadListener,EventESMdataListener,EventMESESMdataListener


MGW_MSE_Listener.destination.name=MES.SERVICE.EVENT

MI50_BSEE_Listener.class=com.bsc.intg.svcs.core.jms.BSCJMSListener
MI50_BSEE_Listener.max.threads=5
MI50_BSEE_Listener.connection=JMSMI50GW
MI50_BSEE_Listener.destination.name=BSC.SERVICE.EVENT.ERROR
MI50_BSEE_Listener.services=EventCommonService
MI50_BSEE_Listener.selector=EVENT_Type = 'Error'

MI50_BEE_Listener.class=com.bsc.intg.svcs.core.jms.BSCJMSListener
MI50_BEE_Listener.max.threads=5
MI50_BEE_Listener.connection=JMSMI50GW
MI50_BEE_Listener.destination.name=BSC.ERROR.EVENT.1
MI50_BEE_Listener.services=EventCommonService

MI50_MSE_Listener.class=com.bsc.intg.svcs.core.jms.BSCJMSListener
MI50_MSE_Listener.max.threads=5
MI50_MSE_Listener.connection=JMSMI50GW
MI50_MSE_Listener.destination.name=MI50.SERVICE.EVENT
MI50_MSE_Listener.services=EventCommonService

D525_BSEE_Listener.class=com.bsc.intg.svcs.core.jms.BSCJMSListener
D525_BSEE_Listener.max.threads=5
D525_BSEE_Listener.connection=JMSD525GW
D525_BSEE_Listener.destination.name=BSC.SERVICE.EVENT.ERROR
D525_BSEE_Listener.services=EventCommonService
D525_BSEE_Listener.selector=EVENT_Type = 'Error'

D525_BEE_Listener.class=com.bsc.intg.svcs.core.jms.BSCJMSListener
D525_BEE_Listener.max.threads=5
D525_BEE_Listener.connection=JMSD525GW
D525_BEE_Listener.destination.name=BSC.ERROR.EVENT.1
D525_BEE_Listener.services=EventCommonService

D525_MSE_Listener.class=com.bsc.intg.svcs.core.jms.BSCJMSListener
D525_MSE_Listener.max.threads=5
D525_MSE_Listener.connection=JMSD525GW
D525_MSE_Listener.destination.name=D525.SERVICE.EVENT
D525_MSE_Listener.services=EventCommonService

profile.dev.ReplayDestination.http.url=https://dev-int-aws-igw4.bsci.bossci.com/api/MESAWSReplayService/v1/event
profile.val.ReplayDestination.http.url=https://val-int-aws-services.bsci.bossci.com/api/MESAWSReplayService/v1/event
profile.stg.ReplayDestination.http.url=https://stg-int-aws-services.bsci.bossci.com/api/MESAWSReplayService/v1/event
profile.prd.ReplayDestination.http.url=https://prd-int-aws-services.bsci.bossci.com/api/MESAWSReplayService/v1/event
profile.tst.ReplayDestination.http.url=https://tst-int-aws-services.bsci.bossci.com/api/MESAWSReplayService/v1/event

profile.val.FetchPayloadService.default.allowed.groups=eai_val_usrs,eai_val_admn,eai_val_rmgt,ei_t_h_s4,ei_t_h_plm,ei_t_h_mi50,ei_t_h_m590
profile.stg.FetchPayloadService.default.allowed.groups=eai_stg_usrs,eai_stg_admn,eai_stg_rmgt,ei_t_h_s4,ei_t_h_plm,ei_t_h_mi50,ei_t_h_d525
profile.prd.FetchPayloadService.default.allowed.groups=eai_prd_usrs,eai_prd_admn,eai_prd_rmgt,ei_p_h_s4,ei_p_h_plm,ei_p_h_mi50,ei_p_h_d525

EventAWSESDispatcherService.es.batch.size=100

MESESGETDestionation.get.timeout=-1
CommonESGETDestionation.get.timeout=-1

profile.tst.EventService.aws.access.key=ss:///edh_eai_test_admn.access.key
profile.tst.EventService.aws.secret.key=ss:///edh_eai_test_admn.secret.key

profile.tst.AWSESClientService.endpoint=https://vpc-eai-tst-eventhub-2wols6fbtwjncchqfpuygswcc4.us-east-1.es.amazonaws.com

profile.tst.EventServiceResubmitController.pre.script={SP("es.latest.event.index",message.getProperty("HTTP.latest_index","bsc_eai_tst_latest_events_alias"));}

profile.tst.EventAWSESDispatcherService.es.all.event.index=bsc_eai_tst_all_events_alias
profile.tst.EventAWSESDispatcherService.es.all.event.search.index=bsc_eai_tst_all_events_alias
profile.tst.EventAWSESDispatcherService.es.latest.event.index=bsc_eai_tst_latest_events_alias

profile.tst.EventAWSESSearchService.es.latest.event.index=bsc_eai_tst_latest_events_alias

profile.tst.FetchPayloadService.bucket=bsc.ei.payloads.tst
profile.tst.FetchPayloadService.role.arn.prefix=arn:aws:iam::920604188468:role/
profile.tst.FetchPayloadService.default.iam.role=ei_t_s_s3_fullacces
profile.tst.FetchPayloadService.default.allowed.groups=eai_stg_usrs,eai_stg_admn,eai_stg_rmgt,ei_t_h_s4,ei_t_h_plm,ei_t_h_m590,ei_t_h_d525,ei_t_h_mi50
profile.tst.FetchPayloadService.aws.access.key=ss:///ei_t_s_s3.access.key
profile.tst.FetchPayloadService.aws.secret.key=ss:///ei_t_s_s3.secret.key

profile.tst.EIS3EventPublish.s3.access.key=ss:///ei_t_s_s3.access.key
profile.tst.EIS3EventPublish.s3.secret.key=ss:///ei_t_s_s3.secret.key

profile.tst.EIS3ExtPayloadArchive.s3.access.key=ss:///ei_t_s_s3.access.key
profile.tst.EIS3ExtPayloadArchive.s3.secret.key=ss:///ei_t_s_s3.secret.key