package com.bsc.intg.svcs.core.vfs;



public class BSCVFSObjectInfo implements Comparable<BSCVFSObjectInfo>{ 
	
	
	// Name of the file 
	private final String name; 
	
	private final String fileName; 
	
	private final String url;
	
	// Volume path of the parent directory hosting the file 
	private final String parentPath; 
	
	// Whether the file is a directory 
	private final boolean directory; 
	
	// Last modification time of the file 
	private final long lastModified; 
	
	// Raw size of the underlying file 
	private final long size; 
	
	// Whether the file is readable 
	private final boolean readable; 
	
	// Whether the file is writable 
	private final boolean writable; 
	
	// Whether the file is executable 
	private final boolean executable; 

	/**
	 * Create a new EncFSFileInfo 
	 *  
	 * @param name 
	 *            Name of the file 
	 * @param parentPath 
	 *            Volume path of the parent directory hosting the file 
	 * @param directory 
	 *            Whether the file is a directory 
	 * @param lastModified 
	 *            Last modification time of the file 
	 * @param size 
	 *            Raw size of the underlying file 
	 * @param readable 
	 *            Whether the file is readable 
	 * @param writable 
	 *            Whether the file is writable 
	 * @param executable 
	 *            Whether the file is executable 
	 */ 
	public BSCVFSObjectInfo(String name, String parentPath, String url, boolean directory, long lastModified, long size, boolean readable, boolean writable,   boolean executable, String fileName) { 
	
	 this.name = name; 
	 this.parentPath = parentPath; 
	 this.directory = directory; 
	 this.lastModified = lastModified; 
	 this.size = size; 
	 this.readable = readable; 
	 this.writable = writable; 
	 this.executable = executable; 
	 this.url		= url;
	 this.fileName 	= fileName;
	 
	} 
	
	/**
	 * Returns the name of the file 
	 *  
	 * @return name of the file 
	 */ 
	public String getName() { 
	 return name; 
	} 
	
	public String getFileName() {
		return fileName;
	}

	/**
	 * Returns the volume path of the parent directory hosting the file 
	 *  
	 * @return complete url of the  directory hosting the file 
	 */ 
	public String getParentPath() { 
	 return parentPath; 
	} 

	/**
	 * Returns the name of the file 
	 *  
	 * @return name of the file 
	 */ 
	public String getURL() { 
	 return url; 
	} 
	
	/**
	 * Returns the volume path of the file 
	 *  
	 * @return volume path of the file 
	 */ 
	public String getPath() { 
	 String result=""; 
	
	
	 return result; 
	} 
	
	/**
	 * Returns the last modification time of the file 
	 *  
	 * @return last modification time of the file 
	 */ 
	public Long getLastModified() { 
	 return lastModified; 
	} 
	
	/**
	 * Returns the raw size of the underlying file 
	 *  
	 * @return raw size of the underlying file 
	 */ 
	public long getSize() { 
	 return size; 
	} 
	
	/**
	 * Returns whether the file is a directory 
	 *  
	 * @return whether the file is a directory 
	 */ 
	public boolean isDirectory() { 
	 return directory; 
	} 
	
	/**
	 * Returns whether the file is readable 
	 *  
	 * @return whether the file is readable 
	 */ 
	public boolean isReadable() { 
	 return readable; 
	} 
	
	/**
	 * Returns whether the file is writable 
	 *  
	 * @return whether the file is writable 
	 */ 
	public boolean isWritable() { 
	 return writable; 
	} 
	
	/**
	 * Returns whether the file is executable 
	 *  
	 * @return whether the file is executable 
	 */ 
	public boolean isExecutable() { 
	 return executable; 
	}

	@Override
	public int compareTo(BSCVFSObjectInfo o) {
		return  this.getLastModified().compareTo(o.getLastModified());
	} 	

}