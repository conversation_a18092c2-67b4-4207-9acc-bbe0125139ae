<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds">


	<statusListener class="ch.qos.logback.core.status.OnConsoleStatusListener" />
	
	<include resource="org/springframework/boot/logging/logback/defaults.xml"/>

	<springProperty scope="context" name="bsc.host.name" source="bsc.host.name"/>
	<springProperty scope="context" name="bsc.service.name" source="bsc.service.name"/>
	<springProperty scope="context" name="bsc.instance.name" source="bsc.instance.name"/>
	<springProperty scope="context" name="bsc.config.name" source="bsc.config.name"/>

	<springProperty scope="context" name="bsc.email.smtphost" source="bsc.email.smtphost"/>
	<springProperty scope="context" name="bsc.email.from" source="bsc.email.from"/>
	<springProperty scope="context" name="bsc.email.to" source="bsc.email.to"/>


	<springProperty scope="context" name="log.root" source="log.root"/>


	<property name="APP_LOG_FILE" value="${log.root}/${bsc.service.name}.log"/>

	<property name="PLATFORM_LOG_FILE" value="${log.root}/boot.log"/>

	<!-- You can override this to have a custom pattern -->
	<property name="CONSOLE_LOG_PATTERN"
			  value="%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>
	
	<property name="APP_LOG_PATTERN"   value="%d{yyyy-MM-dd HH:mm:ss.SSS,America/Chicago} [%thread] %-5level %-40.40logger{39} - %msg%n"/>
	
	<appender name="EMAIL_NOTIFICATION" class="ch.qos.logback.classic.net.SMTPAppender">
		
		<smtpHost>${bsc.email.smtphost}</smtpHost>
		
		<from>${bsc.email.from}</from>
		
		<to>${bsc.email.to}</to>
		
		<subject>${bsc.host.name}/${bsc.instance.name} : %logger{20} - %m</subject>
		
		<layout class="ch.qos.logback.classic.html.HTMLLayout">
			<pattern>%date%level%logger{24}%m</pattern>
		</layout>
		
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>ERROR</level>
		</filter>


		<evaluator class="ch.qos.logback.classic.boolex.JaninoEventEvaluator">
			<expression>
				<![CDATA[ marker != null  && marker.contains("SMTP_TRIGGER") ]]>
			</expression>
		</evaluator>

	</appender>


	<!-- Appender to log to console -->
	<appender name="CONSOLE_LOG" class="ch.qos.logback.core.ConsoleAppender">
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<!-- Minimum logging level to be presented in the console logs-->
			<level>DEBUG</level>
		</filter>
		<encoder>
			<pattern>${CONSOLE_LOG_PATTERN}</pattern>
			<charset>utf8</charset>
		</encoder>
	</appender>

	<!-- Appender to log to file -->
	<appender name="PLATFORM_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${PLATFORM_LOG_FILE}</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${PLATFORM_LOG_FILE}.%d{yyyy-MM-dd}.gz</fileNamePattern>
			<maxHistory>1</maxHistory>
			<totalSizeCap>4MB</totalSizeCap>
		</rollingPolicy>
		<encoder>
			<pattern>${APP_LOG_PATTERN}</pattern>
			<charset>utf8</charset>
		</encoder>
	</appender>

	<appender name="APP_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${APP_LOG_FILE}</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${APP_LOG_FILE}.%d{yyyy-MM-dd}.gz</fileNamePattern>
			<maxHistory>1</maxHistory>
			<totalSizeCap>4MB</totalSizeCap>
		</rollingPolicy>
		<encoder>
			<pattern>${APP_LOG_PATTERN}</pattern>
			<charset>utf8</charset>
		</encoder>
	</appender>

	<logger name="${bsc.config.name}" level="INFO" additivity="false">
		<appender-ref ref="APP_LOG" />
		<appender-ref ref="EMAIL_NOTIFICATION" />
	</logger>

	<root level="INFO">
		<appender-ref ref="PLATFORM_LOG"/>
	</root>

</configuration>
