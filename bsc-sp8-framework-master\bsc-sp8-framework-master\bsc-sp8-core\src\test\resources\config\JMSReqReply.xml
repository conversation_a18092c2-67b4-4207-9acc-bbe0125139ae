<?xml version="1.0" encoding="UTF-8"?>
<!-- Property of Boston Scientific Corporation - Confidential -->

<adapterConfig name="JMSTest" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"  timezone="EST">

	<adapter name="JMSTest">
		
		<logger class="Logger">

			<method name="setLogFileName" value="%ADAPTER%.log"/>
			<method name="setLogSize" value="4096"/>
			<method name="setFailedName" value="failedMsgs/%ADAPTER%/%NAME%_%HOST%_%TIMESTAMP%_%SEQUENCE.|S,-3|%"/>
			<method name="setFileType" value="txt"/>
			<method name="setMailLogStatus" value="true"/>
			<method name="setMailAdapterStatus" value="false"/>
			<method name="setStatusImportant" value="false"/>
			<method name="setMailMaxMsgSize" value="1024"/>
			<method name="setLogStdout" value="true"/>

		</logger>

		<reader class="JmsQReader">

			<method name="setContextFactory" value="com.sun.jndi.fscontext.RefFSContextFactory"/>
			<method name="setProviderURL" value="file:///var/jms/tst_etl"/>		
			<method name="setConnectionFactory" value="${connectionFactory}"/>
			<method name="setQueueName" value="EAI.MDM"/>
			<method name="setConnectRetryCycle" value="60"/> 
			<method name="setConnectRetryReport" value="15"/> 

		</reader>
	      
	    <writer class="FileSystemWriter">
	    
			<method name="setFileType" value="xml"/>
			<method name="setName" value="/eaiarch/FileOutput/JMSTest/%NAME%"/>
			<method name="setTransactional" value="true"/> 
			<method name="setBackup" value="true"/>
           <method name="setOverwrite" value="true"/> 
           
    	</writer>
    	
	</adapter>
	
</adapterConfig>