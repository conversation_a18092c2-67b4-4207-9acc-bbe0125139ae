package com.bsc.intg.svcs.core.rest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriInfo;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentSubType;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCControllerBase;
import com.bsc.intg.svcs.core.BSCService;
import com.bsc.intg.svcs.core.boot.BSCRESTRuntime;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.message.BSCMessageModel;
import com.bsc.intg.svcs.core.message.BSCModel;
import com.bsc.intg.svcs.core.message.BSCObjectMessageModel;
import com.bsc.intg.svcs.core.util.BSCStringUtils;

public abstract class BSCRESTControllerBase extends BSCControllerBase {

	@Context
	UriInfo uriInfo;
	
	@Context 
	HttpHeaders httpHeaders;
	
	@Context
	HttpServletRequest httpServletReq;
	
	
	protected boolean preScriptEnabled 		= false; 
	protected boolean postScriptEnabled		= false;
	protected boolean errorScriptEnabled	= false;

	protected String 	preScript				= null;
	protected String 	postScript				= null;
	protected String 	errorScript				= null;

	
	boolean scriptServiceEnabled 	= false;
	

	protected String 	scriptServiceName		= null;
	protected String	eventServiceName		= null;
	
	
	protected boolean 	isStopped  				= true;
	
	protected String	controllerRuntime		= null;
	
	protected String 	errorEventName			= null;
	
	public BSCRESTControllerBase(BSCComponentType componentType ,BSCComponent parentComponent, String configName) {
		super(componentType, parentComponent, configName);
	}
	
	@Override
	public void initialize() throws BSCComponentInitializationException {
		
		super.initialize(BSCComponentSubType.REST_CONTROLLER);
		
		preScript				=   getProperty("pre.script","");
		postScript				=   getProperty("post.script","");
		errorScript				=   getProperty("error.script","");
		
		scriptServiceName		=  this.getServiceContainer().getProperty("script.service.name","");
		eventServiceName		=  this.getServiceContainer().getNonEmptyProperty("event.service.name");
	
		if(!scriptServiceName.isEmpty()) 	scriptServiceEnabled=true;
		if(!preScript.isEmpty()) 			preScriptEnabled=true;
		if(!postScript.isEmpty()) 			postScriptEnabled=true;
		if(!errorScript.isEmpty()) 			errorScriptEnabled=true;
		
		controllerRuntime	=	getProperty("run.time", "jersy");
		
		errorEventName 		=   this.getConfigName() + ".Event.Error";
		
		BSCRESTRuntime rr = ((BSCRESTRuntime)this.getServiceContainer().getApplicationService().getApplicationServiceConfig().getRuntimeService(this.controllerRuntime));
		
		if(rr !=null ) {
			
			logger.info("Registering REST controller {}", this.getConfigName());
			
			try {
				rr.registerRESTEndpoint(this);
			} catch (Exception e) {
				throw new BSCComponentInitializationException(this, e, BSCErrorLevel.CRITICAL, "Failed to register endpoint for " + this.getConfigName());
			}
		}	
			

	}
	
	protected Response dispatchRequest(String serviceName, String request, String responseMediaType) {
		
		
		if (!isStopped) {
			
			BSCMessage message=null;
			
			try {
	
				if(!scriptServiceEnabled) {
					scriptServiceName=null;
				}
	
				message = new BSCMessage(this,eventServiceName,scriptServiceName );
				
				message.addProperties(this.getProperties(), true, this.getConfigName());
				
				logger.info("Creating the message for the service call");
				message.loadMessage(request);
				message.copyHTTPPropertiesFrom(uriInfo, httpHeaders, (Object[]) null);
				message.setProperty("destination.route", this.getProperty("destination.route",""));
				message.setProperty("HTTP_REQUEST_METHOD", httpServletReq.getMethod());
				
				
				logger.info("Executing the pre script");
				if (preScriptEnabled) message.executeScript(preScript);
				

				Map<String, String> controllerRouteMap 	= new HashMap<String, String>();
				
				String type = this.getProperty("service.route","");
				String serviceRouteProp = this.getProperty("service.route.map"," : ");
				
				if (controllerRouteMap.isEmpty()){
					
					String[] serviceList = serviceRouteProp.split(";");
					for(String sericeObject: serviceList){
						String key = sericeObject.split("\\:")[0];
						String service = sericeObject.split("\\:")[1];
						controllerRouteMap.put(key, service);
					}
				}
				if(controllerRouteMap.get(type)!=null && !controllerRouteMap.get(type).isEmpty())
					serviceName = controllerRouteMap.get(type);
	
				BSCService service = null;
				BSCMessage responseMessage = null;
				try {
					
					service = this.getServiceContainer().getService(serviceName);
					
					responseMessage = service.execute(message);
					
					return Response.ok(responseMessage.getStringMessage(), responseMediaType).build();
				
				} catch (BSCServiceException e) {
					
					try {
						if( errorScriptEnabled && message != null ) {
							logger.info("Executing the error script");
							message.executeScript(errorScript);
						}	
					} catch (BSCServiceException ee) {
						logger.error("Failed to execute error script",ee);
					}
					
					message.generateEvent(this.getConfigName() + ".Event.Error");
					
					if(responseMediaType.equalsIgnoreCase(MediaType.TEXT_PLAIN))
						return Response.ok(e.getErrorString(), responseMediaType).build();
					
					return Response.ok(e.getErrorObject(), responseMediaType).build();
				} finally {
					
					if(service != null) {
						try {
							 this.getServiceContainer().releaseService(serviceName, service);
						} catch (Exception e) {
							return Response.ok("Failed to release service to the pool", responseMediaType).build();
						}
					}
					
					
					
					if (postScriptEnabled && responseMessage != null){
						logger.info("Executing the post script");
						responseMessage.executeScript(postScript);
					}
					
					if (responseMessage != null ) {
						responseMessage.close();
					}
				}
					
			} catch (BSCServiceException | BSCMessageException e ) {
					
					try {
						if( errorScriptEnabled && message != null ) {
							logger.info("Executing the error script");
							message.executeScript(errorScript);
						}	
					} catch (BSCServiceException ee) {
						logger.error("Failed to execute error script",ee);
					}
					
					message.generateEvent(this.errorEventName);
					
					return Response.status(Response.Status.INTERNAL_SERVER_ERROR).type(responseMediaType)	.entity(e.getMessage()).build();
					
			} finally {
				
				if(message != null) {
					message.close();
				}
			}
	
		} else {
			return Response.serverError().build();
		}
		
	}
	
	
	protected BSCModel dispatchRequest(String serviceName, BSCModel request, BSCModel response) throws Exception {
		
		
		if (!isStopped) {
			
		
			BSCService scriptService = null;
			BSCService eventService = null;
			BSCMessage message=null;
			
			try {
	
				if(!scriptServiceEnabled) {
					scriptServiceName=null;
				}
				
				
				message = new BSCMessage(this, eventServiceName, scriptServiceName);
				
				message.addProperties(this.getProperties(), true, this.getConfigName());
				
				logger.info("Creating the message for the service call");
				
				BSCMessageModel mm = new BSCObjectMessageModel();
				mm.addModel("rest.request",request.getClass(), request, null);
				mm.addModel("rest.response",response.getClass(), response, null);
				message.loadMessage(mm);
				
				
				logger.info("Executing the pre script");
				if (preScriptEnabled) message.executeScript(preScript);
				
	
				BSCService service = null;
				BSCMessage responseMessage = null;
				try {
					
					service = this.getServiceContainer().getService(serviceName);
					
					responseMessage = service.execute(message);
					
					return (BSCModel) responseMessage.getMessageModel().getModel("rest.response").getObject();
				
				} catch (BSCServiceException e) {
					
					try {
						if( errorScriptEnabled && message != null ) {
							logger.info("Executing the error script");
							message.executeScript(errorScript);
						}	
					} catch (BSCServiceException ee) {
						logger.error("Failed to execute error script",ee);
					}
					
					message.generateEvent(this.getConfigName() + ".Event.Error");
					
					
					throw new Exception("Failed to execute backend service " + e.getErrorString());
					
				} finally {
					
					if(service != null) {
						try {
							 this.getServiceContainer().releaseService(serviceName, service);
						} catch (Exception e) {
							throw new Exception("Failed to execute backend service " + e.toString());
						}
					}
					
					
					if (postScriptEnabled && responseMessage != null){
						logger.info("Executing the post script");
						responseMessage.executeScript(postScript);
					}
					
					if (responseMessage != null ) {
						responseMessage.close();
					}
				}
					
			} catch (BSCServiceException | BSCMessageException e ) {
					
					try {
						if( errorScriptEnabled && message != null ) {
							logger.info("Executing the error script");
							message.executeScript(errorScript);
						}	
					} catch (BSCServiceException ee) {
						logger.error("Failed to execute error script",ee);
					}
					
					message.generateEvent(this.errorEventName);
					
					throw new Exception("Failed to execute backend service " + e.getErrorString());
					
			} finally {
				
				
				if(message != null) {
					message.close();
				}
			}
	
		} else {
			throw new Exception("Internal server error ");
		}
		
	}



	@Override
	public void stop() {
		if (!isStopped)
			this.isStopped=true;
	}

	@Override
	public void start() {
		
		if ( isStopped ) {
				isStopped=false;
		}	
		
	}
	

}
