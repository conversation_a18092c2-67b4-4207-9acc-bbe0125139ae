package com.bsc.intg.svcs.core.vfs;

import org.quartz.Scheduler;
import org.quartz.SchedulerFactory;
import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentSubType;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCListenerBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCCryptoHelper;
import com.bsc.intg.svcs.core.util.BSCPropertyHelper;
import com.bsc.intg.svcs.core.util.BSCVFSHelper;



public class BS<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>  extends BSCListenerBase {

	BSCVFSListenerContainer fslc=null;
	Scheduler sched=null;

	protected int 		minThreads					=	1; 
	protected int		maxThreads					=	1;
	private String 		cron 						= null;
	private String 		filePath 					= null;
	private String 		filePattern 				= null;
	private long 		fileAge 					;
	private boolean		includeContent 				= false;
	private boolean		decompress 					= false;
	private boolean 	browse	 					= false;
	private boolean 	recursiveScan	 			= false;
	private String 		protocol 					= null;
	private boolean		lockRequired				= false;
	private String		lockPrefix					= null;
	private String		lockName					= null;
	private int			lockTimeOut					= 0;
	protected int 		maxMessages					=	0; 
	private boolean 	lifo	 					= false;
	private int 		minMessageSize				= 0;
	
	
	public BSCVFSListener(BSCComponentType componentType, BSCComponent parentComponent, String configName) {
		super(componentType, parentComponent, configName);
	}
	
	
	public void initialize() throws BSCComponentInitializationException {
		
		logger.debug("Initializing file listener");
		super.initialize(BSCComponentSubType.VFS_LISTENER);
						
		minThreads   			= getIntProperty("min.threads",1);
		maxThreads				= getIntProperty("max.threads",1);
		cron					= getNonEmptyProperty("schedule");
		filePath				= getNonEmptyProperty("file.uri");
		filePattern				= getNonEmptyProperty("file.pattern");
		fileAge					= getIntProperty("file.age",30000);
		browse					= getBooleanProperty("file.browse", false);
		recursiveScan			= getBooleanProperty("file.recursive.scan", false);
		includeContent			= getBooleanProperty("include.content",false);
		decompress				= getBooleanProperty("decompress",false);
		protocol				= getProperty("protocol", "file");
		lockRequired			= getBooleanProperty("lock.required", false);
		lockPrefix				= getProperty("lock.prefix","/SP8/"+getGlobalProperty("spring.profiles.active").toUpperCase()+"/" + getGlobalProperty("bsc.service.name"));
		lockName				= getProperty("lock.name",this.getConfigName());
		lockTimeOut   			= getIntProperty("lock.timeout",0);
		maxMessages				= getIntProperty("max.messages",0);
		lifo					= getBooleanProperty("file.lifo", false);				
		minMessageSize			= getIntProperty("min.message.size", -1);
		
		try {
			
				logger.info("VFS Listener({}) initializing with properties - min.threads({}), max.threads({}), schedule({}), file.uri({}), file.pattern({}), file.age({}),"
					+ "file.browse({}), file.recursive.scan({}), include.content({}), batch.mode({}), protocol({})"
					+ " lock.required({}), lock.prefix({}), lock.name({}), lock.timeout({}), max.messages({}), file.lifo({}),"
					+ " min.message.size({})",getInstanceName(),minThreads,maxThreads,cron,filePath,filePattern,fileAge,browse,
					recursiveScan,includeContent,protocol,lockRequired,lockPrefix,lockName,lockTimeOut,maxMessages,lifo,minMessageSize);
			
				fslc=new BSCVFSListenerContainer(logger);
				fslc.setFileListener(this);
				fslc.setDecompress(decompress);
				fslc.setCron(cron);
				fslc.setFileAge(fileAge);
				fslc.setFilePath(filePath);
				fslc.setFilePattern(filePattern);
				fslc.setBrowse(browse);
				fslc.setRecursiveScan(recursiveScan);
				fslc.setIncludeContent(includeContent);
				fslc.setProtocol(protocol);
				fslc.setLock(lockRequired);
				fslc.setLockDir(lockPrefix+lockName);
				fslc.setLockTimeOut(lockTimeOut);
				fslc.setMaxMessages(maxMessages);
				fslc.setLifo(lifo);
				fslc.setMinMessageSize(minMessageSize);
				fslc.initialize();
			
				
		} catch ( Throwable  e) {
			throw new BSCComponentInitializationException(this, e, BSCErrorLevel.CRITICAL, "Failed to initialize the listener " + getInstanceName() );
		} 

	}
	
	public void start()  throws BSCComponentInitializationException{

		logger.debug("Starting file listener container");
		try {
			fslc.start();
		} catch (Throwable e) {
			logger.error("Error occured to start VFS file listener container", e);
			throw new BSCComponentInitializationException(this, e, BSCErrorLevel.CRITICAL,
					"Failed to start BSCVFSListenerContainer " + getInstanceName());
		}

	}
	
	public void onMessage(BSCVFSMessage vfsMessage) throws BSCServiceException {

		logger.debug("VFS Listener: On Message");

		BSCMessage message = null;
		int vfsRetryCount = 0;

		try {

			if (!scriptServiceEnabled) {
				scriptServiceName = null;
			}

			try {

				logger.info("Creating the message for the services MessageId - ({}) Timestamp -({})", vfsMessage.getProperty("VFS.MESSAGEID"), vfsMessage.getProperty("VFS.MESSAGETIMESTAMP"));
				message = new BSCMessage(this, eventServiceName, scriptServiceName);
				message.addProperties(this.getProperties(), true, this.getConfigName());
				message.loadMessage(vfsMessage);
				
				String retryCountValue = vfsMessage.getProperty("VFS.RETRY.COUNTER");
				if (retryCountValue != null) {
					try {
						vfsRetryCount = Integer.parseInt(retryCountValue);						
					} catch (NumberFormatException e) {
						logger.warn("Invalid retry count property value from VFS, retry might not wok");
					}
				}

				message.setProperty("CONTEXT.MESSAGE.ID", BSCVFSHelper.getMaskURL(vfsMessage.getProperty("VFS.FILE.URL")) );
				message.setProperty("CONTEXT.MESSAGE.TIME",vfsMessage.getProperty("VFS.PROCESS.TIME") );
				message.setProperty("CONTEXT.MESSAGE.OWNER.NAME", this.configName);
				message.setProperty("CONTEXT.MESSAGE.OWNER.INSTANCE", this.instanceName);
				
				
				BSCPropertyHelper.printMessageContext(message, logger);
				
				
			} catch (BSCMessageException e) {
				logger.error("Failed to load/parse VFS message", e);
				throw new BSCServiceException(this, e, BSCErrorLevel.CONTINUE);

			}
			if (vfsRetryCount > 0)
				message.disableEvent();

			onMessage(message);
			
		} catch (BSCServiceException e) {

			if (e.getLevel() == BSCErrorLevel.ROLLBACK) {
				logger.error("Rolling back the message", e);
				message.setException(e);
				message.generateEvent(this.errorEventName);
				message.clearException();
				throw e;
			} else {
				logger.error("Error occurred while invoking the service, continuing with the next message", e);
				message.setException(e);
				message.generateEvent(this.errorEventName);
				message.clearException();
				throw e;
			}

		} catch (Throwable e) {
			logger.error("Error occurred while invoking the service, continuing with the next message", e);
			message.setException(e);
			message.generateEvent(this.errorEventName);
			message.clearException();
			throw new BSCServiceException(this,e,BSCErrorLevel.ROLLBACK,"Failed to execute the app service");
			
		} finally {

			if (message != null) {
				message.close();
				message = null;
			}

		}

	}
	
	public String getLockDir() {
        return lockPrefix + lockName;
    }


}
