package com.bsc.intg.svcs.core.test;



import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestination;
import com.bsc.intg.svcs.core.BSCIntegrationServiceBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.message.BSCMessageProperties;



public class TestService extends BSCIntegrationServiceBase {


	
	public TestService(BSCComponentType componentType, BSCComponent parentComponent, String configName,
			String instance) {
		super(componentType, parentComponent, configName, instance);
	}



	@Override
	public BSCMessage service(BSCMessage inMessage) throws BSCServiceException {
		
		BSCMessage outMessage = null;
		
		try {

			//Transformation Logic
			
			outMessage = inMessage.cloneMessage(false);
			
			outMessage.loadMessage(inMessage.getStringMessage());
			//Set outgoing message property
			outMessage.setProperty(BSCMessageProperties.JMS_CORRELATION_ID,"COR1000" );
			outMessage.setProperty(BSCMessageProperties.JMS_MESSAGE_ID,"MSG1000" );
			//Get the destination
			BSCDestination publish = this.getDestination("Publish");
			
			//Send it to the destination
			publish.send(outMessage);
		
		} catch ( BSCMessageException | BSCDestinationException e ) {
			throw new BSCServiceException(this,e,BSCErrorLevel.FATAL,"Failed to execute the app service");
		}
		
		return inMessage;
	}

	


	
	@Override
	public void initialize() throws BSCComponentInitializationException {

		super.initialize();
		
		valid=true;
	}

}
