<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<artifactId>bsc-sp8-parent</artifactId>
	<packaging>pom</packaging>
	<name>bsc-sp8-parent</name>
	<description>Parent project POM to manage the dependencies and common project configuration for all the services</description>


  <organization>
    <name>Boston Scientific Corporation.</name>
    <url>http://www.bostonscientific.com</url>
  </organization>
  
  <developers>
    <developer>
      <id>sircilv</id>
      <name>Vinay <PERSON>cilla</name>
      <email><EMAIL></email>
      <organization>Boston Scientific Corporation.</organization>
      <organizationUrl>http://www.bostonscientific.com</organizationUrl>
      <roles>
        <role>Project lead</role>
      </roles>
    </developer>
  </developers>
  
  
	<parent>
		<groupId>com.bsc.intg.svcs</groupId>
		<artifactId>bsc-sp8-framework</artifactId>
		<version>8.0.0</version>
	</parent>

	<dependencies>
		<dependency>
			<groupId>com.bsc.intg.svcs</groupId>
			<artifactId>bsc-sp8-core</artifactId>
			<version>8.0.0</version>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>


			<plugin>
			
			    <groupId>pl.project13.maven</groupId>
			    <artifactId>git-commit-id-plugin</artifactId>
			    <version>2.2.4</version>
			    <executions>
			        <execution>
			            <id>get-the-git-infos</id>
			            <goals>
			                <goal>revision</goal>
			            </goals>
			        </execution>
			    </executions>
			    <configuration>
			        <dotGitDirectory>${project.basedir}/.git</dotGitDirectory>
			        <prefix>git</prefix>
			        <verbose>false</verbose>
			        <generateGitPropertiesFile>true</generateGitPropertiesFile>
			        <generateGitPropertiesFilename>src/main/resources/build.service.info</generateGitPropertiesFilename>
			        <format>json</format>
			        <gitDescribe>
			            <skip>false</skip>
			            <always>false</always>
			            <dirty>-dirty</dirty>
			        </gitDescribe>
			    </configuration>
		
			</plugin>

		</plugins>


	</build>


</project>