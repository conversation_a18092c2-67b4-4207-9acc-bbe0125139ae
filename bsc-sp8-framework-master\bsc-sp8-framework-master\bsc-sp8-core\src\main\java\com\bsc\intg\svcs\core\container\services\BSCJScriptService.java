package com.bsc.intg.svcs.core.container.services;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.util.ArrayList;
import java.util.List;

import javax.script.Bindings;
import javax.script.ScriptContext;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCServiceBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCMessage;

public class BSCJScriptService extends BSCServiceBase {


	public BSCJScriptService(BSCComponentType componentType, BSCComponent parentComponent, String configName,
			String instance) {
		super(componentType, parentComponent, configName, instance);
	}



	protected boolean valid=false;		
	
	protected ScriptEngine engine = null;
	protected Bindings bindings  = null;
	@Override
	public BSCMessage service(BSCMessage message) throws BSCServiceException {
	
		try {
			logger.info("Evaluating script for the component {}", message.getMessageOwner().getConfigName());
			bindings.put("message", message);
			engine.eval(message.getScript());
		
		} catch ( RuntimeException | ScriptException  e) {
			throw new BSCServiceException(this,e, BSCErrorLevel.CRITICAL, "Failed to process the script request" );
		}
		
		return message; 
	}

	
	@Override
	public void initialize() throws BSCComponentInitializationException {

		super.initialize();
		
		logger.info("Initializing scripting egnine ");
		
		String engineType=getProperty("engine","nashorn");
		engine  = new ScriptEngineManager().getEngineByName(engineType);
		
		InputStream baseScriptInputStream = this.getClass().getClassLoader().getResourceAsStream(engineType + ".js");
	
		if ( baseScriptInputStream!=null ) {
			try {
				Reader baseScriptReader = new InputStreamReader(baseScriptInputStream);
				engine.eval(baseScriptReader);
			} catch (ScriptException e) {
				logger.error("Failed to evaluate the container base script ");
				valid=false;
				throw new BSCComponentInitializationException(this, e, BSCErrorLevel.CRITICAL, "Failed to initialize script engine service" );
			} finally {
				if ( baseScriptInputStream != null ) {
					try {
						baseScriptInputStream.close();
					} catch (IOException e) {
					logger.error("Failed to close the stream of the base script file",e);
					}
				}
			}
		}
		
		InputStream appScriptInputStream = this.getClass().getClassLoader().getResourceAsStream("services.js");
		
		if( appScriptInputStream != null ) {
			
			try {
				Reader appScriptReader = new InputStreamReader(appScriptInputStream);
				engine.eval(appScriptReader);
			} catch (ScriptException e) {
				logger.error("Failed to evaluate the container app script ");
				valid=false;
				throw new BSCComponentInitializationException(this, e, BSCErrorLevel.CRITICAL, "Failed to initialize script engine service" );
			} finally {
				if (baseScriptInputStream != null ) {
					try {
						baseScriptInputStream.close();
					} catch (IOException e) {
						logger.error(SMTP_TRIGGER, "Failed to close the stream of the base script file",e);
					}
				}
			}
		}
		
		String extJsDir = "";
		if(!getGlobalProperty("bsc.service.root","").equals("")) 
			extJsDir  = getGlobalProperty("bsc.service.root")+"etc/";
				
		String extJSConfig = System.getProperty("bsc.service.ext.js.config",extJsDir);
		String commonJS = System.getProperty("bsc.service.ext.js.common","common.js" );
		
		String ignoreInvaliJS = System.getProperty("bsc.service.ext.js.ignore.invalid","false");
		
		InputStream fis = null;
		InputStreamReader isr = null;
		
		List<File> jsFilesList=new ArrayList<File>();
		
		if (!"".equals(extJSConfig)) {
			
			if(commonJS.equals("common.js")){
				File commonJSFile =new File(extJSConfig+"/" +  commonJS);
				
				if (commonJSFile.isFile() && commonJSFile.exists())
					jsFilesList.add(commonJSFile);
			}else{
				File commonJSFile =new File(commonJS+"/common.js");
				if (commonJSFile.isFile() && commonJSFile.exists())
					jsFilesList.add(commonJSFile);
			}
			
			File jsDir = new File(extJSConfig);
			
			File[] jsFiles = jsDir.listFiles();
			
			if (jsFiles != null) {
				
				for (File jsFile : jsFiles) {
					if (jsFile.isFile() && jsFile.getName().endsWith(".js") && !jsFile.getName().equals(commonJS)  ) {
						jsFilesList.add(jsFile);
					}
				}
						
				for (File jsFile : jsFilesList) {
					
					
					try {
						fis = new FileInputStream(jsFile.getAbsolutePath());
						isr = new InputStreamReader(fis);
						engine.eval(isr);
					} catch (ScriptException | FileNotFoundException e) {
						logger.error("Failed to evaluate the container base script ");
						
						if(!ignoreInvaliJS.equals("true")) {
							valid = false;
							throw new BSCComponentInitializationException(this, e, BSCErrorLevel.CRITICAL, "Failed to initialize script engine service");
						}
						
					} finally {
						
						if (fis != null) {
							try {
								fis.close();
							} catch (IOException e) {
								logger.error("Failed to close the stream of the base script file", e);
							}
						}
						if (isr != null) {
							try {
								isr.close();
							} catch (IOException e) {
								logger.error("Failed to close the stream of the base script file", e);
							}
						}
						
					}
					
					
							
				}

				
				
			}

		
		
		}
				
	
		
		
		ScriptContext context = engine.getContext();
		bindings = context.getBindings(ScriptContext.ENGINE_SCOPE);
		valid=true;
		
	}

	
}
