package com.bsc.intg.svcs.core.soap;

import java.io.ByteArrayOutputStream;

import javax.xml.transform.Source;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.stream.StreamResult;

import com.bsc.intg.svcs.core.message.BSCModel;

public class BSCSOAPGatewayRequest implements BSCModel{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	public Source request;

	public BSCSOAPGatewayRequest(Source request) {
		super();
		this.request = request;
	}
	
	public String getRequest(){
		ByteArrayOutputStream out = new ByteArrayOutputStream();
		StreamResult sr = new StreamResult();
		 Transformer trans;
		 sr.setOutputStream(out);
         
			try {
				trans = TransformerFactory.newInstance().newTransformer();
				trans.setOutputProperty("omit-xml-declaration", "yes");
				trans.transform(request, sr);
			} catch (Exception e1) {
				e1.printStackTrace();				
			}
         return out.toString();
		
	}
	

}
