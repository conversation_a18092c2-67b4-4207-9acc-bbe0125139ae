package com.bsc.intg.svcs.core.test;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentSubType;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestinationBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.message.BSCDestinationAction;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.message.BSCMessageProperties;
import com.bsc.intg.svcs.core.util.BSCPropertyHelper;

public class BSCEventDestinationStub extends BSCDestinationBase {

	HashMap<String, Integer> tcMap = new HashMap<String, Integer>();

	public BSCEventDestinationStub(BSCComponentType componentType, BSCComponent parentComponent, String configName) {
		super(componentType, parentComponent, configName);
	}

	public void initialize() throws BSCComponentInitializationException {

		super.initialize();

		logger.info("Initializing Event Stub");
	}

	@Override
	public BSCMessage onSend(BSCMessage message) throws BSCDestinationException {

		logger.info("Executing onSend method - Event Stub");

		if (message.getAction().equals(BSCDestinationAction.JMS_PUT)) {

			logger.info("Executing JMS_PUT Action");
			String sep = File.separator;
			String workingDir = System.getProperty("user.dir");
			String outputDir = workingDir + sep + "test" + sep + "output" + sep;
			boolean matchFound = true;
			String fileName = "";

			String eventName = message.getProperty(BSCMessageProperties.EVENT_NAME);
			String queueName = this.getProperty("destination.name");
			BSCComponentSubType componentType = message.getMessageOwner().getComponentSubType();
			
			String	tcName = System.getProperty("user.testcase");
			String	serviceStatus = System.getProperty("user.ServiceStatus");

			logger.info("Test case - {}", tcName);
			logger.info("Service status - {}", serviceStatus);
			logger.info("Event - {}", eventName);
			logger.info("Output file location - {}", outputDir);
			logger.info("QueueName - {}", queueName);
			logger.info("ComponentType - {}", componentType.toString());
			logger.info("Data - {}", message);

			tcName = tcName == null? "EventTC":tcName;
					
			if (tcMap.size() < 1) {
				tcMap.put(tcName, 0);
			}

			int fileCtr = 1;
			for (Map.Entry<String, Integer> entry : tcMap.entrySet()) {
				String key = entry.getKey();
				if (key.equals(tcName)) {
					matchFound = true;
					fileCtr = Integer.parseInt(entry.getValue().toString()) + 1;
					entry.setValue(fileCtr);
					break;
				} else {
					matchFound = false;
				}
			}
			if (!matchFound) {
				fileCtr = 1;
				tcMap.put(tcName, fileCtr);
			}

			fileName = queueName + "_" + eventName + "_" + tcName + "_" + String.format("%02d", fileCtr) + ".txt";

			logger.info("Output file name - {}", fileName);
			
			logger.info("Loading custom JMS properties");
        	
        	Map<String,String> jmsProperties = null;
			
        	String includePropertiesPrefix = message.getProperty("include.properties.prefix", "");
        	
        	if (!includePropertiesPrefix.equals("")) {
        		jmsProperties = new HashMap<String,String>();
        		
        		logger.info("Loading JMS properties with prefixes ({})", includePropertiesPrefix );
            	
        		for (String prefix : includePropertiesPrefix.split(",")) {
				
        			logger.info("	Loading JMS properties with prefix ({})", prefix );
                	
        			String prefixToRemove = null, prefixToAdd = null ;
        			
        			String [] splitStrings = prefix.split("/");
        			
        			if (splitStrings.length > 1) {
        				prefixToRemove = prefix.split("/")[0];
        				prefixToAdd = prefix.split("/")[1];
        			} else {
        				prefixToRemove = prefix.split("/")[0];
        				prefixToAdd = "";
        			}
        			
					Map<String,Object> customProperties = (Map<String,Object>) BSCPropertyHelper.getPropertiesStartingWith(message.getProperties(), prefixToRemove, true);
					
					for(Entry<String,Object> p: customProperties.entrySet()) {
						jmsProperties.put("JMS.USER."+ prefixToAdd + p.getKey(), (String)p.getValue());
					}
				}
				
        	}

			try {

				if (!new File(outputDir).exists()) {
					new File(outputDir).mkdir();
				}

				String filePath = outputDir + fileName;
				writeToFile(jmsProperties,message.getProperties(), message.toString(), filePath);

			}catch (IOException e) {

				message.setException(e);
				throw new BSCDestinationException(this, e, BSCErrorLevel.CRITICAL, "Failed to process the request");
			}	
		}
		
		return message;
	}

	public void writeToFile(Map<String, String> jmsProps, Map<String, Object> props, String data, String filePath) throws IOException {

		BufferedWriter writer = new BufferedWriter(new FileWriter(filePath));
		Map<String, Object> httpHeaders = (Map<String, Object>) BSCPropertyHelper.getPropertiesStartingWith(props,
				"HTTP", false);
		for (Entry<String, Object> p : httpHeaders.entrySet()) {
			writer.write(p + "\n");
		}
		
		for (Entry<String, String> p : jmsProps.entrySet()) {
			writer.write(p + "\n");
		}
		writer.write(data + "\n");
		
		writer.close();

	}

	@Override
	public List<BSCMessage> onSend(List<BSCMessage> message) throws BSCDestinationException {
		// TODO Auto-generated method stub
		return null;
	}
}