#ApplicationService Configurations to Launch
bsc-sp8-framework.application.services=AppService1

#Application Service Resources
AppService1.resources=JMSGW,JMSEVENT
AppService1.XA.required=false

#Application Service Containers
AppService1.containers=IntContainer


#Non-XA Connection Resource
JMSGW.cached=true
JMSGW.connection.name=MQGW
JMSGW.class=com.bsc.intg.svcs.core.jms.BSCJMSConnectionResource

#Event Connection Resource
JMSEVENT.cached=true
JMSEVENT.connection.name=EVENT
JMSEVENT.class=com.bsc.intg.svcs.core.jms.BSCJMSConnectionResource

#XA Connection Resource
JMSGWXA.cached=false
JMSGWXA.XA=true
JMSGWXA.connection.name=MQGW
JMSGWXA.class=com.bsc.intg.svcs.core.jms.BSCJMSConnectionResource


#Integration container
IntContainer.listeners=IntJMSListener
IntContainer.services=IntegrationService
IntContainer.controllers=IntRESTController

#REST Controller
IntRESTController.class=com.bsc.intg.svcs.controller.IntegrationRESTController

#JMS Listener
IntListener.class=com.bsc.intg.svcs.core.jms.BSCJMSListener
IntListener.threads=10
IntListener.connection=JMSGW
IntListener.destination.name=APP.SERVICE.PUBLISH
IntListener.services=IntRESTService

#Business Integration Services
IntRESTService.class=com.bsc.intg.svcs.service.IntegrationRESTService
IntRESTService.pool.size=10

#Business Integration Services
IntSOAPService.class=com.bsc.intg.svcs.service.IntegrationSOAPService
IntSOAPService.pool.size=10


#Business Integration Service Destinations
IntRESTService.destinations=Publish

##JMS Destination (Primary)
Publish.connection=JMSGW
Publish.class=com.bsc.intg.svcs.core.jms.BSCJMSDestination
Publish.destination.name=APP.SERVICE.QUEUE


