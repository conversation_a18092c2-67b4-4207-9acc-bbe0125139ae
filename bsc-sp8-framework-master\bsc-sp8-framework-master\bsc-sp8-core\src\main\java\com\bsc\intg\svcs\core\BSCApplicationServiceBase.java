package com.bsc.intg.svcs.core;

import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.transaction.SystemException;

import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.jta.JtaTransactionManager;

import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.jdbc.BSCJDBCDataSourceResource;
import com.bsc.intg.svcs.core.jms.BSCJMSConnectionResource;
import com.bsc.intg.svcs.core.util.BSCPropertyHelper;
import com.bsc.intg.svcs.core.util.BSCStringUtils;

public class BSCApplicationServiceBase extends BSCComponentBase implements BSCApplicationService{

	private ArrayList<String> containers=null;
	private ArrayList<String> runtimeContainers=null;
	private ArrayList<String> resources=null;

	
	private Map<String, BSCResource> 			resourceMap 		= new HashMap<String, BSCResource>();
	
	private Map<String, BSCServiceContainer> 	containerMap 		= new HashMap<String, BSCServiceContainer>();

	private Map<String, BSCServiceContainer> 	runtimeContainerMap 		= new HashMap<String, BSCServiceContainer>();

	protected PlatformTransactionManager  		platformTransactionManager  = null;
	
	public BSCApplicationServiceBase(BSCComponentType componentType ,BSCComponent parentComponent, String configName) {
		
		super(componentType, parentComponent, configName);
	}
	
	public PlatformTransactionManager getTransactionManager() {
		return platformTransactionManager;
	}

	public void initialize() throws BSCComponentInitializationException {

		String containerNames  			= 	getProperty("containers","");
		boolean autoDetectContainers		= 	getBooleanProperty("auto.detect.containers", false);
		String resourceNames 			= 	getProperty("resources","");
		String runtimeContainerNames	= 	getProperty("runtime.containers","");
		
		
		if(!autoDetectContainers){
			this.containers=BSCStringUtils.convertCommaSeperated(containerNames);
		}else{
			this.containers=BSCStringUtils.convertCommaSeperated(getExternalContainers()+containerNames);
		}
		
		this.resources=BSCStringUtils.convertCommaSeperated(resourceNames);
		
		this.runtimeContainers=BSCStringUtils.convertCommaSeperated(runtimeContainerNames);
		
	
		if ( getBooleanProperty("XA.required", false) ){
			
			com.atomikos.icatch.jta.UserTransactionManager atomikosTransactionManager = new com.atomikos.icatch.jta.UserTransactionManager();
			atomikosTransactionManager.setForceShutdown(false);
			try {
				atomikosTransactionManager.init();
			} catch (SystemException e) {
				e.printStackTrace();
			}
			
			com.atomikos.icatch.jta.J2eeUserTransaction atomikosUserTransaction=new com.atomikos.icatch.jta.J2eeUserTransaction();
			try {
				atomikosUserTransaction.setTransactionTimeout(120);
			} catch (SystemException e) {
				e.printStackTrace();
			}
			
			JtaTransactionManager jta =new JtaTransactionManager();
			jta.setTransactionManager(atomikosTransactionManager);
			jta.setUserTransaction(atomikosUserTransaction);
			jta.setAllowCustomIsolationLevels(true);
			
			this.platformTransactionManager=jta;
		}
		
		
		List<String> siteList = Arrays.asList(getGlobalProperty("bsc.service.sites","map").split("\\s*,\\s*"));

		
		for (String prefix:siteList) {
			
			logger.info("Initializing the connection group {} ", prefix);
			
			for (String resourceObjName : resources) {
				
				logger.info("Creating the resource class {} ", resourceObjName);

				BSCResource resource= null;
				String resourceClassName	=	getGlobalProperty(resourceObjName + ".class");
				
				try {
					Class<?> sc = Class.forName(resourceClassName);
					Constructor<?>	ctor=sc.getConstructor(BSCComponentType.class,BSCComponent.class,String.class );
					resource = (BSCResource) ctor.newInstance(BSCComponentType.RESOURCE, this,resourceObjName);
					resource.initialize(prefix);
					resourceMap.put(prefix + "." + resourceObjName, resource);
				} catch (Exception | BSCComponentInitializationException e) {
					logger.info("Failed to create Creating the resource class {} ", resourceObjName);
					throw new BSCComponentInitializationException(this, e, BSCErrorLevel.CRITICAL, "Resource creation failed -" + resourceObjName);
				}
				
			}
			
		}
		
		
		for (String containerObjName:containers) {
			
			logger.info("Starting the container {}" , containerObjName);
			
			BSCServiceContainer container=new BSCDefaultServiceContainer(BSCComponentType.CONTAINER, this, containerObjName);
			
			logger.info("Initializing the container {}" , containerObjName);
			
			container.initialize();
			
			logger.info("Adding the container {} the list" , containerObjName);
			
			containerMap.put(containerObjName, container);
		}

		for (String containerObjName:runtimeContainers) {
			
			logger.info("Starting runtime container {}" , containerObjName);
			
			BSCServiceContainer container=new BSCDefaultServiceContainer(BSCComponentType.CONTAINER, this, containerObjName);
	
			logger.info("Initializing the runtime container {}" , containerObjName);
			
			container.initialize();
			
			logger.info("Adding the runtime container {} the list" , containerObjName);
	
			runtimeContainerMap.put(containerObjName, container);
		}

		
		
	}
 

	private static String getExternalContainers() {
		
		Properties props = System.getProperties();
		String containerList = "";
		
		@SuppressWarnings({ "unchecked", "rawtypes" })
		Map<String, Object> sysPropMap = (Map)props;
		
		Map<String, Object> listenerContMap = BSCPropertyHelper.getPropertiesEndingWith(sysPropMap, ".listeners", false);
		
		Map<String, Object> controllerContMap = BSCPropertyHelper.getPropertiesEndingWith(sysPropMap, ".controllers", false);
		
		for (Map.Entry<String, Object> entry : listenerContMap.entrySet()) {
			containerList += entry.getKey().toString().substring(0, entry.getKey().toString().indexOf(".")) + ",";
		}
		
		for (Map.Entry<String, Object> entry : controllerContMap.entrySet()) {
			containerList += entry.getKey().toString().substring(0, entry.getKey().toString().indexOf(".")) + ",";
		}
		
		
		return containerList;
	}

	public BSCResource getResource(boolean  primary, String name) {
		
		String resourceName;
		
		if ( primary ) {
			resourceName=getGlobalProperty("primary.prefix","map") + "." + name;
		} else {
			resourceName=getGlobalProperty("backup.prefix","nat") + "." + name;
		}
		
		
		return resourceMap.get(resourceName);
	}

	public BSCResource getResource( String name) {
		
		String resourceName;
		
		resourceName=getGlobalProperty("bsc.service.sites","map") + "." + name;
		
		
		return resourceMap.get(resourceName);
	}

	public BSCServiceContainer getContainer(String container) {
		return containerMap.get(container);
	}

	public BSCServiceContainer getRuntimeContainer(String container) {
		return runtimeContainerMap.get(container);
	}

	public BSCApplicationServiceConfig getApplicationServiceConfig() {
		return (BSCApplicationServiceConfig)parentComponent;
	}

	@Override
	public void start() throws BSCComponentInitializationException {

		for (String containerObjName:runtimeContainers) {
			
			try {
				
				logger.info("Starting the runtime container {} " , containerObjName);
			
				this.getRuntimeContainer(containerObjName).start();
			
			} catch (BSCComponentInitializationException e) {
				throw e;
			}
		}
		
		for (String containerObjName:containers) {
			try {
				logger.info("Starting the application container {} " , containerObjName);
				this.getContainer(containerObjName).start();
			} catch (BSCComponentInitializationException e) {
				throw e;
			}
		}


		
	}

	@Override
	public void stop() {
		
		for (String containerObjName:containers) {
			
			logger.info("Stopping the application container {} " , containerObjName);
			
			this.getContainer(containerObjName).stop();
		}

		for (String containerObjName:runtimeContainers) {
			
			logger.info("Stopping runtime container {}" , containerObjName);
			
			this.getRuntimeContainer(containerObjName).stop();
		}
	}



	
	

}
