package com.bsc.intg.svcs.core.exception;

import com.bsc.intg.svcs.core.BSCComponent;

public class BSCDestinationException extends BSCExceptionBase {


	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;



	
	public BSCDestinationException(BSCComponent source,Throwable ex,BSCErrorLevel l, String message) {
		super(source, ex,l,message);
	};

	public BSCDestinationException(BSCComponent source, BSCErrorLevel l, String message) {
		super(source, l, message);
	};
	
	
	public BSCDestinationException(BSCComponent source, Throwable ex, BSCErrorLevel l) {
		super(source, ex,l);
	}
	
}
