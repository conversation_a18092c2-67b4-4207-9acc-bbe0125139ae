package com.bsc.intg.svcs.core.locking;



/**
 * 
 * ServiceLock class defines abstract behavior for the Lock in general
 * 
 * @see ZooLock
 * 
 * <AUTHOR> <PERSON>
 *         <p>
 *         $Revision: 1.1 $
 *         <p>
 */
public abstract class ServiceLock {
	
	public abstract boolean acquire(int timeout) throws Exception; 
	public abstract boolean acquire() throws Exception;
	public abstract void release() throws Exception;
}	
