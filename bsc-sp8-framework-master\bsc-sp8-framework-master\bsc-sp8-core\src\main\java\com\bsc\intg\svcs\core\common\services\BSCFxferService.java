package com.bsc.intg.svcs.core.common.services;

import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ArrayUtils;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestination;
import com.bsc.intg.svcs.core.BSCIntegrationServiceBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCFileUtils;



public class BSCFxferService extends BSCIntegrationServiceBase {


	protected List<BSCDestination> 	destList 			= new ArrayList<BSCDestination>();
	private String 					inEncoding 			= null;
	private String 					outEncoding 		= null;
	private String 					outBOM	 			= null;
	private Boolean extraction;
	private BSCDestination workdest;
	private String extractionFilePattern;
	private boolean extractContent;
	
	public BSCFxferService(BSCComponentType componentType, BSCComponent parentComponent, String configName,
			String instance) {
		super(componentType, parentComponent, configName, instance);
	}


	@Override
	public BSCMessage service(BSCMessage inMessage) throws BSCServiceException {
		logger.debug("Executing FxferService service");
		
		BSCMessage outMessage = null;
		
		String fileName = inMessage.getProperty("VFS.FILE.NAME");		
		String extractedFilePath = null;
		String workDestPath=null;
		try {		

			List<String> eventFileList = new ArrayList<String>();

			if (extraction) {
				logger.info("Extraction needed for {}", fileName);
				logger.info("Publishing to Work location for Extraction");
				workDestPath = workdest.getProperty("file.uri");  	
				workdest.send(inMessage);

				try {
					extractedFilePath = BSCFileUtils.extractFromCompressedFile(workDestPath, fileName,
							extractionFilePattern);
					if (extractedFilePath != null) {
						logger.info("Extraction completed for File {}", extractedFilePath);
						BSCFileUtils.prepareBSCMessageWithExtractedFile(extractedFilePath, extractContent, inMessage);						
					} else {
						throw new BSCServiceException(this, null, BSCErrorLevel.CONTINUE,
								"File with  " + extractionFilePattern + " is not found in " + workDestPath);
					}
				} catch (IOException e) {
					throw new BSCServiceException(this, e, BSCErrorLevel.ROLLBACK,
							"Failed to extract content from Compressed File " + workDestPath);
				}
			}
						
			for(BSCDestination publish : destList){
				logger.info("Delivery intiated for file {} to destination {} ", fileName,publish.getConfigName());
				
				//Encoding Logic
				if(inEncoding != null && outEncoding != null){
					boolean outBOMFlag = false;
					String mOutEncoding = outEncoding;
					
					if(outEncoding.endsWith("-BOM")){
						mOutEncoding = outEncoding.substring(0,outEncoding.indexOf("-BOM"));
						outBOMFlag = true;
					}
					if(Charset.isSupported(inEncoding) && Charset.isSupported(mOutEncoding)){
						logger.info("File encoding is changed from {} to {} ", inEncoding, outEncoding);
						
						try {
							String inEncMsg = inMessage.toString(inEncoding);
							
							byte[] bomBytes = null,combinedBytes = null;
							
							bomBytes = outBOM.getBytes();
							if(outBOMFlag &&  bomBytes != null){								
								logger.debug("Byte Order Mark needs to be added to ouput file for encoding {} and BOM {}",outEncoding, bomBytes);
								combinedBytes = ArrayUtils.addAll(bomBytes, inEncMsg.getBytes(mOutEncoding));
							}else{
								combinedBytes = inEncMsg.getBytes(mOutEncoding);
							}							
							
							inMessage.reset();
							inMessage.write(combinedBytes);
						} catch (IOException e) {
							throw new BSCServiceException(this,e,BSCErrorLevel.ROLLBACK,"Enocding not supported & Error Description: "+e.getMessage());
						}
					}else{
						throw new BSCServiceException(this,BSCErrorLevel.ROLLBACK,"Enocding not supported");
					}
				}
				
				outMessage = publish.send(inMessage);
				logger.info("Delivered file {} to destination {} ", fileName,publish.getConfigName());
				eventFileList.add(outMessage.getStringMessage());
			}
			
			outMessage = inMessage.cloneMessage(false);
			outMessage.setProperty("include.properties.prefix", inMessage.getMessageOwner().getConfigName() + ".Meta./" );
			outMessage.loadMessage(eventFileList.stream().map(x -> x).collect(Collectors.joining(" \n ")));
			
		} catch (BSCMessageException  e ) {
			throw new BSCServiceException(this,e,BSCErrorLevel.ROLLBACK,"Failed to execute the app service. " + e.getMessage());
		
		
	}catch(BSCDestinationException e){
		if(e.getLevel()==BSCErrorLevel.ROLLBACK){
		throw new BSCServiceException(this,e,BSCErrorLevel.ROLLBACK,"Failed to execute the app service. " + e.getMessage());
		}else{
		throw new BSCServiceException(this,e,BSCErrorLevel.CRITICAL,"Failed to execute the app service. " + e.getMessage());
		}
		}
		finally {
			if (extraction) {
				BSCFileUtils.deleteExtractedFile(workDestPath+fileName, extractedFilePath);				
			}
		}
		return outMessage;
	}

	
	@Override
	public void initialize() throws BSCComponentInitializationException {

		super.initialize();
			
		valid					=	true;
		inEncoding 				= 	this.getProperty("input.encoding",null);
		outEncoding				= 	this.getProperty("output.encoding",null);
		outBOM					= 	this.getProperty("output.bom","");
		extraction				= 	this.getBooleanProperty("extraction",false);
		extractionFilePattern	= 	this.getProperty("extraction.file.pattern");
		extractContent			= 	this.getBooleanProperty("extraction.file.content", true);		
		String destinations 	= 	this.getProperty("destinations");
		List<String> destList 	= 	new ArrayList<String>(Arrays.asList(destinations.split("\\s*,\\s*")));
		String workDestinationName = this.getProperty("extraction.work.destination",null);
		if (workDestinationName != null) {
			destList.remove(workDestinationName);
			workdest = this.getDestination(workDestinationName);
		}

		for (String dest : destList) {
			this.destList.add(this.getDestination(dest));
		}	
		
	}
	
}
