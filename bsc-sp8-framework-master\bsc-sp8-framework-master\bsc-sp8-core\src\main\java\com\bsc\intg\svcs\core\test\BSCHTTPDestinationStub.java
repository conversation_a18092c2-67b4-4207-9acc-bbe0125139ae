
package com.bsc.intg.svcs.core.test;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.StringReader;
import java.io.StringWriter;
import java.io.Writer;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;

import org.json.JSONObject;
import org.json.XML;
import org.w3c.dom.Document;
import org.xml.sax.InputSource;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentSubType;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestinationBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCPropertyHelper;
import com.fasterxml.jackson.databind.ObjectMapper;

public class BSCHTTPDestinationStub extends BSCDestinationBase {

	public BSCHTTPDestinationStub(BSCComponentType componentType, BSCComponent parentComponent, String configName) {
		super(componentType, parentComponent, configName);
	}

	public void initialize() throws BSCComponentInitializationException {

		super.initialize();

		logger.info("Initializing HTTP Stub");
	}

	@Override
	public BSCMessage onSend(BSCMessage message) throws BSCDestinationException {

		logger.info("Executing onSend method - HTTP Stub");	
		
		String 	url 				= message.getProperty("REQUEST.HTTP.URL","NO-URL");		
		String 	method 				= message.getProperty("REQUEST.HTTP.METHOD","NO-METHOD");
		String	tcName 				= System.getProperty("user.testcase","Default");
		String	serviceStatus 		= System.getProperty("user.ServiceStatus","Up");
		String 	contentType			= message.getProperty("REQUEST.HTTP.Content-Type","JSON");
		
		BSCMessage outMessage = null;
		
		try {
			outMessage = message.cloneMessage(false);
		} catch (BSCMessageException e1) {
			logger.error("Message cloning failed for test case {}",tcName,e1);
			e1.printStackTrace();
			outMessage = message;
		}
		
		Map<String, Object> customHeaders = BSCPropertyHelper.getPropertiesStartingWith(message.getProperties(),
				"REQUEST.HTTP.HEADER.", true);
		
		String customHTTPPayload = System.getProperty("custom.http.payload");
		
		if (serviceStatus.contains("Down")) {
			logger.info("Stubbing for service status {}",serviceStatus);	
			outMessage.setProperty("RESPONSE.HTTP.HEADER.STATUS","NOT OK");
            outMessage.setProperty("RESPONSE.HTTP.HEADER.STATUSCODE","500");
            outMessage.setProperty("RESPONSE.HTTP.HEADER.ERRORDESC","Internal server error");
            throw new BSCDestinationException(this, BSCErrorLevel.CRITICAL,"Service is down");
			
		}else{
			try {
				logger.info("Stubbing for service status {}",serviceStatus);	
				if(customHTTPPayload!=null)
				{
					outMessage.loadMessage(customHTTPPayload);
				}
				else{
				ObjectMapper mapper = new ObjectMapper();
				JSONObject jsonObj = new JSONObject();
				JSONObject payloadObj = new JSONObject();
				JSONObject headerObj = new JSONObject();
	
				customHeaders.forEach((k, v) -> {headerObj.put(k, v);});
				payloadObj.put("url", url);
				payloadObj.put("method", method);
				payloadObj.put("tcName", tcName);
				payloadObj.put("status", "success");
	
				jsonObj.put("header", headerObj);
				jsonObj.put("payload", payloadObj);
	
				
				if (contentType != null && (contentType.contains("XML") || contentType.contains("xml"))) {
					String xmlString = XML.toString(jsonObj, "HTTPStubResponse");
					DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
					DocumentBuilder builder = factory.newDocumentBuilder();
					Document document = builder.parse(new InputSource(new StringReader(xmlString)));  
					Transformer tf = TransformerFactory.newInstance().newTransformer();
					tf.setOutputProperty(OutputKeys.ENCODING, "UTF-8");
					tf.setOutputProperty(OutputKeys.INDENT, "yes");
					Writer out = new StringWriter();
					tf.transform(new DOMSource(document), new StreamResult(out));

					outMessage.loadMessage(out.toString());
				}else{
	            	Object jsonObject = mapper.readValue(jsonObj.toString(), Object.class);
		            String prettyJson = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(jsonObject);
		            outMessage.loadMessage(prettyJson);
	            }
				}
	            outMessage.setProperty("RESPONSE.HTTP.HEADER.STATUS","OK");
	            outMessage.setProperty("RESPONSE.HTTP.HEADER.STATUSCODE","200");
				
			} catch (BSCMessageException | Exception e) {
				logger.error("Failed constructing response stub message for test case {}",tcName,e);
				e.printStackTrace();
				
			}
		}
		
		
		if(! (message.getMessageOwner().getComponentSubType().equals(BSCComponentSubType.REST_CONTROLLER)
				|| message.getMessageOwner().getComponentSubType().equals(BSCComponentSubType.SOAP_CONTROLLER))){
			String sep = File.separator;
			String workingDir = System.getProperty("user.dir");
			String outputDir = workingDir + sep + "test" + sep + "output" + sep;
			String fileName = "HTTPResponse_"+tcName+"_"+serviceStatus+".txt";
			Map<String, Object> props = new HashMap<String, Object>();
			props.putAll(BSCPropertyHelper.getPropertiesStartingWith(outMessage.getProperties(),"REQUEST.HTTP.",false));
			props.putAll(BSCPropertyHelper.getPropertiesStartingWith(outMessage.getProperties(),"RESPONSE.HTTP.",false));
			try {
				writeToFile(props, outMessage.getStringMessage(), outputDir, fileName);
			} catch (BSCMessageException | IOException e) {
				e.printStackTrace();
			} 
		}
		
		return outMessage;

	}

	
	public void writeToFile(Map<String, Object> props, String data, String fileDir, String fileName) throws IOException {

		if (!new File(fileDir).exists()) {
			new File(fileDir).mkdir();
		}
		
		BufferedWriter writer = new BufferedWriter(new FileWriter(fileDir+fileName));
		if(props!=null){
			for (Entry<String, Object> p : props.entrySet()) {
				writer.write(p.getKey()+"="+(String)p.getValue() + "\n");
			}
		}
		writer.write(data + "\n");
		writer.close();
	}
	
	@Override
	public List<BSCMessage> onSend(List<BSCMessage> message) throws BSCDestinationException {
		// TODO Auto-generated method stub
		return null;
	}
}
