package com.bsc.intg.svcs.core.util;

import java.text.SimpleDateFormat;
import java.util.Date;

public class BSCVFSHelper {
	
	public static String getMaskURL(String url) { 
		 
		if(url != null){
			int colonPos = url.indexOf(":");
			colonPos = url.indexOf(":",colonPos+1);
			int atPos = url.indexOf("@");
			if(colonPos > 1 && atPos > 1 && atPos > colonPos){
				String pwd = url.substring(colonPos+1, atPos);
				String maskedPwd = pwd.replaceAll(".", "*");
				String maskedURL =  url.replace(":"+pwd+"@", ":"+maskedPwd+"@");
				return maskedURL;
			}else{
				return url;
			}
		}else{
			return url;
		}
	}
	
	public static String updateDateInFileName(String fileName, String dateExpr, String format) { 
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
		String dateStr = simpleDateFormat.format(new Date());
		fileName = fileName.replace(dateExpr,dateStr);
		return fileName;
		
		
	}
	

}
