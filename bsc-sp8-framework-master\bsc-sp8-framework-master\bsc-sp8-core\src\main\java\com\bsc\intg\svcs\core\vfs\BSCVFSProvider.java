package com.bsc.intg.svcs.core.vfs;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import org.apache.commons.vfs2.FileContent;
import org.apache.commons.vfs2.FileName;
import org.apache.commons.vfs2.FileObject;
import org.apache.commons.vfs2.FileSelectInfo;
import org.apache.commons.vfs2.FileSelector;
import org.apache.commons.vfs2.FileSystemException;
import org.apache.commons.vfs2.FileSystemOptions;
import org.apache.commons.vfs2.FileType;
import org.apache.commons.vfs2.FileUtil;
import org.apache.commons.vfs2.impl.DefaultFileSystemManager;
import org.slf4j.Logger;

public class BSCVFSProvider {

	 protected Logger logger =null; 
	
	 public final String separator=""; 
	 
	 protected DefaultFileSystemManager fileSystemManager; 
	
	 
	 public final String getSeparator() { 
		 return separator; 
	 } 
	 
	 public final String getRootPath() { 
	  return FileName.ROOT_PATH; 
	 } 
	 
	 public boolean move(String encOrigFileName, String encNewFileName)    throws IOException { 
		  
		 FileObject origFile = resolveFile(encOrigFileName); 
		  if (origFile.exists() == false) 
		   return false; 
		 
		  FileObject newFile = resolveFile(encNewFileName); 
		  if (encNewFileName.lastIndexOf(separator) > 0) { 
		   if (newFile.getParent().exists() == false) { 
		    return false; 
		   } 
		  } 
		  origFile.moveTo(newFile); 
		  return true; 
	 } 
	 
	 public FileObject resolveFile(String encOrigFileName)    throws FileSystemException { 
		return fileSystemManager.resolveFile( encOrigFileName); 
	 } 
	
	 public FileObject resolveFile(String encOrigFileName, FileSystemOptions opts)    throws FileSystemException { 
		 return fileSystemManager.resolveFile( encOrigFileName,opts); 
	 } 
	 
	 public boolean isDirectory(String encFileName,FileSystemOptions opts) throws IOException { 
		 FileObject file = resolveFile(encFileName); 
		 return file.getType() == FileType.FOLDER; 
	 } 
	 
	 public boolean delete(String encFileName,FileSystemOptions opts) throws IOException { 
		  FileObject file = resolveFile(encFileName,opts); 
		  return file.delete(); 
	 } 
	 
	 public boolean mkdir(String encDirName,FileSystemOptions opts) throws IOException { 
		  FileObject file = resolveFile(encDirName,opts); 
		  if (file.exists()) { 
		   return false; 
		  } else { 
		   if (encDirName.lastIndexOf(separator) != 0) { 
		    if (file.getParent().exists() == false) { 
		     return false; 
		    } 
		   } 
		   file.createFolder(); 
		   return true; 
		  } 
	 } 
	 
	 public boolean mkdirs(String encDirName,FileSystemOptions opts) throws IOException { 
		  String[] dirNameParts = encDirName.split(separator); 
		 
		  String tmpDirName = ""; 
		  for (int i = 0; i < dirNameParts.length; i++) { 
		   if (tmpDirName.endsWith(separator) == false) { 
		    tmpDirName += separator; 
		   } 
		   tmpDirName += dirNameParts[i]; 
		 
		   FileObject tmpDirFile = resolveFile(tmpDirName); 
		   boolean partResult = true; 
		   if (tmpDirFile.exists() == false) { 
		    partResult = mkdir(tmpDirName,opts); 
		   } else if (tmpDirFile.getType() == FileType.FILE) { 
		    partResult = false; 
		   } 
		 
		   if (partResult == false) { 
		    return false; 
		   } 
		  } 
		 
		  return true; 
	 } 
	 
	 public boolean copy(String encSrcFileName, String encTargetFileName)     throws IOException { 
		  FileObject srcFile = resolveFile(encSrcFileName); 
		  FileObject targetFile = resolveFile(encTargetFileName); 
		 
		  FileUtil.copyContent(srcFile, targetFile); 
		  return true; 
		 } 
		 
	
	 public List<BSCVFSObjectInfo> listFiles(String encDirName) throws IOException { 
		 
		  FileObject srcDir = resolveFile(encDirName); 
		  FileObject[] children = srcDir.getChildren(); 
		 
		  
		  List<BSCVFSObjectInfo> result = new ArrayList<BSCVFSObjectInfo>(children.length); 
		  for (int i = 0; i < children.length; i++) { 
		   result.add(getFileInfo(children[i])); 
		  } 
		 
		  return result; 
	 } 

	 public List<BSCVFSObjectInfo> listFiles(String encDirName, FileSystemOptions options ) throws IOException { 
		 
		  FileObject 	srcDir 		= null; 
		  FileObject[] 	children 	= null; 
		 
		  if(options != null){
			  srcDir = resolveFile(encDirName, options); 
		  }else{
			  srcDir = resolveFile(encDirName); 
		  }
		  
		  children= srcDir.getChildren();
		  
		  List<BSCVFSObjectInfo> result = new ArrayList<BSCVFSObjectInfo>(children.length); 
		  for (int i = 0; i < children.length; i++) { 
		   result.add(getFileInfo(children[i])); 
		  } 
		 
		  return result; 
	 } 
	 
	 public List<BSCVFSObjectInfo> listFiles(final String dirName,final  String pattern, final long age, FileSystemOptions options,final boolean recursiveScan) throws IOException { 
		 
		 logger.debug("Examine files in the path '{}'  for pattern '{}' and age '{}'",trimPassword(dirName),pattern,age);

		  
		 FileObject srcDir ; 
		 final int srcDepth;
		  
		  if(options != null){
			  srcDir = resolveFile(dirName, options); 
			  srcDepth =  srcDir.getName().getDepth();
		  }else{
			  srcDir = resolveFile(dirName); 
			  srcDepth =  srcDir.getName().getDepth();
		  }
		  
		  
		 FileSelector selector = new FileSelector() {
		  public boolean traverseDescendents(FileSelectInfo info) throws Exception {
	            return true;
	        }

		    public boolean includeFile(FileSelectInfo info) throws Exception {
	        	
		    	FileObject fo = info.getFile();
		    	FileName fn=fo.getName();
		    	int depth = fn.getDepth();
		    	
		    	
		    	if(!recursiveScan && ((depth-srcDepth)>1) ){
		    		//logger.info("Skipping file {} as recursive scan is turned off",fn.getFriendlyURI());
		    		return false;
		    	}else if(fo.getType() == FileType.FOLDER ){
		    		return false;
		    	}		    	
		    	
		    	logger.debug("Examining file {} ", fn.getFriendlyURI());
		    	
		    	long lastModifiedTime = info.getFile().getContent().getLastModifiedTime();
		    	long currentTime = Calendar.getInstance().getTimeInMillis();
		    	
		    	Instant lastModifiedInstant = Instant.ofEpochMilli(lastModifiedTime);
		    	Instant currentTimeInstant = Instant.ofEpochMilli(currentTime);
		    	
		    	
		    	if(Pattern.matches(pattern, fn.getBaseName())){
		    		
		    		logger.debug("Examining file {} further as it pass the pattern {} check",fn.getBaseName(), pattern);
	        		long fileAge = currentTime - lastModifiedTime;
	        		logger.debug("File:{} ,lastModifiedTime:{} ,currentTime:{} , computed-fileAge(ms):{}, expected-fileAge(ms):{}",fn.getFriendlyURI(), lastModifiedInstant.toString(),currentTimeInstant.toString(), fileAge, age);
    		    	
	        		if(fileAge > age){
	        			logger.info("Configured age check for file {} passed ",fn.getFriendlyURI());
	        			Map<String, Object> atrMap = info.getFile().getContent().getAttributes(); 	    		    	
	        			if(atrMap != null && atrMap.size() >0){
	        				atrMap.forEach((k,v) -> logger.debug("Configured File Attribute are {}={}", k, v));
	        			}else{
	        				logger.debug("Configured File Attribute doesnot exist");
	        			}
	        			return true;
	        		}else{
	        			logger.info("Skipping file {} as its age {} ms is still less than the configured file age {}", fn.getFriendlyURI(), fileAge, age);
	        		}
	        		
	        		return false;
	        	} else {
	        		logger.info("Skipping file {} as it doesn't match the configured pattern {}" ,fn.getFriendlyURI(), pattern);
	        		return false;
	        	}		    	
		   }
	     };
	    
		    
		  FileObject[] children = srcDir.findFiles(selector);

		    
		  List<BSCVFSObjectInfo> result = null ;
		  
		  if (children != null ) {
			  	result=new ArrayList<BSCVFSObjectInfo>(children.length); 
				
				for (int i = 0; i < children.length; i++) {			  
				  result.add(getFileInfo(children[i])); 
				} 
	 		} 
	 
		  return result; 
	      
	    
	 } 
	 
	 public InputStream openInputStream(String encSrcFile) throws IOException { 
		  FileObject srcFile = resolveFile(encSrcFile); 
		  return srcFile.getContent().getInputStream(); 
	 } 
	 
	 public FileContent getFileContent(String encSrcFile, FileSystemOptions options) throws IOException { 
		  
		 FileObject srcFile = resolveFile(encSrcFile, options); 
		  
		  return srcFile.getContent(); 
	 } 
	 
	 public OutputStream openOutputStream(String encSrcFile, long outputLength)   throws IOException { 
		  FileObject srcFile = resolveFile(encSrcFile); 
		  return srcFile.getContent().getOutputStream(); 
	 } 
	 
	 public BSCVFSObjectInfo getFileInfo(String encSrcFile) throws IOException { 
		  FileObject srcFile = resolveFile(encSrcFile); 
		  return getFileInfo(srcFile); 
	 } 
	 
	 public boolean exists(String encSrcFile) throws IOException { 
		  FileObject srcFile = resolveFile(encSrcFile); 
		  return srcFile.exists(); 
	 } 
	 
	 private BSCVFSObjectInfo getFileInfo(FileObject fileObject) throws IOException { 

		  String name = fileObject.getName().getPath(); 
		  String volumePath = fileObject.getName().getPath(); 
		  String url		= fileObject.toString();
		 
		  boolean isDirectory = fileObject.getType() == FileType.FOLDER; 
		  long modified = fileObject.getContent().getLastModifiedTime(); 
		  long size = (isDirectory ? 0 : fileObject.getContent().getSize()); 
		  boolean canRead = true;
		  boolean canWrite =true;
		  
		  boolean canExecute = false; 
		  String fileName = fileObject.getName().getBaseName(); 
		 
		  BSCVFSObjectInfo result = new BSCVFSObjectInfo(name, volumePath, url,  isDirectory, modified, size, canRead, canWrite, canExecute,fileName); 
		 
		  return result; 
	 } 
	 
	 public BSCVFSObjectInfo createFile(String encTargetFile) throws IOException { 
		 
		  if (exists(encTargetFile)) { 
		   throw new IOException("File already exists"); 
		  } 
		 
		  FileObject targetFile = resolveFile(encTargetFile); 
		  targetFile.createFile(); 
		 
		  return getFileInfo(targetFile); 
	 } 
	 
	 public String trimPassword(String s){
		 int atPos= s.indexOf("@");
		 if (atPos != -1){
			 s=s.substring(atPos);
		 }
		
		 return s;
	 }
	 
	 public void init() throws FileSystemException{
		 fileSystemManager.init();
	 }
	 public void close() throws FileSystemException{
		 ((DefaultFileSystemManager) fileSystemManager).close();
	 }
}
