package com.bsc.intg.svcs.core.locking;



/**
 * 
 * LockingService class defines abstract behavior for the LockService
 * 
 * @see ZooLock
 * 
 * <AUTHOR> <PERSON>
 */

public interface LockService {
	
	public ServiceLock acquire( int timeout, String path) throws ServiceLockException;
	
	public ServiceLock acquire( String path) throws ServiceLockException;
	
	public void release(ServiceLock lock) throws ServiceLockException;
	
	public void setLogLevel(String level);
	
	public LockServiceProvider getLockServiceProvider();
	
	public boolean lockExists(ServiceLock lock) ;
	
}
