package com.bsc.intg.svcs.core.jdbc;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.sql.DataSource;
import javax.transaction.Transactional;

import org.springframework.jdbc.core.CallableStatementCreator;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.PreparedStatementSetter;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.SqlParameter;
import org.springframework.jdbc.datasource.DriverManagerDataSource;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestinationBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCStringUtils;

@Transactional
public class BSCJDBCDestination extends BSCDestinationBase {

	public BSCJDBCDestination(BSCComponentType componentType, BSCComponent parentComponent, String configName) {
			super(componentType, parentComponent, configName);
	}
	
	private JdbcTemplate  	jdbcTemplate = new JdbcTemplate();
	
	private String connection;
	private boolean isPreparedStmt = false;
	
	public void initialize() throws BSCComponentInitializationException {
		super.initialize();
		logger.info("Initializing JDBC Destination {}",this.getConfigName());
		try {
			
			this.connection= getNonEmptyProperty("connection");	
			this.jdbcTemplate=createJdbcTemplate();
			
		} catch (Exception e) {
			throw new BSCComponentInitializationException(this,e, BSCErrorLevel.CRITICAL,  "Failed to initialize the destination (" + getConfigName() + ")");
		}
		
	}
	
	@Override
	public BSCMessage onSend(BSCMessage message) throws BSCDestinationException {
		
		BSCMessage outMsg = null;
		
		if(message.getAction()!=null){
			String query = message.getProperty("sql.query", this.getProperty("sql.query"));
			
			switch (message.getAction()) {
			
			case JDBC_SELECT:
				
				String responseFormat = message.getProperty("sql.response.format", this.getProperty("sql.response.format","json"));				
				isPreparedStmt = Boolean.valueOf(message.getProperty("create.preparedstmt", this.getProperty("create.preparedstmt","true")));				
				List<Object> results = new ArrayList<>();
				try {
					if(query.toLowerCase().startsWith("call")){
				
						List<SqlParameter> parameters = Arrays.asList(new SqlParameter(Types.NVARCHAR));
						Map<String, Object> resultMap = jdbcTemplate.call(new CallableStatementCreator() {
							int i=1;
							@Override
							public CallableStatement createCallableStatement(Connection conn) throws SQLException {
								CallableStatement cs = conn.prepareCall("{"+query+"}");
						        
								BSCStringUtils.convertCommaSeperated(message.getProperty("cst.param.values"))
								.stream()
								.forEach((param) -> {
									try {
										cs.setObject(i, param);
										i++;
									} catch (SQLException e) {
										throw new RuntimeException(e);
									}
									
								});							
						        
						        return cs;
							}
						}, parameters);
						
					}else if(isPreparedStmt && query.contains("?")){
							jdbcTemplate.query(query, new PreparedStatementSetter(){
								int i=1;
								@Override
								public void setValues(PreparedStatement pst) throws SQLException {
									
									BSCStringUtils.convertCommaSeperated(message.getProperty("pst.param.values"))
									.stream()
									.forEach((param) -> {
										try {
											pst.setObject(i, param);
											i++;
										} catch (SQLException e) {
											throw new RuntimeException(e);
										}
										
									});
												
								}
								
							}, new RowMapper<Object>() {
		
								@Override
								public Object mapRow(ResultSet row, int arg1) throws SQLException {
									String csvHeader = "";
									if(csvHeader.isEmpty() && responseFormat.equalsIgnoreCase("txt")){
										csvHeader = (String)BSCJDBCUtils.colNamesToDelimFileHeader(row,message.getProperty("col.delimiter", ","));
										results.add(csvHeader);
									}
									results.add(BSCJDBCUtils.mapJDBCResponse(row, responseFormat,message.getProperty("col.delimiter", ",")));
									return results;
								}
							});
					}else{
						jdbcTemplate.query(query,new RowMapper<Object>() {
							String csvHeader = "";
							@Override
							public Object mapRow(ResultSet row, int arg1) throws SQLException {
								if(csvHeader.isEmpty() && responseFormat.equalsIgnoreCase("txt")){
									csvHeader = (String)BSCJDBCUtils.colNamesToDelimFileHeader(row,message.getProperty("col.delimiter", ","));
									results.add(csvHeader);
								}
								results.add(BSCJDBCUtils.mapJDBCResponse(row, responseFormat,message.getProperty("col.delimiter", ",")));
								return results;
							}
						});
					}						
				
					outMsg = message.cloneMessage(false);
					outMsg.loadMessage(BSCJDBCUtils.constructJDBCResponse(results, responseFormat));	
				} catch (BSCMessageException e) {
					message.setException(e);
					logger.error("Unable to execute SQL query - {}", e.toString());
					throw new BSCDestinationException(this, BSCErrorLevel.ROLLBACK,"Destination exception occurred during response creation: "+e.getMessage());
				} catch(Exception e){
					message.setException(e);
					logger.error("Unable to execute SQL query - {}", e.toString());
					throw new BSCDestinationException(this, BSCErrorLevel.ROLLBACK,"Unable to execute query. A runtime destination exception occurred "+e.getMessage());
				}
				
				break;
				
				case JDBC_INSERT:
					logger.info("Executing JDBC INSERT ");
					
					try {
						jdbcTemplate.setQueryTimeout(100);
						jdbcTemplate.update(query);
						outMsg = message.cloneMessage(false);
						outMsg.loadMessage("Query executed successfully");
					} catch (Exception | BSCMessageException e) {
						logger.error("Unable to execute SQL query - {}", e.toString());
						throw new BSCDestinationException(this, e, BSCErrorLevel.CRITICAL,"Unable to execute SQL query - " + e.toString());
					}
				break;
				default:
					throw new BSCDestinationException(this, BSCErrorLevel.CRITICAL,"Unspecified destination action ");				
	
			}		
			
			
		}else{
			throw new BSCDestinationException(this, BSCErrorLevel.CRITICAL,"Unspecified destination action ");
		}
		
		
		return outMsg;

	}
	
	
	public  JdbcTemplate createJdbcTemplate() {
	    
		DataSource ds = ((BSCJDBCDataSourceResource)this.getService().getServiceContainer().getApplicationService().getResource(this.connection)).getDataSource();
		
	    jdbcTemplate.setDataSource(ds);
	    
	    return jdbcTemplate;
	     
	}
	
	
	@Override
	public List<BSCMessage> onSend(List<BSCMessage> message) throws BSCDestinationException {
		return null;
	}


}
