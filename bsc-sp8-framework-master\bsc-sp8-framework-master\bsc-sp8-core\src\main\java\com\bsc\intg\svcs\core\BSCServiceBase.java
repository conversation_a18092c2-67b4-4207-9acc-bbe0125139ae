package com.bsc.intg.svcs.core;

import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCMessage;

public abstract class BSCServiceBase extends BSCComponentBase implements BSCService,BSCServiceProvider {
	
	protected boolean valid = true;
	
	public BSCServiceBase(BSCComponentType componentType ,BSCComponent parentComponent, String configName, String instance ) {
		super(componentType, parentComponent, configName, instance);
	}

	public void initialize() throws BSCComponentInitializationException  {
		loadParentProperties();
	}		
	
	public BSCServiceContainer getServiceContainer() {
		return (BSCServiceContainer)parentComponent;
	}
	
	@Override
	public BSCMessage execute(BSCMessage message) throws BSCServiceException {
		
		try {
			logger.info("Invoking the service call");
			return service(message);

		} catch (BSCServiceException e) {
			logger.error("Service call execution failed", e);
			throw e;
		}
	}
	
	
	@Override
	public void reset() {
		
	}

	@Override
	public boolean isValid() {
		return valid;
	}

	
}
