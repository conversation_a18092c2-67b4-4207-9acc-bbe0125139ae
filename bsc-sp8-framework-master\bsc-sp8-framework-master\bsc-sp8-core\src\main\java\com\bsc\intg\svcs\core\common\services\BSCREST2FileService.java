package com.bsc.intg.svcs.core.common.services;

import java.io.File;
import java.net.URL;
import java.util.Base64;
import java.util.Map;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestination;
import com.bsc.intg.svcs.core.BSCIntegrationServiceBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCPropertyHelper;

public class BSCREST2FileService extends BSCIntegrationServiceBase {

	protected BSCDestination filePublish = null;
	BSCMessage outMessage = null;
	String archivedpath = null;

	public BSCREST2FileService(BSCComponentType componentType, BSCComponent parentComponent, String configName,
			String instance) {
		super(componentType, parentComponent, configName, instance);
	}

	@Override
	public BSCMessage service(BSCMessage inMessage) throws BSCServiceException {

		// BSCMessage outMessage = null;

		try {

			outMessage = inMessage.cloneMessage(false);
			String application = null;
			String messageType = null;

			inMessage.getProperties().forEach((k, v) -> logger.debug("Message properties are {}={}", k, (String) v));
			URL url = new URL(inMessage.getProperty("HTTP_REQUEST_URI"));
			String queryParameter = url.getQuery();
			logger.info("Query is {}", queryParameter);

			if ((inMessage.getProperty("HTTP_REQUEST_URI").contains("File/Publish"))) {
				byte[] bytes = Base64.getDecoder().decode(inMessage.getStringMessage());
				outMessage.loadMessage(bytes);
			}

			else if (inMessage.getProperty("HTTP_REQUEST_URI").contains("Publish")) {
				outMessage.loadMessage(inMessage.getStringMessage());
			}

			application = inMessage.getProperty("JS.SourceApp");
			messageType = inMessage.getProperty("JS.MessageType");

			if (application == null || messageType == null) {
				logger.info("Mandatory Parameters { x-bsc-application, x-bsc-messagetype } are missing in request");
				throw new BSCServiceException(this, null, BSCErrorLevel.FATAL,
						"Mandatory Parameters { x-bsc-application, x-bsc-messagetype } are missing in request");
			}

			logger.info("application : {} and messageType : {}", application, messageType);
			String partner = application + "_" + messageType;

			String consumerenv = inMessage.getProperty("HTTP.x-bsc-env",
					inMessage.getProperty("JS.ConsumerEnvironemnt", null));

			if (consumerenv != null) {
				logger.info("Publisher requested to publish to specific env : {}  ", consumerenv);
				partner = partner + "_" + consumerenv;
			}

			partner = partner.toLowerCase();
			logger.info("Fetching script corresponding to {}", partner);
			String script = System.getProperty(partner + ".script");
			// Execute the JS script if any
			if (script == null) {
				logger.info("Properties corresponding to : " + partner + " are missing ");
				throw new BSCServiceException(this, null, BSCErrorLevel.FATAL,
						"Properties corresponding to : " + partner + " are missing in request");
			}

			logger.info("Executing Script : {} ", script);
			inMessage.executeScript(script);
			logger.info("Script executed successfully");

			String outputFileName = inMessage.getProperty("HTTP.x-bsc-filename",
					inMessage.getProperty("JS.FileName", inMessage.getProperty("OUTPUT.FILE.NAME", "NameNotSupplied")));

			outMessage.setProperty("VFS.CONTENT", "true");

			// Read all output path from property
			Map<String, Object> outputPaths = BSCPropertyHelper.getPropertiesStartingWith(inMessage.getProperties(),
					"OUTPUT.FILE.PATH", true);

			outputPaths.forEach((k, v) -> {
				try {
					logger.debug(" {}={}", k, v);
					String outputFilePath = v.toString();
					if (outputFilePath.contains("/env/") || outputFilePath.contains("/ENV/")) {
						outputFilePath.replace("/env/",
								"/" + inMessage.getProperty("JS.Environment").toLowerCase() + "/");
						outputFilePath.replace("/ENV/", "/" + inMessage.getProperty("JS.Environment") + "/");
					}

					// create output path if not available
					new File(outputFilePath).mkdir();
					outMessage.setProperty("VFS.FILE.NAME", outputFilePath + outputFileName);
					archivedpath = outputFilePath + outputFileName;
					logger.info("outputFilePath is {} outputFileName is {} ", outputFilePath, outputFileName);
					filePublish.send(outMessage);
				} catch (BSCDestinationException e) {

					inMessage.setException(e);
					try {
						throw new BSCServiceException(this, e, BSCErrorLevel.ROLLBACK,
								"Failed to publish message to destination due to " + e.getMessage());
					} catch (BSCServiceException e1) {
						// TODO Auto-generated catch block
						e1.printStackTrace();
					}

				}
			});

			outMessage = inMessage.cloneMessage(false);
			outMessage.loadMessage("Message/File is archived successfully at " + archivedpath);
			// 201 need to be sent

		} catch (BSCMessageException | BSCServiceException | Exception e) {
			inMessage.setException(e);
			throw new BSCServiceException(this, e, BSCErrorLevel.ROLLBACK,
					"Failed to Archive due to " + e.getMessage());
		}
		return outMessage;
	}

	@Override
	public void initialize() throws BSCComponentInitializationException {

		super.initialize();
		filePublish = this.getDestination("FilePublish");
	}
}
