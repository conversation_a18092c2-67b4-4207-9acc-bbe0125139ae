package com.bsc.intg.svcs.core;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.output.ByteArrayOutputStream;

import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCCryptoHelper;
//import com.bsc.intg.svcs.crypto.BCPGPDecryptor;
import com.bsc.intg.svcs.crypto.BCPGPEncryptor;

public  abstract class BSCDestinationBase extends BSCComponentBase implements BSCDestination,BSCDestinationProvider {

	protected boolean 	primary 				=  true; 
	protected boolean 	mandatory 				=  true;
	protected boolean 	encrypt 				=  false;
	
	
	
	protected boolean preScriptEnabled 		= false; 
	protected boolean postScriptEnabled		= false;
	protected boolean errorScriptEnabled	= false;

	protected String 	preScript				= null;
	protected String 	postScript				= null;
	protected String 	errorScript				= null;
	
	protected String privateKey = getProperty("dec.private.key");
	protected String signingKey = getProperty("dec.sign.verify.key");
	protected String decryptionPassword = getProperty("dec.private.key.password");
	
	
	
	public BSCDestinationBase(BSCComponentType componentType ,BSCComponent parentComponent, String configName) {
		super(componentType, parentComponent, configName);
	}
	
	public BSCService getService() {
		return (BSCService)parentComponent;
	}

	public void initialize() throws BSCComponentInitializationException {
		
		logger.info("Loading the destination properties from the Service");
		
		loadParentProperties();
		
		primary= getBooleanProperty("primary",true);
		mandatory=getBooleanProperty("mandatory",true);
		
		encrypt=getBooleanProperty("encrypt",false);		
		
		preScript				=   getProperty("pre.script","");
		postScript				=   getProperty("post.script","");
		errorScript				=   getProperty("error.script","");
		
		if(!preScript.isEmpty()) 		preScriptEnabled=true;
		if(!postScript.isEmpty()) 		postScriptEnabled=true;
		if(!errorScript.isEmpty()) 		errorScriptEnabled=true;
		
	}
	
		@SuppressWarnings("restriction")
	public BSCMessage encrypt(BSCMessage message)throws BSCDestinationException{
		
		BSCMessage encryptedMessage = null;
		try{
			encryptedMessage = message.cloneMessage(false);
			BCPGPEncryptor encryptor = new BCPGPEncryptor();
		String encryptionKey = getProperty("enc.encrypt.key");
		String signingKey = getProperty("enc.sign.key");
		

		if (encryptionKey == null) {
			if (signingKey == null) {
				throw new BSCDestinationException(this, null, BSCErrorLevel.CONTINUE,
						"No encryption or signingn key available");
			}
		}

		String password = getProperty("enc.sign.key.password");
		
		Boolean signingNeeded = false;
		logger.info("Fetched encryption properties ");
		if (signingKey!=null && signingKey.length() > 5) {
			signingNeeded = true;
		}
		
		ByteArrayInputStream fileInputStream = new ByteArrayInputStream(message.toByteArray());
		ByteArrayOutputStream fileOutStream = new ByteArrayOutputStream();

		// setting encryptor parameters
		encryptor.setArmored(false);
		encryptor.setCheckIntegrity(false);
		encryptor.setPublicKeyFilePath(encryptionKey);
		encryptor.setSigning(signingNeeded);
		encryptor.setSigningPrivateKeyFilePath(signingKey);
		if(password !=null && !password.isEmpty())
			encryptor.setSigningPrivateKeyPassword(BSCCryptoHelper.getSecret(password));

		logger.info("Calling Encrypt function");
		encryptor.encryptFile("", fileInputStream, fileOutStream);
		encryptedMessage.loadMessage(fileOutStream.toByteArray());
		logger.info("Encryption is done ");
		} catch (BSCDestinationException | Exception | BSCMessageException e) {
			
				throw new BSCDestinationException(this,BSCErrorLevel.ROLLBACK,"Failed to encrypt file.");
			
		}
		
		return encryptedMessage ;
	}

	
	

	@Override
	public BSCMessage send(BSCMessage message) throws BSCDestinationException {
		
		BSCMessage response=null;
		
		if (message == null ) {
			throw new BSCDestinationException(this,BSCErrorLevel.CRITICAL, "Null message object received"); 
		}
		
		try {
			if( preScriptEnabled && message != null ) {
				logger.info("Executing the pre-script");
				message.executeScript(preScript);
			}
		} catch (BSCServiceException ee) {
			logger.error("Failed to execute pre-script",ee);
			throw new BSCDestinationException(this,ee, BSCErrorLevel.CRITICAL, "Failed to execute pre-script"); 

		}
		
		try {
			
			if(encrypt)
				message=encrypt(message);
			
			response=onSend(message);
			
			try {
				
				if( postScriptEnabled && message != null ) {
					logger.info("Executing the post-script");
					message.executeScript(postScript);
				}
				
			} catch (BSCServiceException ee) {

				logger.error("Failed to execute post-script",ee);
				throw new BSCDestinationException(this,ee, BSCErrorLevel.CRITICAL, "Failed to execute post-script"); 

			}
			
		} catch (BSCDestinationException e) {
			
			try {
				if( errorScriptEnabled && message != null ) {
					logger.info("Executing the error script");
					message.executeScript(errorScript);
				}	
			} catch (BSCServiceException ee) {
				
				logger.error("Failed to execute error script",ee);
				throw new BSCDestinationException(this,ee, BSCErrorLevel.CRITICAL, "Failed to execute error script"); 

			}
			
			if(mandatory) {
				throw e;
			} else {
				logger.warn("Exception occured while invoking the send call, destination is not mandatory, continuing");
			}
		} 
		
		return response;
		
	}

	
	@Override
	public List<BSCMessage> send(List<BSCMessage> messages) throws BSCDestinationException {
		
		List<BSCMessage> responses=null;
		
		try {
			if( preScriptEnabled && messages != null ) {
				logger.info("Executing the pre-script");
				for (BSCMessage m : messages)
					m.executeScript(preScript);
			}
		} catch (BSCServiceException ee) {
			logger.error("Failed to execute pre-script",ee);
		}
		
		try {
			
			responses=onSend(messages);
			
			try {
				
				if( postScriptEnabled && messages != null ) {
					logger.info("Executing the post-script");
					for (BSCMessage message : messages)
						message.executeScript(postScript);
				}
				
			} catch (BSCServiceException ee) {
				logger.error("Failed to execute post-script",ee);
			}
			
		} catch (BSCDestinationException e) {
			
			try {
				if( errorScriptEnabled && messages != null ) {
					logger.info("Executing the error script");
					for (BSCMessage message : messages)
						message.executeScript(errorScript);
				}	
			} catch (BSCServiceException ee) {
				logger.error("Failed to execute error script",ee);
			}
			
			if(mandatory) {
				throw e;
			} else {
				logger.warn("Exception occured while invoking the send call, destination is not mandatory, continuing");
			}
		} 
		
		return responses;
		
	}
	
	
	
}
