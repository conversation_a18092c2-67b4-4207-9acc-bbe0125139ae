package com.bsc.intg.svcs.core.common.services;

import org.json.JSONObject;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestination;
import com.bsc.intg.svcs.core.BSCIntegrationServiceBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCCryptoHelper;

public class BSCJWTAuthService extends BSCIntegrationServiceBase {

	public BSCJWTAuthService(BSCComponentType componentType, BSCComponent parentComponent, String configName,
			String instance) {
		super(componentType, parentComponent, configName, instance);
	}

	private static final String AUTHORIZATION_HEADER = "REQUEST.HTTP.HEADER.Authorization";
	protected BSCDestination jwtDestination;

	@Override
	public BSCMessage service(BSCMessage inMessage) throws BSCServiceException {
		logger.debug("Executing BSCJWTService");
		try {
			if (this.getProperty("reset.session", "false").equals("true"))
				logout();
			return login(inMessage);
		} catch (BSCDestinationException e) {
			throw new BSCServiceException(this, BSCErrorLevel.CRITICAL,
					"Failed to authenticate the message in BSCOAuthService");
		}

	}

	/**
	 * Initializes all the resources of the service
	 * 
	 * @throws BSCComponentInitializationException
	 *             if any exception occurs during initialization of the
	 *             components or resources of the service.
	 */
	@Override
	public void initialize() throws BSCComponentInitializationException {
		super.initialize();
		valid = true;
		String dest = this.getProperty("jwt.dest");
		jwtDestination = this.getDestination(dest);

	}

	public BSCMessage login(BSCMessage message) throws BSCDestinationException {

		logger.info("Executing login using JWT - ");
		String jwtToken = this.getProperty("jwt.token", "");

		if (jwtToken.isEmpty()) {
			BSCMessage authRespMsg = null;
			BSCMessage loginMessage = null;
			try {

				loginMessage = createJWTRequest(message);
				authRespMsg = jwtDestination.send(loginMessage);
				logger.info("JWT Authorization response recieved successfully");
				String statusCode = authRespMsg.getProperty("RESPONSE.HTTP.HEADER.STATUSCODE");
				if (!statusCode.startsWith("2")) {
					logger.info("Response: " + authRespMsg.getStringMessage());

					throw new BSCDestinationException(this, BSCErrorLevel.CONTINUE,
							"Failed to login using OAUTH ERROR CODE - " + statusCode + " ERROR REASON - "
									+ authRespMsg.getProperty("RESPONSE.HTTP.HEADER.STATUS"));
				}
				processJWTResponse(authRespMsg);
				jwtToken = this.getProperty("jwt.token", "");

			} catch (BSCDestinationException | BSCMessageException e) {
				logger.error("Error occured while trying to login and fetch JWT Token", e);
				throw new BSCDestinationException(this, e, BSCErrorLevel.CONTINUE,
						"Error occured while trying to login and fetch JWT Token");
			} finally {
				if (loginMessage != null)
					loginMessage.close();
				if (authRespMsg != null)
					authRespMsg.close();
			}
		}

		message.setProperty(AUTHORIZATION_HEADER, "Bearer " + jwtToken);
		return message;

	}

	public BSCMessage createJWTRequest(BSCMessage inMessage) throws BSCDestinationException, BSCMessageException {

		BSCMessage loginRequest = inMessage.cloneMessage(false);
		String jwtUsername = this.getProperty("jwt.username");
		String jwtPassword = this.getProperty("jwt.password");
		String requestMethod = this.getProperty("jwt.requestmethod");
		String contentType = this.getProperty("jwt.contenttype");

		if (jwtPassword != null && jwtPassword.startsWith("ss://")) {
			try {
				jwtPassword = BSCCryptoHelper.getSecret(jwtPassword);
			} catch (Exception e) {
				this.getLogger().error("Error decrypting the password");
				throw new BSCDestinationException(this, e, BSCErrorLevel.FATAL,
						"Authentication Failed " + e.toString());
			}
		}
		String jwtUsernameKey = this.getProperty("jwt.jwtusernamekey");
		String jwtPasswordKey = this.getProperty("jwt.jwtpasswordkey");

		JSONObject jsonrequest = new JSONObject();
		jsonrequest.put(jwtUsernameKey, jwtUsername);
		jsonrequest.put(jwtPasswordKey, jwtPassword);
		loginRequest.loadMessage(jsonrequest.toString());
		loginRequest.setProperty("REQUEST.HTTP.METHOD", requestMethod);
		loginRequest.setProperty("REQUEST.HTTP.HEADER.Content-Type", contentType);
		loginRequest.setProperty("REQUEST.HTTP.HEADER.connection", "Keep-Alive");

		return loginRequest;

	}

	private void processJWTResponse(BSCMessage responseMessage) throws BSCMessageException, BSCDestinationException {

		JSONObject responseJson = new JSONObject(responseMessage.getStringMessage());

		String jwtToken = responseJson.optString("access_token");
		if (jwtToken == null || jwtToken.isEmpty()) {
			String statusCode = responseMessage.getProperty("RESPONSE.HTTP.HEADER.STATUSCODE");

			throw new BSCDestinationException(this, BSCErrorLevel.CONTINUE, "access_token is not presnt in JWT response"
					+ statusCode + " ERROR REASON - " + responseMessage.getProperty("RESPONSE.HTTP.HEADER.STATUS"));
		} else {
			this.setProperty("jwt.token", jwtToken);
		}
	}

	/**
	 * Resets JWT Token.
	 *
	 */

	public void logout() {

		logger.info("Logging out - ");
		this.setProperty("jwt.token", "");
		this.setProperty("reset.session", "false");
		logger.info("Logged out successfully - ");

	}

}
