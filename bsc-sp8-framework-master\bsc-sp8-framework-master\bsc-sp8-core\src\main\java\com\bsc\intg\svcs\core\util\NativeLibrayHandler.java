package com.bsc.intg.svcs.core.util;



import java.io.*;
import java.nio.file.FileSystemNotFoundException;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.ProviderNotFoundException;
import java.nio.file.StandardCopyOption;
import java.util.StringTokenizer;

/**

 */
public class NativeLibrayHandler {
 
    /**
     * The minimum length a prefix for a file has to have according to {@link File#createTempFile(String, String)}}.
     */
    private static final int MIN_PREFIX_LENGTH = 3;
    public static final String NATIVE_FOLDER_PATH_PREFIX = "sp8";

    /**
     * Temporary directory which will contain the DLLs.
     */
    private static File temporaryDir;

    /**
     * Private constructor - this class will never be instanced
     */
    private NativeLibrayHandler() {
    }

    /**
     * Loads library from current JAR archive
     * 
     * The file from JAR is copied into system temporary directory and then loaded. The temporary file is deleted after
     * exiting.
     * Method uses String as filename because the pathname is "abstract", not system-dependent.
     * 
     * @param path The path of file inside JAR as absolute path (beginning with '/'), e.g. /package/File.ext
     * @throws IOException If temporary file creation or read/write operation fails
     * @throws IllegalArgumentException If source file (param path) does not exist
     * @throws IllegalArgumentException If the path is not absolute or if the filename is shorter than three characters
     * (restriction of {@link File#createTempFile(java.lang.String, java.lang.String)}).
     * @throws FileNotFoundException If the file could not be found inside the JAR.
     */
    public static void loadLibrariesFromJar(String path, String libraries,String prefix, String ext) throws IOException {
 
    	
        if (temporaryDir == null) {
            temporaryDir = createTempDirectory(NATIVE_FOLDER_PATH_PREFIX);
            temporaryDir.deleteOnExit();
        }

		String existingLibraryPath = System.getProperty("java.library.path","");
		OSCheck.OSType ostype=OSCheck.getOperatingSystemType();
		switch (ostype) {
		    
			case Windows: 
				
				if ( !existingLibraryPath.equals("") ) {
					System.setProperty("java.library.path", existingLibraryPath + ";" + temporaryDir.toString());
					System.out.println("Setting the java.library.path to " + System.getProperty("java.library.path"));
				} else {
					System.setProperty("java.library.path",   temporaryDir.toString());
					System.out.println("Setting the java.library.path to " + System.getProperty("java.library.path"));
				}			
				break;
					
		    case Linux: 
				
		    	if ( !existingLibraryPath.equals("") ) {
		   		System.setProperty("java.library.path", existingLibraryPath + ":" + temporaryDir.toString());
					System.out.println("Setting the java.library.path to " + System.getProperty("java.library.path"));
		    	} else {
		    		
					System.setProperty("java.library.path",   temporaryDir.toString());
					System.out.println("Setting the java.library.path to " + System.getProperty("java.library.path"));
		    	}			
				
		    	break;
		    	
		    case Other: 
		    	break;
		}
		 		

		
		StringTokenizer st=new StringTokenizer(libraries,",");
		
		 while (st.hasMoreTokens()) {
			 	
			 String filename = st.nextToken();
			
		        // Prepare temporary file
		
		        File temp = new File(temporaryDir, prefix+filename+ext);
		       
		        try (InputStream is = NativeLibrayHandler.class.getResourceAsStream(path+prefix+filename+ext)) {
		            Files.copy(is, temp.toPath(), StandardCopyOption.REPLACE_EXISTING);
		        } catch (IOException e) {
		            temp.delete();
		            throw e;
		        } catch (NullPointerException e) {
		            temp.delete();
		            throw new FileNotFoundException("File " + path + " was not found inside JAR.");
		        }
		        
		        try {
		        	System.out.println("loading the library -" + temp.getAbsolutePath());
		            System.load(temp.getAbsolutePath());
		        } finally {
		            if (isPosixCompliant()) {
		               temp.deleteOnExit();
		            } else {
		               temp.deleteOnExit();
		            }
		        }
		        
		 }
        
    }

    private static boolean isPosixCompliant() {
        try {
            return FileSystems.getDefault()
                    .supportedFileAttributeViews()
                    .contains("posix");
        } catch (FileSystemNotFoundException
                | ProviderNotFoundException
                | SecurityException e) {
            return false;
        }
    }

    private static File createTempDirectory(String prefix) throws IOException {
        
    	String tempDir = System.getProperty("sp.tmp.dir",System.getProperty("java.io.tmpdir"));
        
        File generatedDir = new File(tempDir);
        
        if (!generatedDir.exists()) {
        	if (!generatedDir.mkdir())
        		throw new IOException("Failed to create temp directory " + generatedDir.getName());
        }
        
        return generatedDir;
    }
}