package com.bsc.intg.svcs.core.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Arrays;

public class BSCStringUtils {
	
	public static ArrayList<String> convertCommaSeperated(String commaSeperated) {
		
		if ( commaSeperated != null && !commaSeperated.trim().equals("")) {
			return new ArrayList<String>(Arrays.asList(commaSeperated.trim().split("\\s*,\\s*")));
		} else {
			return new ArrayList<String>();
		}
	}
	
	public static String getStringFromInputStream(InputStream is) {

		BufferedReader br = null;
		StringBuilder sb = new StringBuilder();

		String line;
		try {

			br = new BufferedReader(new InputStreamReader(is));
			while ((line = br.readLine()) != null) {
				sb.append(line);
				sb.append(System.getProperty("line.separator"));
			}

		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			if (br != null) {
				try {
					br.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}

		return sb.toString();

	}
}
