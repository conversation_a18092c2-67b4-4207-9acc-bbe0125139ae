package com.bsc.intg.svcs.core.message;

import java.io.Serializable;

public class BSCModelSpec implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;


	protected Object object;
	protected Class<?> objectClass;
	protected String [] keys;
	protected String name;
	
	public BSCModelSpec(String name, Class<?> objectClass, Object object,  String[] keys) {
		this.object=object;
		this.objectClass=objectClass;
		this.keys=keys;
		this.name=name;
	}
	
	public Object getObject() {
		return object;
	}
	public void setObject(Object object) {
		this.object = object;
	}
	public Class<?> getObjectClass() {
		return objectClass;
	}
	public void setObjectClass(Class<?> objectClass) {
		this.objectClass = objectClass;
	}
	public String[] getKeys() {
		return keys;
	}
	public void setKeys(String[] keys) {
		this.keys = keys;
	}
	
	
}
