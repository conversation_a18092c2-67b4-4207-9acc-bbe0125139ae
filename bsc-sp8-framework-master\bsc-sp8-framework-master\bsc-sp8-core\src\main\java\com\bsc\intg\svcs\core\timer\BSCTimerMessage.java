package com.bsc.intg.svcs.core.timer;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.Map;

public class BSCTimerMessage extends ByteArrayOutputStream {

	Map<String, Object> properties = new LinkedHashMap<String, Object>();


	
	public void loadFileContent(byte[] data) {
		try {
			this.write(data);
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	public void setProperty(String key, String value) {
		properties.put(key, value);
	}

	public String getProperty(String key) {
		return (String) properties.get(key);
	}


	public Map<String, Object> getProperties() {
		return this.properties;
	}

	public void setObjectProperty(String key, Object value) {
		properties.put(key, value);
	}

}
