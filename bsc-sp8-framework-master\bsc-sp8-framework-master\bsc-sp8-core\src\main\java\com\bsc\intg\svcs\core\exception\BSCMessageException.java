package com.bsc.intg.svcs.core.exception;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.message.BSCMessage;

public class BSCMessageException extends BSCExceptionBase {


	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;


	public BSCMessageException(BSCComponent source,Throwable ex,BSCErrorLevel l, String message, BSCMessage messageObj) {
		super(source,ex,l, message,messageObj);
	};

	public BSCMessageException(BSCComponent source, BSCErrorLevel l, String message, BSCMessage messageObj) {
		super(source,l, message,messageObj);
	};
	
	
	public BSCMessageException(BSCComponent source, Throwable ex, BSCErrorLevel l, BSCMessage messageObj) {
		super(source, ex, l, messageObj);
	}
	

	
	
}
