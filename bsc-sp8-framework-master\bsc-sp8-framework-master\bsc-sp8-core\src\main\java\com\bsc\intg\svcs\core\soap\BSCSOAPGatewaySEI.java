package com.bsc.intg.svcs.core.soap;

import javax.xml.transform.Source;
import javax.xml.ws.Provider;
import javax.xml.ws.Service;
import javax.xml.ws.ServiceMode;
import javax.xml.ws.WebServiceProvider;

/**
 * A simple Provider-based web service implementation.
 *
 * <AUTHOR> (c) 2010, Oracle and/or its affiliates. 
 * All Rights Reserved.
 */
// The @ServiceMode annotation specifies whether the Provider instance 
// receives entire messages or message payloads.
@ServiceMode(value = Service.Mode.PAYLOAD)

// Standard JWS annotation that configures the Provider-based web service.
@WebServiceProvider()
public interface BSCSOAPGatewaySEI extends Provider<Source>{
 
  //Invokes an operation according to the contents of the request message.
  public Source invoke(Source source);
//   
 
}
