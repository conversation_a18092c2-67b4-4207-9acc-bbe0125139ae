package com.bsc.intg.svcs.core.exception;

import com.bsc.intg.svcs.core.BSCComponent;

public class BSCComponentInitializationException extends BSCExceptionBase {


	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;


	public BSCComponentInitializationException(BSCComponent source,Throwable ex,BSCErrorLevel l, String message) {
		super(source, ex,l,message);
	};

	public BSCComponentInitializationException(BSCComponent source,BSCErrorLevel l,  String message) {
		super(source, l, message);
	};
	
	public BSCComponentInitializationException(BSCComponent source,Throwable ex,BSCErrorLevel l) {
		super(source,ex,l);
	}


	
}
