package com.bsc.intg.svcs.core.jms;

import javax.jms.ConnectionFactory;
import javax.jms.Destination;
import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageListener;

import org.springframework.jms.listener.DefaultMessageListenerContainer;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentSubType;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCListenerBase;
import com.bsc.intg.svcs.core.BSCService;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCPropertyHelper;

public class BSCJMSListener extends BSCListenerBase implements MessageListener {

	protected 		String 			destinationName		=  null;
	protected 		String 			destinationType		=  null;
	
	private DefaultMessageListenerContainer dmlc;
	
	protected String 	connection  				=  	null;
	protected int 		minThreads					=	1; 
	protected int		maxThreads					=	1;
	private long 		rollbackWait  				=   0;
	private int 		rollbackErrorFrequency		=	1;
	protected 	String 	selector  					=  	null;


	
	public BSCJMSListener(BSCComponentType componentType, BSCComponent parentComponent, String configName) {
		super(componentType, parentComponent, configName);
	}

	
	public void initialize() throws BSCComponentInitializationException {
		
		super.initialize(BSCComponentSubType.JMS_LISTENER);
		
		destinationName			= getNonEmptyProperty("destination.name");
		destinationType			= getProperty("destination.type", "queue");
		
		connection   			= getNonEmptyProperty("connection");
		minThreads   			= getIntProperty("min.threads",1);
		maxThreads				= getIntProperty("max.threads",1);
		rollbackWait			= getIntProperty("rollback.wait", 30000);
		rollbackErrorFrequency	= getIntProperty("rollback.error.frequency", 1);
		selector				= getProperty("selector", "");

		
		try {
			dmlc=createDMLC();
		} catch (Exception e) {
			throw new BSCComponentInitializationException(this, e, BSCErrorLevel.CRITICAL, "Failed to initialize the destination " + getInstanceName() );
		}
		
		logger.info("Binding the message listener to JMS Default Containter");
		dmlc.setMessageListener(this);
	
	}
	
		
	@Override
	public void onMessage(Message jmsMessage)  {
		
		BSCMessage message=null;
		int deliveryCount = 0;

			try {
				
				if(!scriptServiceEnabled) {
					scriptServiceName=null;
				}

				try{
					
					logger.info("Creating the message for the services MessageId - ({}) Timestamp -({})", jmsMessage.getJMSMessageID(), jmsMessage.getJMSTimestamp() );
					message = new BSCMessage(this, eventServiceName, scriptServiceName);
					message.addProperties(this.getProperties(), true, this.getConfigName());
					message.loadMessage(jmsMessage);
					message.copyJMSPropertiesFrom(jmsMessage, (Object[]) null);
					
					
					String deliveryCountValue=message.getProperty("JMS.USER.JMSXDeliveryCount");
					
					if(deliveryCountValue!=null) {
						try{
							deliveryCount=Integer.parseInt(deliveryCountValue);
						} catch (NumberFormatException e) {
							logger.warn("Invalid delivery count property value from JMS, rollback delay might not wok");
						}
					}
	
					message.setProperty("CONTEXT.MESSAGE.ID", jmsMessage.getJMSMessageID() );
					message.setProperty("CONTEXT.MESSAGE.TIME", String.valueOf(jmsMessage.getJMSTimestamp()) );
					message.setProperty("CONTEXT.MESSAGE.OWNER.NAME", this.configName);
					message.setProperty("CONTEXT.MESSAGE.OWNER.INSTANCE", this.instanceName);

					BSCPropertyHelper.printMessageContext(message, logger);
					
				} catch (BSCMessageException | JMSException e) {
					logger.error("Failed to load/parse JMS message",e);
					throw new BSCServiceException(this,e,BSCErrorLevel.CONTINUE);
					
				} 
				
				if (deliveryCount>1)
					message.disableEvent();
				
				onMessage(message);
			
			} catch (BSCServiceException e) {
			
				if ( e.getLevel() == BSCErrorLevel.ROLLBACK ) {
					logger.error("Rolling back the message to the queue",e);
					
					if (deliveryCount>1) {
						try {
							Thread.sleep(this.rollbackWait);
						} catch (InterruptedException e1) {
							logger.error("Error occured while snoozing during rollback");
						}
					}
					
					if (deliveryCount == 1 ) {
						message.setException(e);
						message.generateEvent(this.errorEventName);
						message.clearException();
						
					} else {
					
						if ( deliveryCount % rollbackErrorFrequency == 0 ) {
							message.enableEvent();
							message.setException(e);
							message.generateEvent(this.errorEventName);
							message.clearException();
						} 
					}
					
					throw new RuntimeException("Rollback the transaction");
				} else {
					logger.error("Error occurred while invoking the service, continuing with the next message", e);
					message.setException(e);
					message.generateEvent(this.errorEventName);
					message.clearException();
				}
				
			} finally {
	
				if( message != null) {
					message.close();
					message = null;
				}	
		
			}
		
		
	}
	


	public DefaultMessageListenerContainer createDMLC() throws Exception {
    	

		ConnectionFactory cf = ((BSCJMSConnectionResource)this.getServiceContainer().getApplicationService().getResource(this.connection)).getJMSConnectionFactory(); 
		Destination destination = ((BSCJMSConnectionResource)this.getServiceContainer().getApplicationService().getResource( this.connection)).getJMSDestination(this.destinationName,this.destinationType);
	    
    	DefaultMessageListenerContainer dmlc=new DefaultMessageListenerContainer();
		
    	dmlc.setConnectionFactory(cf);
    	
    	dmlc.setDestination(destination);
    	dmlc.setAutoStartup(false);
    	dmlc.setSessionTransacted(true);
    	dmlc.setSessionAcknowledgeMode(1);
    	dmlc.setReceiveTimeout(5000);
    	dmlc.setRecoveryInterval(5000);
    	dmlc.setConcurrency("1");
    	dmlc.setMessageListener(this);
    	dmlc.setConcurrentConsumers(minThreads);
    	dmlc.setMaxConcurrentConsumers(maxThreads);
    	dmlc.setBeanName(destinationName);
    	
    	if(!selector.equals("") ){
    		dmlc.setMessageSelector(selector);
    	}

    	
    	dmlc.initialize();
    	
    	return dmlc;
    }

	@Override
	public void start() {
		dmlc.start();
	}

	@Override
	public void stop() {
		dmlc.shutdown();
	}



	
	
}
