package com.bsc.intg.svcs.core.dfs;

/**
 * 
 * DFSServiceFactory class constructs concrete DFSService object and returns as singleton instance or separate instance per request 
 * depending upon the underlying locking framework
 * 
 */

public class DFSServiceFactory {

	public static DFSService getDFSService(DFSServiceType dfsType ) {
		
		DFSService dfsService = null; 
		
		switch (dfsType) {
			
			case ZOOKEEPER:
				System.out.println("Returning DFS service Zookeeper");
				dfsService = ZooKeeperDFSService.getInstance();
				
				break;
				
			case NFS:
				System.out.println("Returning DFS service NFS");
				dfsService = NFSDFSService.getInstance();
				
				break;
				
			default:
				System.out.println("Returning default DFS service Zookeeper");
				dfsService = ZooKeeperDFSService.getInstance();
				break;
		}
		
		return dfsService;
	}
	
	
	
	public static DFSService getDefaultDFSService() {
		System.out.println("Returning default DFS service Zookeeper");
		DFSService dsService = ZooKeeperDFSService.getInstance();
		return dsService;
	}

}
