package com.bsc.intg.svcs.core.vfs.aws.s3;

import org.apache.commons.logging.Log;
import org.apache.commons.vfs2.FileSystemException;
import org.apache.commons.vfs2.provider.VfsComponent;
import org.apache.commons.vfs2.provider.VfsComponentContext;

/**
 * A partial {@link VfsComponent} implementation.
 */
abstract class AbstractVfsComponent implements VfsComponent {

    private VfsComponentContext context;
    private Log log;

    /**
     * Sets the Logger to use for the component.
     *
     * @param log The Log to use.
     */
    @Override
    public final void setLogger(final Log log) {
        this.log = log;
    }

    /**
     * Sets the context for this file system provider.
     *
     * @param context The VfsComponentContext.
     */
    @Override
    public final void setContext(final VfsComponentContext context) {
        this.context = context;
    }

    /**
     * Initializes the component. This implementation does nothing.
     *
     * @throws FileSystemException if an error occurs.
     */
    @Override
    public void init() throws FileSystemException {
        // default is noop.
    }

    /**
     * Closes the provider. This implementation does nothing.
     */
    @Override
    public void close() {
        // default is noop.
    }

    /**
     * Returns the logger for this file system to use.
     *
     * @return logger for this file system
     */
    protected final Log getLogger() {
        return log;
    }

    /**
     * Returns the context for this provider.
     *
     * @return provider context
     */
    protected final VfsComponentContext getContext() {
        return context;
    }
}
