package com.bsc.intg.svcs.core;

import java.util.List;

import com.bsc.intg.svcs.core.exception.BSCServiceException;

public interface BSCServiceContainer extends BSCComponent {


	
	public BSCService getService(String serviceName) throws BSCServiceException;
	
	public void  releaseService(String serviceName, BSCService service) throws BSCServiceException;
	
	public BSCApplicationService getApplicationService();
	
	public List<String> getListenerNames() ;
	
	public BSCListener getListener(String name);
	
	public List<String> getControllerNames();
	
	public BSCController getController(String name) ;
	
	public List<String> getServiceNames() ;
}
