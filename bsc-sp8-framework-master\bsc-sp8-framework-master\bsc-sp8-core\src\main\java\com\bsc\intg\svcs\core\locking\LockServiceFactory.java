package com.bsc.intg.svcs.core.locking;

/**
 * 
 * LockingServiceFactory class constructs concrete LockService object and returns as singleton instance or separate instance per request 
 * depending upon the underlying locking framework
 * 
 * @see ZooLockService
 * 
 * <AUTHOR> Sircilla
 */

public class LockServiceFactory {

	public static LockService getLockService(LockServiceType lsType ) {
		
		LockService lockService = null; 
		
		switch (lsType) {
			
			case ZOOKEEPER:
				System.out.println("Returning locking service Zookeeper");
				lockService = ZooKeeperLockService.getInstance();
				
				break;
				
			default:
				System.out.println("Returning default locking service Zookeeper");
				lockService = ZooKeeperLockService.getInstance();
				break;
		}
		
		return lockService;
	}
	
	public static LockService getDefaultLockService() {
		System.out.println("Returning default locking service Zookeeper");
		LockService lockService = ZooKeeperLockService.getInstance();
		return lockService;
	}
	


}
