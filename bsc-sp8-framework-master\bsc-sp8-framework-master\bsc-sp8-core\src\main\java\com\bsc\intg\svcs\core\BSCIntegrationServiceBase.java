package com.bsc.intg.svcs.core;

import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.Map;

import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCPropertyHelper;
import com.bsc.intg.svcs.core.util.BSCStringUtils;

public abstract class BSCIntegrationServiceBase extends BSCServiceBase implements BSCIntegrationService {
	

	ArrayList<String> destinations = null;
	
	Map<String, BSCDestination> destinationMap = new LinkedHashMap<String,BSCDestination >();
	
	protected boolean scriptServiceEnabled 	= false;
	
	protected boolean preScriptEnabled 		= false; 
	protected boolean postScriptEnabled		= false;
	protected boolean errorScriptEnabled	= false;

	protected String 	preScript				= null;
	protected String 	postScript				= null;
	protected String 	errorScript				= null;

	protected String 	scriptServiceName		= null;
	protected String	eventServiceName		= null;
	protected	String 	errorEventName			= null;

	public BSCIntegrationServiceBase(BSCComponentType componentType ,BSCComponent parentComponent, String configName, String instance ) {
		super(componentType, parentComponent, configName, instance);
		
	}

	

	public void initialize() throws BSCComponentInitializationException  {
		
		super.initialize();
		
		scriptServiceName		=   this.getServiceContainer().getProperty("script.service.name","");
		scriptServiceEnabled	=	scriptServiceName.isEmpty() ? false : true ;
		
		eventServiceName		=  ((BSCServiceContainerBase)this.getServiceContainer()).getProperty("event.service.name");
		
		preScript				=   getProperty("pre.script","");
		postScript				=   getProperty("post.script","");
		errorScript				=   getProperty("error.script","");
		
		if(!preScript.isEmpty()) 	preScriptEnabled=true;
		if(!postScript.isEmpty()) 	postScriptEnabled=true;
		if(!errorScript.isEmpty()) 	errorScriptEnabled=true;
		
		errorEventName 		=   this.getConfigName() + ".Event.Error";
		
		String destinationNames		=   getProperty("destinations");
		
		destinations			=   BSCStringUtils.convertCommaSeperated(destinationNames);
		
		for (String destinationObjName : destinations) {
			
			BSCDestination destination = null;
			
			logger.info("Creating the destination {}", destinationObjName);
			
			String destinationClassName=getGlobalProperty(destinationObjName + ".class");
			
			try {
				Class<?> sc = Class.forName(destinationClassName);
				Constructor<?>	ctor=sc.getConstructor(BSCComponentType.class, BSCComponent.class, String.class );
				destination= (BSCDestination) ctor.newInstance(BSCComponentType.DESTINATION, this,destinationObjName);
			} catch (Exception e) {
				logger.error("Error creating the destination {}", destinationObjName,e);
				throw new BSCComponentInitializationException(this, e, BSCErrorLevel.CRITICAL, "Destination creation failed -" + destinationObjName);
			}
		
			logger.info("Initializing the destination {}", destinationObjName);
		
			

			destination.initialize();
			
			logger.info("Adding the destination {} to list", destinationObjName);
			
			destinationMap.put(destinationObjName, destination);
			
		}
}	
	public BSCDestination getDestination(String destinationObjName) {
		
		return destinationMap.get(destinationObjName);
	}
	
	@Override
	public BSCMessage execute(BSCMessage message) throws BSCServiceException {
		BSCMessage response = null;
		try {
				if (preScriptEnabled) message.executeScript(preScript);
			
				logger.info("Invoking the service call");
				response =  service(message);
			
				if(response != null && postScriptEnabled ) {
					response.executeScript(postScript);
				}
				return response;

		} catch (BSCServiceException e) {
			logger.error("Service call execution failed");
			throw e;
		}
	}
	
}
