function initializeMESProps(env){
	
	if(message.getProperty("JMS.USER.APP_Application")!=null&& message.getProperty("JMS.USER.EVENT_DestinationApp")!=null ){
	
	message.logDebug("Inside App and DestApp not null---------------------");
		if(message.getProperty("JMS.USER.APP_Application").equals("MES") || message.getProperty("JMS.USER.EVENT_DestinationApp").equals("MES")){
			SP("es.all.event.index","bsc_eai_"+env.toLowerCase()+"_all_events_mes_alias");
			SP("es.all.event.search.index","bsc_eai_"+env.toLowerCase()+"_all_events_mes_alias");
			
			SP("JMS.USER.es_all_event_index","bsc_eai_"+env.toLowerCase()+"_all_events_mes_alias");
			SP("JMS.USER.es_all_event_search_index","bsc_eai_"+env.toLowerCase()+"_all_events_mes_alias");
			
			
			if(message.getProperty("JMS.USER.APP_Application").equals("MES")){
			message.logDebug("App is MES-----------------------");
				SP("es.latest.event.index","bsc_eai_"+env.toLowerCase()+"_latest_events_mes_"+message.getProperty("JMS.USER.EVENT_SourceApp").toLowerCase()+"_alias");
				SP("JMS.USER.es_latest_event_index","bsc_eai_"+env.toLowerCase()+"_latest_events_mes_"+message.getProperty("JMS.USER.EVENT_SourceApp").toLowerCase()+"_alias");
				
			}
			if(message.getProperty("JMS.USER.EVENT_DestinationApp").equals("MES")){
				if(message.getProperty("JMS.USER.EVENT_IndexTag")!=null){
					message.logDebug("Event Index Tag is -----------------------");
					SP("es.latest.event.index","bsc_eai_"+env.toLowerCase()+"_latest_events_mes_"+message.getProperty("JMS.USER.EVENT_IndexTag").toLowerCase()+"_alias");
					SP("JMS.USER.es_latest_event_index","bsc_eai_"+env.toLowerCase()+"_latest_events_mes_"+message.getProperty("JMS.USER.EVENT_IndexTag").toLowerCase()+"_alias");
					
				}
			}				
	
		}
	
		if(message.getProperty("JMS.USER.EVENT_AppServer").equals("MESMI50BAPIGatewayService_v1")){
			SP("es.all.event.index","bsc_eai_"+env.toLowerCase()+"_all_events_mes_alias,bsc_eai_"+env.toLowerCase()+"_all_events_alias");
			SP("JMS.USER.es_all_event_index","bsc_eai_"+env.toLowerCase()+"_all_events_mes_alias,bsc_eai_"+env.toLowerCase()+"_all_events_alias");
			
		}
		
		if(message.getProperty("JMS.USER.EVENT_AppServer").equals("S4GatewayService_v1")){
			SP("es.all.event.index","bsc_eai_"+env.toLowerCase()+"_all_events_mes_alias,bsc_eai_"+env.toLowerCase()+"_all_events_alias");
			SP("JMS.USER.es_all_event_index","bsc_eai_"+env.toLowerCase()+"_all_events_mes_alias,bsc_eai_"+env.toLowerCase()+"_all_events_alias");
			
		}
	
	}
				
	SP("calculate.tnxtime","false");
	if((message.getProperty("JMS.USER.EVENT_AppServer").equals("MESMI50BAPIGatewayService_v1")||message.getProperty("JMS.USER.EVENT_AppServer").equals("S4GatewayService_v1")) && message.getProperty("JMS.USER.APP_Application").equals("MES")){
		SP("error.event.notification","false");
	}else{
	  	SP("error.event.notification","true");
	}
	
	if(message.getProperty("JMS.USER.EVENT_Object").contains("POListener")){
		SP("replay.ignore.event","true");
	}

}