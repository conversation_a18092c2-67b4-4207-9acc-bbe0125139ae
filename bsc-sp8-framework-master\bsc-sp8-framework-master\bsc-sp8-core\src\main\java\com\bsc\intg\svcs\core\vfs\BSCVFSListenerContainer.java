package com.bsc.intg.svcs.core.vfs;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.TimeZone;
import java.util.UUID;
import java.util.stream.Collectors;

import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveInputStream;
import org.apache.commons.io.IOUtils;
import org.apache.commons.vfs2.FileContent;
import org.apache.commons.vfs2.FileObject;
import org.apache.commons.vfs2.FileSystemException;
import org.apache.commons.vfs2.FileSystemOptions;
import org.slf4j.Logger;
import com.bsc.intg.svcs.core.boot.BSCScheduler;
import com.bsc.intg.svcs.core.boot.BSCSchedulerRuntime;
import com.bsc.intg.svcs.core.boot.BSCSchedulerTask;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.locking.LockService;
import com.bsc.intg.svcs.core.locking.LockServiceFactory;
import com.bsc.intg.svcs.core.locking.ServiceLock;
import com.bsc.intg.svcs.core.locking.ServiceLockException;
import com.bsc.intg.svcs.core.util.BSCVFSHelper;

public class BSCVFSListenerContainer implements BSCSchedulerTask  {
	
	
	private Logger 				logger 						= null;
	private BSCVFSListener 		fileListener 				=  null;
	private String 				cron 						= null;
	private String 				filePath 					= null;
	private String 				filePattern 				= null;
	private long 				fileAge 					= 120000;	
	private boolean				includeContent 				= false;
	private boolean				decompress 					= false;
	private boolean 			browse	 					= false;
	private boolean 			recursiveScan	 			= false;
	private String 				protocol 					= null;
	private boolean				lock						= false;
	private String				lockDir						= null;
	private int					lockTimeOut					= 0;
	private int					maxMessages					= 0;
	private boolean 			fifo	 					= false;
	private boolean 			lifo	 					= false;
	
	
	private		boolean 		running 					= false;
	
	protected BSCVFSProvider  	vfs 					 	= null;
	
	protected FileObject 		rootDir 					= null;
	
	protected LockService		lockService					= null;
	
	protected ServiceLock		svcsLock					= null;
	
	protected FileSystemOptions options 					= null;
	
	protected BSCScheduler scheduler						= null;
	
	private DateFormat formatter 							= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
    
	private int 		minMessageSize				= 0;
	
	public boolean isRunning() {
		return running;
	}

	public void setRunning(boolean running) {
		this.running = running;
	}

	public BSCVFSListenerContainer(Logger logger) {
		this.logger = logger;
	}

	public Logger getLogger() {
		return logger;
	}

	public void setFileListener(BSCVFSListener fileListener) {
		this.fileListener = fileListener;
	}


	public void initialize() throws Throwable {

		logger.debug("Initializing VFS Listener Container " + this.hashCode());
		
		BSCSchedulerRuntime sr = ((BSCSchedulerRuntime)fileListener.getServiceContainer().getApplicationService().getApplicationServiceConfig().getRuntimeService(fileListener.getProperty("scheduler.run.time","quartz")));

		scheduler=sr.getScheduler();

		scheduler.scheduleTask(fileListener.getConfigName(), fileListener.getServiceContainer().getConfigName(), cron, this, logger);
		
		if(isLock()){
			initializeLock();
		}
		
		options = BSCDefaultVFSProvider.createFileSystemOptions(fileListener.getProperty("protocol","file"), fileListener);
		
		formatter.setTimeZone(TimeZone.getTimeZone("UTC"));
	}

	public void process() {

		logger.debug("VFS processing files ");
		
		try {

			vfs = new BSCDefaultVFSProvider(logger,fileListener.getProperty("protocol","file"),fileListener);
			vfs.init();
			
			if(isLock()){
				logger.info("Accquiring lock before processing");
				lock();
			}
			
			List<BSCVFSObjectInfo> matchedFiles = null;

			String fileURL = "";

			if ("file".equalsIgnoreCase(protocol)) {
				
				if (!filePath.startsWith("/")) {
					File relativeFile = new File(filePath);
					filePath = relativeFile.getAbsolutePath();
				}

				fileURL = BSCDefaultVFSProvider.createConnectionString(protocol, fileListener,filePath);
				matchedFiles = vfs.listFiles(fileURL, filePattern, fileAge, null, recursiveScan);
				
			} else {
				fileURL = BSCDefaultVFSProvider.createConnectionString(protocol, fileListener,filePath);
				matchedFiles = vfs.listFiles(fileURL, filePattern, fileAge, options,recursiveScan);
			}		
			
			
			if (matchedFiles != null && !matchedFiles.isEmpty()) {
				BSCVFSMessage vfsMessage = new BSCVFSMessage();
				UUID uuid = UUID.randomUUID();
				
				vfsMessage.setProperty("VFS.MESSAGEID", uuid.toString());
				
				vfsMessage.setProperty("VFS.MESSAGETIMESTAMP", formatter.format(new Date()));
				
				if(lifo){
					Collections.sort(matchedFiles, Collections.reverseOrder());
				}else{				
					Collections.sort(matchedFiles);					
				}
				
				if(maxMessages >0){
					maxMessages = maxMessages> matchedFiles.size()? matchedFiles.size():maxMessages;
					matchedFiles=matchedFiles.stream().limit(maxMessages).collect(Collectors.toList());
				}
				
				process(vfsMessage, matchedFiles, 0);
			} else {
				logger.info("No files found with matching criteria ");
			}
			
		} catch (Throwable e) {
			logger.error("Failure occurred in read cycle", e);
			throw new RuntimeException(e);			
		
		} finally {
			
			if(isLock()){
				try {
					logger.info("Releasing lock after processing is done");
					unlock();
				} catch (Exception e) {
					logger.error("Error releasing lock. ",e);
					e.printStackTrace();
				}
			}
			
			try {
				vfs.close();
				vfs=null;
			} catch (FileSystemException e) {
				logger.error("Error releasing lock. ",e);
				e.printStackTrace();
			}
			
		}
		
		
	}

	public void process(BSCVFSMessage vfsMessage, List<BSCVFSObjectInfo> matchedFiles, int retryCycle) {

		try {
			
			byte[] data;
			for (Iterator<BSCVFSObjectInfo> fileItr = matchedFiles.iterator(); fileItr.hasNext();) {
				
				BSCVFSObjectInfo file = fileItr.next();
				
				vfsMessage.addFile(file);
				
				long fileSize = file.getSize();
				
				vfsMessage.setProperty("VFS.FILE.PROCESS.TIME", formatter.format(new Date()));
				
				if (includeContent) {
					
					vfsMessage.setProperty("VFS.CONTENT", "TRUE");
					
					logger.debug("Reading content of the file {}", BSCVFSHelper.getMaskURL(file.getURL()));
					
					InputStream inStream = null;

					try {
						
						FileContent fileContent =vfs.getFileContent(file.getURL(), options);
						inStream = fileContent.getInputStream();
						if(decompress){
							unZipFile(inStream, vfsMessage);
						}
						else{
							data = new byte[(int)fileContent.getSize()];
							int offset = 0;
							int status = 0;
							while (offset < data.length && (status = inStream.read(data, offset, data.length - offset)) >= 0) {
								offset += status;
							}
							if (offset < data.length) {
								logger.error("Could not completely read file: {}", BSCVFSHelper.getMaskURL(file.getURL()));
								throw new IOException("Failed to read the entire file");
							}    
						
							logger.debug("Status: {} of reading file: {} ", status, BSCVFSHelper.getMaskURL(file.getURL()));
							vfsMessage.reset();
							vfsMessage.loadFileContent(data);
						}
						
					} catch (Throwable e) {
						logger.error("Error proessing file: {}", BSCVFSHelper.getMaskURL(file.getURL()), e);
						throw e;
					} finally {
						try {
							if (inStream != null)
								inStream.close();
						} catch (Exception ie) {
							logger.error("Error proessing file: {}", BSCVFSHelper.getMaskURL(file.getURL()), ie);
						}
					}
				} else {
					logger.debug("File content read is set to off");
					vfsMessage.reset();
					vfsMessage.loadFileContent(BSCVFSHelper.getMaskURL(vfsMessage.getProperty("VFS.FILE.URL")).getBytes());
				}

				try {
					
					if(!fileItr.hasNext()){
						vfsMessage.setProperty("VFS.BATCH.COMPLETE", "true");
					}else{
						vfsMessage.setProperty("VFS.BATCH.COMPLETE", "false");
					}
						
					if(fileSize > minMessageSize){
						fileListener.onMessage(vfsMessage);
					}else{
						logger.warn("File {} is scrapped as file size {} is less than specifiied min message size {}", BSCVFSHelper.getMaskURL(file.getURL()), fileSize, minMessageSize);
					}
					
					if(!browse){
						vfs.delete(file.getURL(),options);
					}
				} catch (BSCServiceException e) {
					if (e.getLevel() == BSCErrorLevel.ROLLBACK) {
						logger.error("Rolling back the message", e);
						throw e;
					}else{
						vfs.delete(file.getURL(),options);
					}
				} catch (Throwable e) {
					logger.error("Rolling back the file " + BSCVFSHelper.getMaskURL(file.getURL()), e);
					throw e;
				}
				fileItr.remove();
			}

		} catch (Throwable e) {
			logger.error("Error processing file and entering into retry cycle", e);
			this.setRunning(false);
		}

	}
	

	public BSCVFSMessage unZipFile(InputStream inStream,BSCVFSMessage vfsMessage)throws Exception {
		// Create zip file stream.
		
			ZipArchiveInputStream archive = new ZipArchiveInputStream(
				new BufferedInputStream(inStream)) ;
			ZipArchiveEntry entry;
			try {
				while ((entry = archive.getNextZipEntry()) != null) {
					// Print values from entry.
					logger.info("Zipped file entry name:"+ entry.getName());
					logger.info("Zipped file entry method:"+ entry.getMethod());// ZipEntry.DEFLATED is int 8
					vfsMessage.reset();
					vfsMessage.loadFileContent(IOUtils.toByteArray(archive));
					vfsMessage.setProperty("COMPRESSED.FILE.NAME", entry.getName());
				}
			
		archive.close();
		inStream.close();
			} catch (IOException e) {
				logger.error("Failed to decompress zipped file.", e);
				throw e;
			}
		
		
		return vfsMessage;
	}
	
	

	public void start() throws Exception {
		scheduler.start();
	}

	public void stop() throws Exception {
		if (scheduler != null) {
			scheduler.stop();
		}
	}

	public String getCron() {
		return cron;
	}

	public void setCron(String cron) {
		this.cron = cron;
	}


	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

	public String getFilePattern() {
		return filePattern;
	}

	public void setFilePattern(String filePattern) {
		this.filePattern = filePattern;
	}

	public long getFileAge() {
		return fileAge;
	}

	public void setFileAge(long fileAge) {
		this.fileAge = fileAge;
	}

	public boolean isIncludeContent() {
		return includeContent;
	}

	public void setIncludeContent(boolean includeContent) {
		this.includeContent = includeContent;
	}

	public String getProtocol() {
		return protocol;
	}

	public void setProtocol(String protocol) {
		this.protocol = protocol;
	}
	

	public BSCVFSListener getFileListener() {
		return fileListener;
	}

	
	public boolean isBrowse() {
		return browse;
	}

	public void setBrowse(boolean browse) {
		this.browse = browse;
	}

	public boolean isRecursiveScan() {
		return recursiveScan;
	}

	public void setRecursiveScan(boolean recursiveScan) {
		this.recursiveScan = recursiveScan;
	}
	
	public boolean isDecompress() {
		return decompress;
	}

	public void setDecompress(boolean decompress) {
		this.decompress = decompress;
	}


	public boolean isLock() {
		return lock;
	}

	public void setLock(boolean lock) {
		this.lock = lock;
	}

	public String getLockDir() {
		return lockDir;
	}

	public void setLockDir(String lockDir) {
		this.lockDir = lockDir;
	}

	public int getLockTimeOut() {
		return lockTimeOut;
	}

	public void setLockTimeOut(int lockTimeOut) {
		this.lockTimeOut = lockTimeOut;
	}

	public int getMaxMessages() {
		return maxMessages;
	}

	public void setMaxMessages(int maxMessages) {
		this.maxMessages = maxMessages;
	}

	public boolean isLifo() {
		return lifo;
	}

	public void setLifo(boolean lifo) {
		this.lifo = lifo;
	}

	protected void lock() throws ServiceLockException {

		logger.debug( " Locking");

		svcsLock = lockService.acquire(lockTimeOut,this.getLockDir());
		
		logger.debug( " Locked at  {}", svcsLock.toString());
	}


	protected boolean unlock() throws Exception {

		if (svcsLock != null) {

			logger.debug("Removing lock:  {}", svcsLock.toString());

			lockService.release(svcsLock);

			logger.debug(" Unlocked");

			svcsLock = null;

			return true;

		} else {
			return false;
		}

	}
	
	/**
	 * initializes the lock directory.
	 * 
	 */
	protected void initializeLock() throws Throwable {
		
		lockService = LockServiceFactory.getDefaultLockService();

	}

	public int getMinMessageSize() {
		return minMessageSize;
	}

	public void setMinMessageSize(int minMessageSize) {
		this.minMessageSize = minMessageSize;
	}


}
