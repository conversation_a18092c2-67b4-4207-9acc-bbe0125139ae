package com.bsc.intg.svcs.core.vfs;

import java.io.InputStream;
import java.net.URL;
import java.util.List;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.vfs2.FileContent;
import org.apache.commons.vfs2.FileObject;
import org.apache.commons.vfs2.FileSystemException;
import org.apache.commons.vfs2.FileSystemOptions;
import org.apache.commons.vfs2.Selectors;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestinationBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCVFSHelper;

public class BSCVFSDestination extends BSCDestinationBase {

	private String 		protocol 					= null;
	private String 		additionalProtocols 		= null;
	private String 		archiveFileURI				= null;
	private String 		fileURI						= null;
	private String 		workPath 					= null;
	private boolean 	overwrite 					= false;
	private boolean 	errorOnDuplicate 			= false;
	private String 		errorOnDuplicateAction 		= "";
	private boolean 	append	 					= false;
	private String 		appendSep					= null;
	private String 		triggerFileName				=null;
	private int 		triggerDelay				=0;
	

	public BSCVFSDestination(BSCComponentType componentType, BSCComponent parentComponent, String configName) {
		super(componentType, parentComponent, configName);
	}
	
	protected BSCDefaultVFSProvider vfs =null;
	
	
	public void initialize() throws BSCComponentInitializationException {
		
		try {
			
			super.initialize();
			
			logger.debug("Initializing VFS destination");
			
			workPath				= getProperty("work.path", getGlobalProperty("bsc.service.work")+".vfs/");
			fileURI					= getProperty("file.uri");
			protocol				= getProperty("protocol", "file");
			overwrite				= getBooleanProperty("overwrite", false);
			errorOnDuplicate		= getBooleanProperty("error.on.duplicate", false);
			errorOnDuplicateAction	= getProperty("error.on.duplicate.action", "skip");
			append					= getBooleanProperty("append", false);
			additionalProtocols		= getProperty("additional.protocols", "");
			archiveFileURI			= getProperty("archive.file.uri", "");
			appendSep				= getProperty("append.seperator","\n");
			triggerFileName			= getProperty("trigger.file.name","extract.done");
			triggerDelay			= getIntProperty("trigger.delay",0);
			
			if(errorOnDuplicate)
				errorOnDuplicateAction = "rollback";
			
			
			
			
			
		} catch (Throwable e) {
			throw new BSCComponentInitializationException(this, e, BSCErrorLevel.CRITICAL, "Failed to initialize the destination " + getInstanceName() );
		}
		
	}

	@Override
	public BSCMessage onSend(BSCMessage message) throws BSCDestinationException {
		
		return process(message);
		
	}
	
	public BSCMessage process(BSCMessage message) throws BSCDestinationException {
		
		BSCMessage outMessage = null;
		FileContent destFC = null;
		
		try {
			
			vfs = new BSCDefaultVFSProvider(logger,protocol+ "," + additionalProtocols,this);
			vfs.init();
			
			
			logger.info("Executing VFS destination");
			
			boolean includeContent = false;
			outMessage = message.cloneMessage(false);
			boolean doesFileExist = false;
			String includeContentStr = message.getProperty("VFS.CONTENT","false");
			String destFileURL = "";
			
			if("true".equalsIgnoreCase(includeContentStr)){
				includeContent = true;
			}
			
			
			String fileURL=message.getProperty("VFS.FILE.URL", "NA");
			String fileName=message.getProperty("VFS.FILE.NAME");
			
			FileObject destFO = null;
			String prefix=null;
			
			String fqArchiveFileURI = null;
			String fqArchiveFileURL = null;
			String archiveFileName = null;
			
			FileObject workFO = null;					
			FileObject sourceFO = null;	
			
			destFileURL = BSCDefaultVFSProvider.createConnectionString(protocol,this,fileURI+fileName);
			destFO = vfs.resolveFile(destFileURL ,BSCDefaultVFSProvider.createFileSystemOptions(protocol, this));

			doesFileExist = destFO.exists();
			if(doesFileExist && !errorOnDuplicateAction.equalsIgnoreCase("skip")){
				if(errorOnDuplicateAction.equalsIgnoreCase("rollback")){
					logger.error("File already exists in destination.File cannot be overwritten.Rolling back to retry.");
					throw new BSCDestinationException(this,BSCErrorLevel.ROLLBACK,"File already exists in destination.File cannot be overwritten.Rolling back to retry.");
				}
				
				if(errorOnDuplicateAction.equalsIgnoreCase("continue")){
					logger.error("File already exists in destination.File cannot be overwritten.Writing to archive location");
					prefix=BSCDefaultVFSProvider.getProviderFromURL(fileURL);
					workFO = vfs.resolveFile(BSCDefaultVFSProvider.createConnectionString("file",this, workPath + fileName ));

					if (workFO.exists()) {
						workFO = vfs.resolveFile( BSCDefaultVFSProvider.createConnectionString("file",this, workPath + BSCDefaultVFSProvider.getTempFilename(fileName)   ) );
					} 

					sourceFO = vfs.resolveFile(fileURL,BSCDefaultVFSProvider.createFileSystemOptions(prefix, message.getMessageOwner()));
					workFO.copyFrom(sourceFO, Selectors.SELECT_SELF);
					if( !archiveFileURI.equals("") ) {
						prefix=BSCDefaultVFSProvider.getProviderFromURL(archiveFileURI);
						fqArchiveFileURI=BSCDefaultVFSProvider.createConnectionString(prefix, this,archiveFileURI);
						archiveFileName = message.getProperty("VFS.ARCHIVE.FILE.NAME");
						if ( archiveFileName == null ) 
							archiveFileName= fileName;

						fqArchiveFileURL = fqArchiveFileURI + archiveFileName;
						FileObject archiveFO = vfs.resolveFile(fqArchiveFileURL,   BSCDefaultVFSProvider.createFileSystemOptions(prefix, this));
						
						boolean doesArchiveFileExist = archiveFO.exists();						
						if (doesArchiveFileExist ){
								fqArchiveFileURL = fqArchiveFileURI + BSCDefaultVFSProvider.getArchiveFileName(archiveFileName);
								archiveFO = vfs.resolveFile(fqArchiveFileURL,   BSCDefaultVFSProvider.createFileSystemOptions(prefix, this));
								archiveFO.copyFrom(workFO, Selectors.SELECT_SELF);
							
						}else{
							archiveFO.copyFrom(workFO, Selectors.SELECT_SELF);
						}
						message.reset();
						message.loadMessage(BSCVFSHelper.getMaskURL(archiveFO.getURL().toString()));
					}
					workFO.delete();
					throw new BSCDestinationException(this,BSCErrorLevel.CRITICAL,"File already exists in destination.File cannot be overwritten.Moving file to archive location");
				}

			}
			
			
			if (doesFileExist && !overwrite && !append) {
				logger.debug("File {} is overwritten as it already exist and configured to overwrite",BSCVFSHelper.getMaskURL(destFileURL));
				destFileURL = BSCDefaultVFSProvider.createConnectionString(protocol,this,fileURI+BSCDefaultVFSProvider.getTempFilename(fileName));
				destFO = vfs.resolveFile(destFileURL ,BSCDefaultVFSProvider.createFileSystemOptions(protocol, this));
			}

			
			if (includeContent) {
				outMessage = message;
				destFC = destFO.getContent();
				if(doesFileExist && append ){
					destFC.getOutputStream(append).write(ArrayUtils.addAll(appendSep.getBytes(), message.toByteArray()));					
				}else{
					destFC.getOutputStream().write(message.toByteArray());
				}
				destFC.close();
				
				try { 

					if( !archiveFileURI.equals("") ) {

						prefix=BSCDefaultVFSProvider.getProviderFromURL(archiveFileURI);								
						fqArchiveFileURI=BSCDefaultVFSProvider.createConnectionString(prefix, this,archiveFileURI);								
						archiveFileName = message.getProperty("VFS.ARCHIVE.FILE.NAME");

						if ( archiveFileName == null ) 
							archiveFileName=fileName;

						fqArchiveFileURL = fqArchiveFileURI + archiveFileName;
						FileObject archiveFO = vfs.resolveFile(fqArchiveFileURL,   BSCDefaultVFSProvider.createFileSystemOptions(prefix, this));
						
						boolean doesArchiveFileExist = archiveFO.exists();
						if (doesArchiveFileExist && !append){
							fqArchiveFileURL = fqArchiveFileURI + BSCDefaultVFSProvider.getArchiveFileName(archiveFileName);
							archiveFO = vfs.resolveFile(fqArchiveFileURL,   BSCDefaultVFSProvider.createFileSystemOptions(prefix, this));									
						}

						FileContent archiveFC = archiveFO.getContent();
						if(doesArchiveFileExist && append ){
							archiveFC.getOutputStream(append).write(ArrayUtils.addAll(appendSep.getBytes(), message.toByteArray()));					
						}else{
							archiveFC.getOutputStream().write(message.toByteArray());
						}
						archiveFC.close();
						outMessage.setProperty("VFS.ARCHIVE.FILE.URL", BSCVFSHelper.getMaskURL(archiveFO.getURL().toString()));
					}

				} catch (Throwable e){
					logger.error("Failed to archive the file {} to {} in BSCVFSDestionation", fileName, BSCVFSHelper.getMaskURL(fqArchiveFileURI+archiveFileName));
				}				

			} else {
				outMessage = message.cloneMessage(false);
				prefix=BSCDefaultVFSProvider.getProviderFromURL(fileURL);

				workFO = vfs.resolveFile(BSCDefaultVFSProvider.createConnectionString("file",this, workPath + fileName ));

				if (workFO.exists()) {
					workFO = vfs.resolveFile( BSCDefaultVFSProvider.createConnectionString("file",this, workPath + BSCDefaultVFSProvider.getTempFilename(fileName)   ) );
				} 

				sourceFO = vfs.resolveFile(fileURL,BSCDefaultVFSProvider.createFileSystemOptions(prefix, message.getMessageOwner()));
				workFO.copyFrom(sourceFO, Selectors.SELECT_SELF);
				InputStream inStream = null;
				byte[] data = null;
				if(doesFileExist && append){
					FileSystemOptions options = BSCDefaultVFSProvider.createFileSystemOptions("file", this);
					FileContent fileContent =vfs.getFileContent(workFO.getURL().toString(), options);
					inStream = fileContent.getInputStream();
					data = new byte[(int)fileContent.getSize()];
					int status = inStream.read(data);
					fileContent.close();
					logger.debug("Status: {} of reading work file: {} ", status, BSCVFSHelper.getMaskURL(workFO.getURL().toString()));
					destFC = destFO.getContent();
					destFC.getOutputStream(append).write(ArrayUtils.addAll(appendSep.getBytes(), data));
					destFC.close();
				}else{
					destFO.copyFrom(workFO, Selectors.SELECT_SELF);						
				}
				try { 

					if( !archiveFileURI.equals("") ) {

						prefix=BSCDefaultVFSProvider.getProviderFromURL(archiveFileURI);

						fqArchiveFileURI=BSCDefaultVFSProvider.createConnectionString(prefix, this,archiveFileURI);

						archiveFileName = message.getProperty("VFS.ARCHIVE.FILE.NAME");

						if ( archiveFileName == null ) 
							archiveFileName= fileName;

						fqArchiveFileURL = fqArchiveFileURI + archiveFileName;
						FileObject archiveFO = vfs.resolveFile(fqArchiveFileURL,   BSCDefaultVFSProvider.createFileSystemOptions(prefix, this));
						
						boolean doesArchiveFileExist = archiveFO.exists();						
						if (doesArchiveFileExist ){
							if(append){
								FileSystemOptions options = BSCDefaultVFSProvider.createFileSystemOptions("file", this);
								FileContent fileContent =vfs.getFileContent(workFO.getURL().toString(), options);
								inStream = fileContent.getInputStream();
								data = new byte[(int)fileContent.getSize()];
								int status = inStream.read(data);
								fileContent.close();
								logger.debug("Status: {} of reading work file: {} ", status, BSCVFSHelper.getMaskURL(workFO.getURL().toString()));
								FileContent archiveFC = archiveFO.getContent();
								archiveFC.getOutputStream(append).write(ArrayUtils.addAll(appendSep.getBytes(), data));
								archiveFC.close();
							}else{
								fqArchiveFileURL = fqArchiveFileURI + BSCDefaultVFSProvider.getArchiveFileName(archiveFileName);
								archiveFO = vfs.resolveFile(fqArchiveFileURL,   BSCDefaultVFSProvider.createFileSystemOptions(prefix, this));
								archiveFO.copyFrom(workFO, Selectors.SELECT_SELF);
							}
						}else{
							archiveFO.copyFrom(workFO, Selectors.SELECT_SELF);
						}

						outMessage.setProperty("VFS.ARCHIVE.FILE.URL", BSCVFSHelper.getMaskURL(fqArchiveFileURL));
						if(prefix.equals("s3"))
							outMessage.loadMessage(BSCVFSHelper.getMaskURL(fqArchiveFileURL));
						else
							outMessage.loadMessage(BSCVFSHelper.getMaskURL(archiveFO.getURL().toString()));

					}

				} catch (Throwable e){
					logger.error("Failed to archive the file {} to {} in BSCVFSDestionation", fileName, BSCVFSHelper.getMaskURL(fqArchiveFileURI+archiveFileName));
				}						

				workFO.delete();			
			}
			
			String fileMod = message.getProperty("VFS.FILE.MOD","");
			if(fileMod!= null && fileMod.length()>0){
				logger.debug("File {} is required to be set with permission ({}) However current permission is R:({}) W:({}) X:({})",BSCVFSHelper.getMaskURL(destFileURL),destFO.isReadable(), fileMod, destFO.isWriteable(), destFO.isExecutable());
				
				if(fileMod.toLowerCase().contains("r")){
					destFO.setReadable(true, false);
				}
				if(fileMod.toLowerCase().contains("w")){
					destFO.setWritable(true, false);
				}
				if(fileMod.toLowerCase().contains("x")){
					destFO.setExecutable(true, false);
				}				
				
				logger.debug("File {} has been granted permission R:({}) W:({}) X:({}) ",BSCVFSHelper.getMaskURL(destFileURL), destFO.isReadable(), destFO.isWriteable(), destFO.isExecutable());
			}
			
		
			
			logger.info("File {} is processed successfully. ",fileName );
			
			if(getBooleanProperty("generate.trigger", false)){
				
				logger.info("Generating Trigger file");
				String triggerURL = BSCDefaultVFSProvider.createConnectionString(protocol,this,fileURI+triggerFileName);
				FileObject triggerFO = vfs.resolveFile(triggerURL ,BSCDefaultVFSProvider.createFileSystemOptions(protocol, this));

				if(triggerDelay >0){
				//	TimeUnit.SECONDS.wait(triggerDelay);
					Thread.sleep(triggerDelay);
				}
				if(triggerFO.exists())
					triggerFO.delete();
				
				triggerFO.createFile();
			}

			return outMessage;
			
		}	catch (Throwable e){
			
			logger.error("Unrecoverable error while processing file in BSCVFSDestionation", e);
			throw new BSCDestinationException(this, e, BSCErrorLevel.ROLLBACK,e.getMessage()+".Unrecoverable error while processing file in BSCVFSDestionation.");
			
		} finally {
			
			if(destFC != null && destFC.isOpen()){
				try {
					destFC.close();
				} catch (FileSystemException e) {
					e.printStackTrace();
				}
			}
			
			try {
				vfs.close();
				vfs=null;
			} catch (FileSystemException e) {
				logger.error("Error closing the vfs resource. ",e);
				e.printStackTrace();
			}
			
			
		}
	}
	
	
	
	@Override
	public List<BSCMessage> onSend(List<BSCMessage> message) throws BSCDestinationException {
	
		return null;
	}
	
	
}
