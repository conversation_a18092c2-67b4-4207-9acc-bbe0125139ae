package com.bsc.intg.svcs.core.dfs;

import ch.qos.logback.classic.Level;

import java.util.List;

import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.recipes.locks.InterProcessMutex;
import org.apache.curator.framework.state.ConnectionState;
import org.apache.curator.utils.CloseableUtils;
import org.apache.zookeeper.CreateMode;
import org.slf4j.LoggerFactory;

import com.bsc.intg.svcs.core.zookeeper.ZooKeeperService;


/**
 * 
 * ZooKeeperDFSService class wraps ZNode operations using Zookeeper CuratorFramework 
 */
public class ZooKeeperDFSService implements DFSService,DFSServiceProvider  {
	
	private  static CuratorFramework client = null;
	private static DFSService zooDfs = null;
	
	
	private ZooKeeperDFSService(){
		initialize();
	}
	
	public static void initialize(){
		client = ZooKeeperService.getClient();
	}
	
	
	public static DFSService getInstance() {
		if(zooDfs!=null)
			return zooDfs;
		else{
			zooDfs = new ZooKeeperDFSService();
			return zooDfs;
		}
		
	}
	
	/**
	 * Creates zNode at mentioned path with byte data and 
	 * creation mode as specified 
	 * CreateMode e.g. - CreateMode.EPHEMERAL,CreateMode.PERSISTENCE.
	 *
	 */

	@Override
	public void create(String path, byte[] data, String mode) throws DFSServiceException {
		CreateMode crmode = CreateMode.PERSISTENT;
		
		if(mode.equals("EPHEMERAL"))
			crmode = CreateMode.EPHEMERAL;
		if(mode.equals("PERSISTENT"))
			crmode = CreateMode.PERSISTENT;
		if(mode.equals("EPHEMERAL_SEQUENTIAL"))
			crmode = CreateMode.EPHEMERAL_SEQUENTIAL;
		if(mode.equals("PERSISTENT_SEQUENTIAL"))
			crmode = CreateMode.PERSISTENT_SEQUENTIAL;
			
		try {
			client.create().creatingParentsIfNeeded().withMode(crmode).forPath(path, data);
		} catch (Exception e) {
			DFSServiceException ale = new DFSServiceException(
					"Znode creation failed,  exception occured while creating the znode " + e.getMessage());
			ale.initCause(e);
			throw ale;
		}

	}

	/**
	 * Fetch zNode data from mentioned path as byte data
	 * @return byte[]
	 *
	 */
	@Override
	public byte[] getData(String path) throws DFSServiceException {

		boolean isExist;
		byte[] myNodeBytes = null;

		try {
			isExist = client.checkExists().forPath(path) != null;
			
			if (isExist) 
				myNodeBytes = client.getData().forPath(path);
			

		} catch (Exception e) {
			DFSServiceException ale = new DFSServiceException(
					"Znode read failed,  exception occured while trying to read the znode " + e.getMessage());
			ale.initCause(e);
			throw ale;
		}

		return myNodeBytes;

	}
	
	/**
	 * checks if zNode exists for mentioned path
	 * @return true if zNode exists 
	 *
	 */
	@Override
	public boolean pathExists(String path) throws DFSServiceException {
		boolean isExist = false;
		try {
			isExist = client.checkExists().forPath(path) != null;
		} catch (Exception e) {
			DFSServiceException ale = new DFSServiceException(
					"Exception occured while trying to check existence of the znode " + e.getMessage());
			ale.initCause(e);
			throw ale;
		}
		if (isExist)
			return true;
		else
			return false;
	}

	/**
	 * Writes zNode data in bytes for mentioned path
	 */
	@Override
	public void setData(String path, byte[] data) throws DFSServiceException {

		boolean isExist;
		try {
			isExist = client.checkExists().forPath(path) != null;

			if (isExist) {
				client.setData().forPath(path, data);
			}
		} catch (Exception e) {
			DFSServiceException ale = new DFSServiceException(
					"Znode update failed,  exception occured while updating the znode " + e.getMessage());
			ale.initCause(e);
			throw ale;
		}

	}
	/**
	 * Deletes specified zNode 
	 * 
	 */

	@Override
	public void delete(String path)throws DFSServiceException{
		boolean isExist;
		try {
			isExist = client.checkExists().forPath(path) != null;

			if (isExist) {
				client.delete().forPath(path);
			}
		} catch (Exception e) {
			DFSServiceException ale = new DFSServiceException(
					"Znode delete failed,  exception occured while deleting the znode " + e.getMessage());
			ale.initCause(e);
			throw ale;
		}
		
	}

	/**
	 * Lists the zNodes under specific path
	 */
	@Override
	public List<String> getChildren(String path)throws DFSServiceException {
		List<String> child = null;
		try {
			child = client.getChildren().forPath(path);
		} catch (Exception e) {
			DFSServiceException ale = new DFSServiceException(
					"Znode children fetch failed,  exception occured while fetching children of the znode " + e.getMessage());
			ale.initCause(e);
			throw ale;
		}
		return child;
		
	}
	
	
	@Override
	public void start() {
		
	}

	@Override
	public void shutdown() {
		ZooKeeperService.shutdown();
		
	}

	@Override
	public void setLogLevel(String level) {
		ZooKeeperService.setLogLevel(level);
		
	}

	/**
	 * Provides DFSServiceProvider instance to control start and shutdown of the CuratorFramework  client instance
	 */
	@Override
	public DFSServiceProvider getDFSServiceProvider() {
		return (DFSServiceProvider)zooDfs;
	}


}



