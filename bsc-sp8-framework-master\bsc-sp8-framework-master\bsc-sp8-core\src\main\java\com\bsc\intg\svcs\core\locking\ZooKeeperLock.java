package com.bsc.intg.svcs.core.locking;

import java.util.concurrent.TimeUnit;

import org.apache.curator.framework.recipes.locks.InterProcessMutex;

/**
 * 
 * ZooLock class implements specifics of ZookeeperLock based on Zookeeper distributed locking recipe InterProcessMutex
 * 
 * @see Zoo<PERSON>eeperLock
 * 
 * <AUTHOR> <PERSON>
 *         <p>
 *         $Revision: 1.1 $
 *         <p>
 */

public class ZooKeeperLock extends ServiceLock {
	
	private InterProcessMutex mutex = null;
	private String path=null;
	
	/**
	 * Constructs ZooLock based on InterProcessMutex object and ZNode path
	 * 
	 * @param mutex
	 * 			org.apache.curator.framework.recipes.locks.InterProcessMutex
	 * @param path
	 *         	java.lang.String
	 */
	
	public ZooKeeperLock(InterProcessMutex mutex,String path) {
		this.mutex=mutex;
		this.path=path;
	}
	
	/**
	 * Acquires lock and blocks until the given time expires, returns true if lock is acquired before timeout otherwise returns false
	 * 
	 * @param timeout
	 * 			int
	 * @return boolean
	 * 		
	 */
	public boolean acquire(int timeout) throws Exception {
		return mutex.acquire(timeout, TimeUnit.SECONDS);
	}
	
	/**
	 * Acquires lock and blocks until acquired
	 * 
	 * @return true if acquired
	 * 			
	 */
	public boolean acquire() throws Exception {
		mutex.acquire();
		return true;
	}
	
	/**
	 * Releases lock 
	 * 
	 */
	public void release() throws Exception {
		mutex.release();
	}
	
	/**
	 * Returns string representation of acquired lock
	 *   			
	 */
	@Override
	public String toString() {
		return path + " (" + mutex.toString() +")" ;
	}

	
}
