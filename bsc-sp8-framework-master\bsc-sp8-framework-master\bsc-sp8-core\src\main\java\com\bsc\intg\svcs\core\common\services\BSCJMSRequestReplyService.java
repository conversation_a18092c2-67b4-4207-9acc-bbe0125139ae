package com.bsc.intg.svcs.core.common.services;



import java.util.List;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestination;
import com.bsc.intg.svcs.core.BSCIntegrationServiceBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCDestinationAction;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.message.BSCMessageProperties;



public class BSCJMSRequestReplyService extends BSCIntegrationServiceBase {

	BSCDestination requestQueue = null;
	BSCDestination replyQueue = null;


	public BSCJMSRequestReplyService(BSCComponentType componentType, BSCComponent parentComponent, String configName,
			String instance) {
		super(componentType, parentComponent, configName, instance);
	}

	@Override
	public BSCMessage service(BSCMessage message) throws BSCServiceException {
		
		try {
		
			logger.info("Sending the message to request queue {} ", requestQueue.getInstanceName() );
			message.setAction(BSCDestinationAction.JMS_PUT);
			requestQueue.send(message);
				
			logger.info("Creating the message selector to browse ");
			 
	        String messageSelector = "JMSMessageID='" + message.getProperty(BSCMessageProperties.JMS_MESSAGE_ID)+ "'";

	        message.setProperty(BSCMessageProperties.JMS_MESSAGE_SELECTOR,messageSelector );

			logger.info("Waiting for the reply message");
			message.setAction(BSCDestinationAction.JMS_GET);
			BSCMessage reply = replyQueue.send(message);
			
			if (reply == null )
				return message.cloneMessage(false).loadMessage("NO_REPLY");
			else
				return reply;
			
		} catch (BSCMessageException | RuntimeException | BSCDestinationException  e) {
			throw new BSCServiceException(this,e, BSCErrorLevel.CRITICAL, "Failed to process the request" );
		} 
		
	}


	
	@Override
	public void initialize() throws BSCComponentInitializationException {

		super.initialize();
		
		logger.info("Initializing destinations ");
		
		requestQueue 			= getDestination("RequestQueue");
		replyQueue 				= getDestination("ReplyQueue");
		
		valid=true;
	}



	

}
