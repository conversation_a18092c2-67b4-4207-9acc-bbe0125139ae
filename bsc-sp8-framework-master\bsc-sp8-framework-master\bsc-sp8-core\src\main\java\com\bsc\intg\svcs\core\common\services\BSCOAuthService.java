package com.bsc.intg.svcs.core.common.services;

import java.util.Base64;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestination;
import com.bsc.intg.svcs.core.BSCIntegrationServiceBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCCryptoHelper;




public class BSCOAuthService extends BSCIntegrationServiceBase {

	protected static String loginCookie = null;
	protected static Pattern accessPattern = null;
	protected static Pattern instanceURLPattern = null;
	protected BSCDestination oAuthDest = null;
	protected String auth = null;

	
	
	public BSCOAuthService(BSCComponentType componentType, BSCComponent parentComponent, String configName,
			String instance) {
		super(componentType, parentComponent, configName, instance);
	}
	

@SuppressWarnings("rawtypes")
@Override
	public BSCMessage service(BSCMessage inMessage) throws BSCServiceException {
		logger.debug("Executing BSCOAuthService service");
		BSCMessage outMessage = null;
		try {
			if(this.getProperty("reset.session","false").equals("true"))
				inMessage = logout(inMessage);
			outMessage = inMessage.cloneMessage(false);
			outMessage = login(inMessage);
		} catch (BSCMessageException | BSCDestinationException e) {
			throw new BSCServiceException(this,BSCErrorLevel.CRITICAL, "Failed to authenticate the message in BSCOAuthService");
		}
		
		return outMessage;
	
	}



/**
 * Initializes all the resources of the service
 * 
 * @throws BSCComponentInitializationException
 * 			if any exception occurs during initialization of the components or resources of the service.
 */
	@Override
	public void initialize() throws BSCComponentInitializationException {

		super.initialize();
		valid = true;
		String dest 				= this.getProperty("oauth.dest");
		oAuthDest = this.getDestination(dest);
		accessPattern = Pattern.compile(getProperty("accessToken.pattern",".*\"access_token\"\\s*:\\s*\"([^\"]+)\".*"));
		instanceURLPattern = Pattern.compile(getProperty("instance.url.pattern",".*\"instance_url\"\\s*:\\s*\"([^\"]+)\".*"));

//		
		
	}



public BSCMessage login(BSCMessage message) throws BSCDestinationException {


		logger.info("Executing login using oauth - ");
		String loginToken = getProperty("oauth.login.accesstoken","");
	//	String instanceURL = getProperty("oauth.login.instanceurl","");
		
		if(!loginToken.isEmpty()){
			message = setLoginToken(loginToken,message);
			return message;
		}
		
		BSCMessage authRespMsg = null;
		BSCMessage loginMessage = null;
		try {		
				
			loginMessage = createOAuthRequest(message);
			loginMessage.setProperty("REQUEST.HTTP.METHOD", null);
			authRespMsg = oAuthDest.send(loginMessage);
			logger.info("Authorization response recieved successfully");
			if (!authRespMsg.getProperty("RESPONSE.HTTP.HEADER.STATUSCODE").startsWith("2"))
				throw new BSCDestinationException(this, BSCErrorLevel.CONTINUE,
							"Failed to login using OAUTH ERROR CODE - "
									+ authRespMsg.getProperty("RESPONSE.HTTP.HEADER.STATUSCODE") + " ERROR REASON - "
									+ authRespMsg.getProperty("RESPONSE.HTTP.HEADER.STATUS"));
			message = processOAuthResponse(authRespMsg,message);

			
			} catch (BSCDestinationException| Exception | BSCMessageException e) {

				logger.error("Error occured while trying to login ", e);

				throw new BSCDestinationException(this, e, BSCErrorLevel.CONTINUE,
						"Failed to login using OAUTH ERROR CODE - "
								+ authRespMsg.getProperty("RESPONSE.HTTP.HEADER.STATUSCODE") + " ERROR REASON - "
								+ authRespMsg.getProperty("RESPONSE.HTTP.HEADER.STATUS"));
			} finally {
				if (loginMessage != null)
					loginMessage.close();
				if (authRespMsg != null)
					authRespMsg.close();
			}
			
		return message;
	
}


public BSCMessage createOAuthRequest(BSCMessage inMessage)throws BSCDestinationException, BSCMessageException{
	StringBuffer content = new StringBuffer();
	BSCMessage oAuthMessage = inMessage.cloneMessage(false);
	String password 	= getProperty("oauth.content.password","");
	String scrtyToken 	= getProperty("oauth.security.token","");
	String clientId		= getProperty("oauth.client.id","");
	String clientScrt 	= getProperty("oauth.client.secret","");
	String scope		= getProperty("oauth.scope","");
	//if (!password.equals("") &&  && !clientScrt.equals("")) {
		try {
			if(!clientId.equals(""))
				clientId	= BSCCryptoHelper.getSecret(clientId);
			if(!password.equals(""))
				password 	= BSCCryptoHelper.getSecret(password);
			if(!scrtyToken.equals(""))
				scrtyToken 	= BSCCryptoHelper.getSecret(scrtyToken);
			if(!clientScrt.equals(""))
				clientScrt 	= BSCCryptoHelper.getSecret(clientScrt);
		} catch (Exception e) {
			this.getLogger().error("Error decrypting the password");
			throw new BSCDestinationException(this, e, BSCErrorLevel.FATAL,
					"Authentication Failed " + e.toString());
		}
	//}

	content.append("grant_type=" + getProperty("oauth.grant.type"));
	content.append("&client_id=" + clientId);
	content.append("&client_secret=" + clientScrt);
	content.append("&username=" + getProperty("oauth.content.user"));
	content.append("&password=" + password + scrtyToken);
	content.append("&scope=" + scope);
	content.append("&format=" + getProperty("oauth.format"));
	oAuthMessage.loadMessage(getProperty("oauth.message",content.toString()));
	String oAuthPwd = getProperty("oauth.password");
	String oAuthUser = getProperty("oauth.user");
	
	if(oAuthPwd != null) {
		try{ 
			if (oAuthPwd.contains("ss://"))
				oAuthPwd = BSCCryptoHelper.getSecret(oAuthPwd);
	} catch (Exception e) {
		this.getLogger().error("Error decrypting the password");
		throw new BSCDestinationException(this, e, BSCErrorLevel.FATAL,
				"Authentication Failed " + e.toString());
		}
	}
	
	if(!(oAuthUser == null || oAuthPwd == null)){
		
		auth =  oAuthUser+ ":" + oAuthPwd;
		String pwd = Base64.getEncoder().encodeToString(auth.getBytes());
		auth = "Basic " + pwd;
		
	}
	oAuthMessage.setProperty("REQUEST.HTTP.URL", getProperty("oauth.url"));
	oAuthMessage.setProperty("REQUEST.HTTP.HEADER.Authorization", auth);
	oAuthMessage.setProperty("REQUEST.HTTP.HEADER.Content-Type", this.getProperty("oauth.content.type",""));
	oAuthMessage.setProperty("REQUEST.HTTP.HEADER.connection", "Keep-Alive");

	
	return oAuthMessage;
	
}

/**
 * Processes OAUTH login response and derives Authorization header for OAUTH http calls.
 *
 * @param oAuthRespMessage			
 * @see BSCMessage
 * 
 * @param inMessage
 * 			BSCMessage
 */	
public BSCMessage processOAuthResponse(BSCMessage oAuthRespMessage,BSCMessage inMessage){
	
	String loginToken = "", instanceURL = "";
	 Matcher accessMatcher = accessPattern.matcher(oAuthRespMessage.toString());
	 Matcher instanceURLMatcher = instanceURLPattern.matcher(oAuthRespMessage.toString());
        if (accessMatcher.matches() && accessMatcher.groupCount() > 0) {
        	loginToken = accessMatcher.group(1);
        	this.setProperty("oauth.login.accesstoken", loginToken);
        	inMessage.setProperty("REQUEST.HTTP.HEADER.Authorization",getProperty("oauth.login.token.type","") +" "+ loginToken);
        }
        
        if (instanceURLMatcher.matches() && instanceURLMatcher.groupCount() > 0) {
        	instanceURL = instanceURLMatcher.group(1);
        	this.setProperty("oauth.login.instanceurl", instanceURL);
        	inMessage.setProperty("REQUEST.HTTP.URL", instanceURL);
        	
        }
        
        if(!getProperty("oauth.token.header","").isEmpty()){
        	loginToken = oAuthRespMessage.getProperty("RESPONSE.HTTP.HEADER."+getProperty("oauth.token.header"));
        	this.setProperty("oauth.login.accesstoken", loginToken);
        	inMessage.setProperty("REQUEST.HTTP.HEADER."+getProperty("oauth.token.header"),loginToken);
        	inMessage.setProperty("REQUEST.HTTP.HEADER.Authorization", auth);
        }
        
        loginCookie = oAuthRespMessage.getProperty("RESPONSE.HTTP.HEADER.SET-COOKIE"); 
        
        if(loginCookie!=null)
        	inMessage.setProperty("REQUEST.HTTP.HEADER.Cookie",loginCookie);
        
       return inMessage;
	
}

private BSCMessage setLoginToken(String token,BSCMessage inMessage){
	
        if (!getProperty("oauth.login.token.type","").isEmpty()) {
        	inMessage.setProperty("REQUEST.HTTP.HEADER.Authorization",getProperty("oauth.login.token.type") +" "+ token);
        }
        if(!getProperty("oauth.login.instanceurl","").isEmpty()){
        	inMessage.setProperty("REQUEST.HTTP.URL", getProperty("oauth.login.instanceurl",""));
        }
        
        
        if(!getProperty("oauth.token.header","").isEmpty()){
        	inMessage.setProperty("REQUEST.HTTP.HEADER."+getProperty("oauth.token.header"),token);
        }
        if(loginCookie!=null)
        	inMessage.setProperty("REQUEST.HTTP.HEADER.Cookie",loginCookie);
        if(auth!=null)
        	inMessage.setProperty("REQUEST.HTTP.HEADER.Authorization", auth);
        
	return inMessage;
}


/**
 * Resets login headers .
 *
 */	

public BSCMessage logout(BSCMessage inMessage) {
	
	logger.info("Logging out - ");
	this.setProperty("oauth.login.accesstoken", "");
	this.setProperty("oauth.login.authorization", "");
	this.setProperty("oauth.login.intsanceurl", "");
	this.setProperty("reset.session","false");
	inMessage.setProperty("REQUEST.HTTP.HEADER.Cookie",null);
	inMessage.setProperty("REQUEST.HTTP.HEADER.Authorization", null);
	loginCookie = null;
	auth = null;
	logger.info("Logged out successfully - ");
	return inMessage;
	
}



	
}
