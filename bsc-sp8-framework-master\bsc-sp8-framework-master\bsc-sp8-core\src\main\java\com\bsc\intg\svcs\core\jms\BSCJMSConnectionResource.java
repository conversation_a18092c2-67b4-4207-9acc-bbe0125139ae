package com.bsc.intg.svcs.core.jms;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

import javax.jms.ConnectionFactory;
import javax.jms.Destination;
import javax.jms.JMSException;
import javax.jms.QueueConnectionFactory;
import javax.jms.XAConnectionFactory;
import javax.naming.NamingException;

import org.springframework.jms.connection.CachingConnectionFactory;

import com.atomikos.jms.AtomikosConnectionFactoryBean;
import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCResourceBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;

public class BSCJMSConnectionResource extends BSCResourceBase {
	
	public ConnectionFactory connectionFactory = null ;
	
	protected int encoding = 0;
	
	protected String connectionName = null;
	
	protected String connectionFactoryClass = null;
	
	protected String xaConnectionFactoryClass = null;
	
	protected String destinationClass = null;
	
	protected String providerName	= null;
	
	protected	JMSProvider	jmsProvider = null;
	
	protected Map<String,String>	connectionProperties = new HashMap<String,String>();
	protected Map<String,String>	destinationProperties = new HashMap<String,String>();
	
	
	public BSCJMSConnectionResource(BSCComponentType componentType, BSCComponent parent, String configName) {
		super(componentType, parent, configName);
	}
	
	public ConnectionFactory getJMSConnectionFactory() {
		return connectionFactory;
		
	}
	
	@Override
	public void initialize(String prefix ) throws BSCComponentInitializationException  { 

			this.initialize();
			
			logger.info("Initializing the connection group ({})({}) ", prefix, configName);
			
			this.connectionName   			= getNonEmptyProperty("connection.name");
			
			this.providerName	  			= getNonEmptyProperty("jms.provider.name");
			
			this.xaConnectionFactoryClass	= getNonEmptyProperty("jms.connection.xa.factory.class");
			
			this.connectionFactoryClass	  	= getNonEmptyProperty("jms.connection.factory.class");
			
			this.destinationClass		  	= getNonEmptyProperty("jms.destination.class");	
			
			
			boolean cached 					= getBooleanProperty("cached",true);
			
			boolean XA						= getBooleanProperty("XA",false);
	
			
			int cachePoolSize = getIntProperty("cache.pool.size",5);
			
			
			
			if (providerName.equals("MQ")) {
				jmsProvider = new JMSMQProvider(logger);
			}
			
			for ( int pi=1; pi<10;pi++) {
				
				
				String property =  getGlobalProperty(prefix + "." + connectionName + ".jms.connection.properties."+pi, "");
				
				int index=property.indexOf("=");
				
				if (!property.equals("")) {
					if (index < 0) {
						
						logger.info("Skipping connection property {} as it is formatted incorrectly",property);
						
					} else {
						
						String propertyName=property.substring(0,index);
						String propertyValue=property.substring(index+1,property.length());
						
						logger.info("Adding property name :{} value :{}",propertyName,propertyValue);
						connectionProperties.put(propertyName.trim(), propertyValue.trim());
					}
				
				}
			}
			
			for ( int pi=1; pi<10;pi++) {
				
				
				String property =  getGlobalProperty(prefix + "." + connectionName + ".jms.destination.properties."+pi, "");
				
				int index=property.indexOf("=");
				
				if (!property.equals("")) {
					if (index < 0) {
						logger.info("Skipping destination property {} as it is formatted incorrectly",property);
					} else {
						String propertyName=property.substring(0,index);
						String propertyValue=property.substring(index+1,property.length());
						logger.info("Adding property name :{} value :{}",propertyName,propertyValue);
						destinationProperties.put(propertyName.trim(), propertyValue.trim());
					}
				}
			}
				
			
			
			if (XA) {
				
				XAConnectionFactory xaqcf = null;  
				try {
					xaqcf = jmsProvider.getXAConnectionFactory(xaConnectionFactoryClass);
				} catch (Exception e) {
					logger.error("XA-ConnectionFactory failed to initialize for provider {}", providerName);
					throw new BSCComponentInitializationException(this, BSCErrorLevel.CRITICAL,"XA-ConnectionFactory failed to initialize for provider "+ providerName);
				}
				logger.info("({}) XA-ConnectionFactory is initializing with JMS properties",this.getConfigName());
				AtomikosConnectionFactoryBean acfb=new AtomikosConnectionFactoryBean();
			    acfb.setXaConnectionFactoryClassName(xaConnectionFactoryClass);
			    Properties p = acfb.getXaProperties();
			    p.putAll(connectionProperties);
			    jmsProvider.setConnectionProperties((ConnectionFactory)xaqcf, connectionProperties);
			    acfb.setUniqueResourceName(prefix+configName);
			    acfb.setXaConnectionFactory(xaqcf);
			    acfb.setXaProperties(p);
			    acfb.setLocalTransactionMode(false);
			    acfb.setMaxPoolSize(10);
			    
			    logger.info("({}) XA-ConnectionFactory is initialed with JMS properties, addding to the connection list",this.getConfigName());
			    this.connectionFactory=acfb;
			   
			} else {
	
				try {
					
					logger.info("({}) NONXA-ConnectionFactory is initializing with JMS properties",this.getConfigName());
					
					QueueConnectionFactory queueConnectionFactory = (QueueConnectionFactory) jmsProvider.getConnectionFactory();
					
					jmsProvider.setConnectionProperties(queueConnectionFactory, connectionProperties);
				
					if ( cached ) {
						CachingConnectionFactory cachefactory = new CachingConnectionFactory();
					    cachefactory.setTargetConnectionFactory(queueConnectionFactory);
					    cachefactory.setCacheProducers(true);
					    cachefactory.setSessionCacheSize(cachePoolSize);
					    this.connectionFactory=cachefactory;
					    
					} else {
						this.connectionFactory=queueConnectionFactory;
					}
			
					logger.info("({}) NONXA-ConnectionFactory is initialed with JMS properties, addding to the connection list",configName);
					   
					    
				} catch (JMSException | NamingException e) {
				    logger.error("({}) NONXA-ConnectionFactory failed to initialize", configName);
					throw new BSCComponentInitializationException(this, e, BSCErrorLevel.CRITICAL);
				}
			}
	
	}


	public Destination getJMSDestination(String destinationName, String destinationType) throws Exception {
		
		Destination destination= null;
		switch(destinationType) {
			
			case "queue":
				destination=jmsProvider.getDestination(destinationName, destinationType);
				jmsProvider.setDestinationProperties(destination, destinationProperties);
				break;
				
			case "topic":
				return null;
				
			default:
				return null;
				
		}
		return destination;
	    
	}

	@Override
	public void initialize() throws BSCComponentInitializationException {
		super.initialize();
	}

}
