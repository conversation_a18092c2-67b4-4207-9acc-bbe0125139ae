package com.bsc.intg.svcs.core;

import java.util.List;

import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.message.BSCMessage;

public interface BSCDestinationProvider extends BSCComponent {
	
	public BSCMessage onSend(BSCMessage message) throws BSCDestinationException;
	
	public List<BSCMessage> onSend(List<BSCMessage> message) throws BSCDestinationException;

}
