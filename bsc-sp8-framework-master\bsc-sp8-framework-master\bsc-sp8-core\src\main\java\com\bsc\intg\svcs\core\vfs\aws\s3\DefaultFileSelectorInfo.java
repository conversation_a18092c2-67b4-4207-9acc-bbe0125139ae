package com.bsc.intg.svcs.core.vfs.aws.s3;

import org.apache.commons.vfs2.FileObject;
import org.apache.commons.vfs2.FileSelectInfo;

/**
 * A default {@link FileSelectInfo} implementation.
 */
final class DefaultFileSelectorInfo implements FileSelectInfo {

    private FileObject baseFolder;
    private FileObject file;
    private int depth;

    @Override
    public FileObject getBaseFolder() {
        return baseFolder;
    }

    public void setBaseFolder(final FileObject baseFolder) {
        this.baseFolder = baseFolder;
    }

    @Override
    public FileObject getFile() {
        return file;
    }

    public void setFile(final FileObject file) {
        this.file = file;
    }

    @Override
    public int getDepth() {
        return depth;
    }

    public void setDepth(final int depth) {
        this.depth = depth;
    }

    @Override
    public String toString() {
        return super.toString() + " [baseFolder=" + baseFolder + ", file=" + file + ", depth=" + depth + "]";
    }
}
