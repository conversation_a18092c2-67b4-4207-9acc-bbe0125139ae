package com.bsc.intg.svcs.core.common.services;



import java.util.ArrayList;
import java.util.List;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestination;
import com.bsc.intg.svcs.core.BSCDestinationBase;
import com.bsc.intg.svcs.core.BSCIntegrationService;
import com.bsc.intg.svcs.core.BSCIntegrationServiceBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCStringUtils;



public class BSCDestinationLoadBalancer extends BSCDestinationBase {

	
	protected int lastOne = 0;
	
	protected String loadBalancingPolicy = "ACTIVE_PASSIVE";
	protected List<String> destinations = new ArrayList<String>();

	public BSCDestinationLoadBalancer(BSCComponentType componentType, BSCComponent parentComponent, String configName) {
		super(componentType, parentComponent, configName);
	}

	@Override
	public void initialize() throws BSCComponentInitializationException {

		String destinationsList = getProperty("destinations");

		this.destinations = BSCStringUtils.convertCommaSeperated(destinationsList);
		
		if ( destinations.size() == 0 ) {
			throw new BSCComponentInitializationException(this, BSCErrorLevel.CRITICAL,  "No destinations are defined for the destination load balancer " + getInstanceName() );
		}
	}


	@Override
	public BSCMessage onSend(BSCMessage message) throws BSCDestinationException {
		
		BSCMessage response = null;
		boolean previousCallSuccess=false;
		
		int totalDestinations =  this.destinations.size();
		int destinationCounter=0;
		
		for (String destinationName: this.destinations) {	
		
			if (! previousCallSuccess) {
					
				destinationCounter++;
				
				BSCDestination destination= null;
		
				try {
					
					logger.info("Executing the destination call on {} ", destinationName );
					response=null;
					destination =((BSCIntegrationService)this.getService()).getDestination(destinationName);
					response= destination.send(message);
					previousCallSuccess=true;
										
				} catch (RuntimeException | BSCDestinationException e) {
					
					previousCallSuccess=false;
					logger.warn("destination call failed witn the destination {}" , destinationName);
					
					if( response !=null ) response.close();
					
					if(totalDestinations==destinationCounter) {
						logger.warn("Exchausted with all the destinations configured " );
						throw new BSCDestinationException(this, e, BSCErrorLevel.CRITICAL, "Failed to invoke the destination service call on all of the destinations configured" + e.toString());
					}
				} 
			}	
		}
		
		return response;

	}


	@Override
	public List<BSCMessage> onSend(List<BSCMessage> message) throws BSCDestinationException {
		return null;
	}


	

	

}
