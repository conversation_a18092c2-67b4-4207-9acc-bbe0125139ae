package com.bsc.intg.svcs.core.jdbc;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.StringJoiner;
import java.util.stream.Collectors;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.json.XML;

public class BSCJDBCUtils {

	public static Object mapJDBCResponse(ResultSet row,String responseFormat, String delimiter) throws SQLException{
			
			switch(responseFormat){
			
			case "json":
				return mapJDBCResponseToJSON(row);
				
			case "xml":
				return mapJDBCResponseToXML(row);
				
			case "txt":
				return mapJDBCResponseToDelimFile(row,delimiter);
				
			default:
				return mapJDBCResponseToJSON(row);			
			
			}
					
	}
	
	public static JSONObject mapJDBCResponseToJSON(ResultSet row) throws SQLException {
		JSONObject jsonObj = new JSONObject();
		int colCount = row.getMetaData().getColumnCount();
		for (int i = 1; i <= colCount; i++) {

			String colName = row.getMetaData().getColumnName(i);
			String colVal = row.getString(colName);
			jsonObj.put(colName, colVal == null ? JSONObject.NULL : colVal);

		}
		return jsonObj;
	}

	
	
	
	public static String mapJDBCResponseToXML(ResultSet row) throws JSONException, SQLException {
		String xml = XML.toString(mapJDBCResponseToJSON(row));			
		return ("<Row>"+xml+"</Row>");
	}
	
	
	
	public static String constructJDBCResponse(List<Object> mappedResults, String responseFormat){
		
		switch(responseFormat){
		
		case "json":
			JSONArray jsonArr = new JSONArray();
			mappedResults.stream().forEach(jsonObj -> jsonArr.put(jsonObj));
			return jsonArr.toString(4);
		
		case "xml":
			StringBuffer xmlMsg = new StringBuffer();
			xmlMsg.append("<ResultSet>");
			mappedResults.stream().forEach(tag -> xmlMsg.append("\n"+tag));
			return xmlMsg.append("</ResultSet>").toString();
			
		case "csv":
			StringBuffer csvMsg = new StringBuffer();
			mappedResults.stream().forEach(csvRow -> csvMsg.append(csvRow));
			return csvMsg.toString();
		}		
		
		return null;
	}
	
	public static String mapJDBCResponseToDelimFile(ResultSet row, String delimiter) throws SQLException {
		StringBuffer csvRow = new StringBuffer();
		csvRow.append("\n");
		int colCount = row.getMetaData().getColumnCount();
		for (int i = 1; i <= colCount; i++) {
			String colName = row.getMetaData().getColumnName(i);
			if(i==colCount){
				csvRow.append(row.getString(colName));
			}else{
				csvRow.append(row.getString(colName)+delimiter);
			}
		}
		return csvRow.toString();
	}
	

	public static Object colNamesToDelimFileHeader(ResultSet row, String delimiter) throws SQLException {
		StringBuffer csvHeader = new StringBuffer();
		int colCount = row.getMetaData().getColumnCount();
		for (int i = 1; i <= colCount; i++) {
			String colName = row.getMetaData().getColumnName(i);
			if(i==colCount){
				csvHeader.append(colName);
			}else{
				csvHeader.append(colName+delimiter);
			}
		}
		return csvHeader.toString();
	}
	
	public static String mapJDBCMapResponseToJSON(Map<String, Object> row, List<String> skipColumns) {
		JSONObject jsonObj = new JSONObject();
		row.forEach((key, value) -> {
			if (!skipColumns.contains(key)) {
				jsonObj.put(key, value == null ? JSONObject.NULL : value);
			}
		});
		return jsonObj.toString();
	}

	public static String mapJDBCMapResponseToXML(Map<String, Object> row, List<String> skipColumns)
			throws JSONException {
		return XML.toString(mapJDBCMapResponseToJSON(row, skipColumns));
	}

	public static String mapJDBCMapResponseToDelimtedFile(Map<String, Object> row, String delimiter,
			List<String> skipcolumns) {
		        
        String result = row.entrySet().stream()
                .filter(entry -> !skipcolumns.contains(entry.getKey()))
                .map(Map.Entry::getValue)
                .map(i -> String.valueOf(i))
                .collect(Collectors.joining(delimiter));
        
        return result;
	}

	public static String colNamesToDelimFileHeader(Set<String> columns, String delimiter,
			List<String> skipcolumns) {
		StringJoiner joiner = new StringJoiner(delimiter);
		columns.forEach((value) -> {
			if (!skipcolumns.contains(value)) {
				joiner.add(value);
			}
		});
		return joiner.toString();
	}

	
}
