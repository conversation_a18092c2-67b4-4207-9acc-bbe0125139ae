package com.bsc.intg.svcs.core.common.services;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.StringReader;
import java.io.StringWriter;

import javax.xml.transform.Result;
import javax.xml.transform.Source;
import javax.xml.transform.Templates;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerConfigurationException;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.stream.StreamSource;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCIntegrationServiceBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCMessage;

public class BSCXSLTTtransformerService extends BSCIntegrationServiceBase {

	protected String xsltFileName = null;
	private static Source xslTransformFile = null;
	protected Transformer xformer = null;

	public BSCXSLTTtransformerService(BSCComponentType componentType, BSCComponent parentComponent, String configName,
			String instance) {
		super(componentType, parentComponent, configName, instance);

	}

	@Override
	public BSCMessage service(BSCMessage inMessage) throws BSCServiceException {

		BSCMessage outMessage = null;

		try {

			outMessage = inMessage.cloneMessage(false);
			StringWriter resultWriter = new StringWriter();

			// Prepare the input and output files
			Source source = new StreamSource(new StringReader(inMessage.getStringMessage()));
			Result result = new StreamResult(resultWriter);

			// Apply the xsl file to the source file and write the result to the
			// output file
			this.xformer.transform(source, result);
			outMessage.loadMessage(resultWriter.toString());

		} catch (BSCMessageException | TransformerException e) {
			logger.error("Error in transforming the message", e.getMessage());
			throw new BSCServiceException(this, e, BSCErrorLevel.ROLLBACK,
					e.getMessage() + " Failed to execute the XSLTTransformerService service");

		}

		return outMessage;

	}

	@Override
	public void initialize() throws BSCComponentInitializationException {

		super.initialize();
		valid = true;

		this.xsltFileName = this.getProperty("xslt.file.name");
		logger.debug("xsltFileName ( {} )", xsltFileName);

		File xsltFile = new File(xsltFileName);
		if (!xsltFile.canRead()) {
			throw (new BSCComponentInitializationException(this, BSCErrorLevel.FATAL,
					"XSLT file: " + xsltFileName + " not accessible!"));
		}
		try {
			xslTransformFile = new StreamSource(new FileInputStream(xsltFileName));
			// Create transformer factory
			TransformerFactory factory = TransformerFactory.newInstance();

			// Use the factory to create a template containing the xsl file
			Templates template = factory.newTemplates(xslTransformFile);

			// Use the template to create a transformer
			this.xformer = template.newTransformer();

		} catch (FileNotFoundException | TransformerConfigurationException e) {

			throw (new BSCComponentInitializationException(this, BSCErrorLevel.FATAL,
					"Not able to load XSLT file " + xsltFileName));
		}

	}

}
