package com.bsc.intg.svcs.core.common.services;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.security.KeyFactory;
import java.security.MessageDigest;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.interfaces.RSAPrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.xml.bind.DatatypeConverter;

												   
import org.apache.commons.lang.StringUtils;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestination;
import com.bsc.intg.svcs.core.BSCIntegrationServiceBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCCryptoHelper;

import io.jsonwebtoken.Jwts;
										  

public class BSCOAuthClientAssertionService extends BSCIntegrationServiceBase {

	protected static String loginCookie = null;
	protected static Pattern accessPattern = null;
	protected static Pattern instanceURLPattern = null;
	protected BSCDestination oAuthDest = null;
	protected String auth = null;

	public BSCOAuthClientAssertionService(BSCComponentType componentType, BSCComponent parentComponent,
			String configName, String instance) {
		super(componentType, parentComponent, configName, instance);
	}

	@Override
	public BSCMessage service(BSCMessage inMessage) throws BSCServiceException {
		logger.debug("Executing BSCOAuthClientAssertionService service");
		BSCMessage outMessage = null;
		try {
			if (this.getProperty("reset.session", "false").equals("true")) {
				logger.debug("Reset Session Set to true, resetting authentication info");
				inMessage = logout(inMessage);
			}
			outMessage = inMessage.cloneMessage(false);
			outMessage = login(inMessage);
		} catch (BSCMessageException | BSCDestinationException e) {
			throw new BSCServiceException(this, BSCErrorLevel.CRITICAL,
					"Failed to authenticate the message in BSCOAuthClientAssertionService");
		}
		return outMessage;

	}

	/**
	 * Initializes all the resources of the service
	 * 
	 * @throws BSCComponentInitializationException if any exception occurs during
	 *                                             initialization of the components
	 *                                             or resources of the service.
	 */
	@Override
	public void initialize() throws BSCComponentInitializationException {
		super.initialize();
		valid = true;
		String dest = this.getProperty("oauth.dest");
		oAuthDest = this.getDestination(dest);
		accessPattern = Pattern
				.compile(getProperty("accessToken.pattern", ".*\"access_token\"\\s*:\\s*\"([^\"]+)\".*"));
		instanceURLPattern = Pattern
				.compile(getProperty("instance.url.pattern", ".*\"instance_url\"\\s*:\\s*\"([^\"]+)\".*"));

	}

	/**
	 * Method to fetch access token, if not already present.
	 * 
	 * @param message - input message to set the access token
	 * @return input message with OAuth token set
	 * @throws BSCDestinationException
	 * @throws BSCServiceException
	 */
	public BSCMessage login(BSCMessage message) throws BSCDestinationException, BSCServiceException {

		logger.info("Executing login using client assertion oauth - ");
		String loginToken = getProperty("oauth.login.accesstoken", "");
		if (!loginToken.isEmpty()) {
			logger.info("Login token already present, setting login token");
			message = setLoginToken(loginToken, message);
			return message;
		}

		BSCMessage authRespMsg = null;
		BSCMessage loginMessage = null;
		try {
			loginMessage = createOAuthRequest(message);
			loginMessage.setProperty("REQUEST.HTTP.METHOD", null);
			authRespMsg = oAuthDest.send(loginMessage);
			logger.info("Authorization response recieved successfully");
			if (!authRespMsg.getProperty("RESPONSE.HTTP.HEADER.STATUSCODE").startsWith("2"))
				throw new BSCDestinationException(this, BSCErrorLevel.CONTINUE,
						"Failed to login using OAUTH ERROR CODE - "
								+ authRespMsg.getProperty("RESPONSE.HTTP.HEADER.STATUSCODE") + " ERROR REASON - "
								+ authRespMsg.getProperty("RESPONSE.HTTP.HEADER.STATUS"));
			message = processOAuthResponse(authRespMsg, message);

		} catch (BSCDestinationException | Exception | BSCMessageException e) {

			logger.error("Error occured while trying to login ", e);

			throw new BSCDestinationException(this, e, BSCErrorLevel.CONTINUE,
					"Failed to login using OAUTH ERROR CODE - "
							+ authRespMsg.getProperty("RESPONSE.HTTP.HEADER.STATUSCODE") + " ERROR REASON - "
							+ authRespMsg.getProperty("RESPONSE.HTTP.HEADER.STATUS"));
		} finally {
			if (loginMessage != null)
				loginMessage.close();
			if (authRespMsg != null)
				authRespMsg.close();
		}

		return message;

	}

	/**
	 * Create OAuth Request
	 * @param inMessage - Message to use for OAuth request creation
	 * @return - OAuth request Message
	 * @throws BSCServiceException
	 * @throws BSCMessageException
	 * @throws Exception
	 */
	public BSCMessage createOAuthRequest(BSCMessage inMessage)
			throws BSCServiceException, BSCMessageException, Exception {
		logger.debug("Generate OAuth Request");
		BSCMessage oAuthMessage = inMessage.cloneMessage(false);
		String scope = getProperty("oauth.scope", "");
		logger.debug("Scope {}", scope);		
		String tenant = getProperty("oauth.tenant", "");
		logger.debug("Tenant {}", tenant);		
		String grantType = getProperty("oauth.grant.type", "");
		logger.debug("Grant Type {}", grantType);		
		String clientId = getProperty("oauth.client.id", "");
		logger.debug("Client Id {}", clientId);		
		if(!clientId.equals(""))
			clientId	= BSCCryptoHelper.getSecret(clientId);
		String clientAssertionType = getProperty("oauth.client.assertion.type", "");
		logger.debug("Client Assertion Type {}", clientAssertionType);		
		String url = getProperty("oauth.url", "");
		logger.info("OAuth url {}", url);		
		String clientAssertion = generateClientAssertion();

		StringBuffer content = new StringBuffer();
		content.append("scope=" + scope);
		content.append("&tenant=" + tenant);
		content.append("&client_assertion=" + clientAssertion);
		content.append("&client_assertion_type=" + clientAssertionType);
		content.append("&grant_type=" + grantType);
		content.append("&client_id=" + clientId);
		
		oAuthMessage.loadMessage(content.toString());
		oAuthMessage.setProperty("REQUEST.HTTP.URL", url);
		return oAuthMessage;
	}

	/**
	 * Method to generate client assertion JWT token
	 * 
	 * @return - JWT Token String
	 * @throws BSCServiceException
	 */
	public String generateClientAssertion() throws BSCServiceException {

		logger.debug("Generate Client Assertion");
		String privateFile = getProperty("oauth.private.key", "");
		logger.debug("Private Key {}", privateFile);		
		String publicFile = getProperty("oauth.public.key", "");
		logger.debug("Public Key {}", publicFile);
		String subject = getProperty("oauth.client.assertion.sub", "");
		logger.debug("Subject {}", subject);
		String issuer = getProperty("oauth.client.assertion.iss", "");
		logger.debug("Issuer {}", issuer);
		String audience = getProperty("oauth.client.assertion.aud", "");
		logger.debug("Audience {}", audience);
		int expDays = Integer.valueOf(getProperty("oauth.client.assertion.exp.days", "1"));
		logger.debug("Expiry Days {}", expDays);
		String claimsString = getProperty("oauth.client.assertion.claims", "");
		logger.debug("Claims {}", claimsString);
		HashMap<String, Object> claimsMap = new HashMap<String, Object>();
		if (StringUtils.isNotEmpty(claimsString)) {
			String[] claims = claimsString.split(";");
			for (String claim : claims) {
				String key = claim.split(":")[0];
				String value = claim.split(":")[1];
				String[] valueArray = value.split(",");
				if (valueArray.length == 1) {
					claimsMap.put(key, value);
				} else {
					claimsMap.put(key, valueArray);
				}
			}
		}
		try {
																																
			logger.debug("Calculate Thumbprint of Public Key file {} to add to jwt header", publicFile);
			CertificateFactory certificateFactory = CertificateFactory.getInstance("X.509");
			X509Certificate cert = (X509Certificate) certificateFactory
					.generateCertificate(Files.newInputStream(Paths.get(publicFile), StandardOpenOption.READ));
   
			MessageDigest messageDigest = MessageDigest.getInstance("SHA-1");
			messageDigest.update(cert.getEncoded());
			String digestHex = DatatypeConverter.printHexBinary(messageDigest.digest());
			byte[] digest = javax.xml.bind.DatatypeConverter.parseHexBinary(digestHex);
			String thumbprint = Base64.getUrlEncoder().withoutPadding().encodeToString(digest);
			logger.debug("Public Certificate {}", cert);
			logger.debug("Public Certificate Thumbprint {}", thumbprint);

			logger.debug("Calculate Not Before Date for JWT");
			Date jwtNotBeforeDate = Calendar.getInstance().getTime();
			logger.debug("Not Before Date: {} ", jwtNotBeforeDate);

			logger.debug("Calculate Expiration Date for JWT");
			Calendar calNow = Calendar.getInstance();
			calNow.add(Calendar.DATE, expDays);
			Date jwtExpirationDate = calNow.getTime();
			logger.debug("Expiration Date: {} ", jwtExpirationDate);

			logger.debug("Read Private Key {} for Signing ", privateFile);
			PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(Files.readAllBytes(Paths.get(privateFile)));
																									
			logger.debug("Client Assertion - Private KeySpec" + keySpec);	 
			KeyFactory keyfact = KeyFactory.getInstance("RSA");																				   
			RSAPrivateKey privKey = (RSAPrivateKey) keyfact.generatePrivate(keySpec);
			logger.debug("Client Assertion - Private Key EXPO  {}", privKey.getPrivateExponent());
			logger.debug("Client Assertion - Private Key MODU  {}", privKey.getPrivateExponent());

			return Jwts.builder().notBefore(Calendar.getInstance().getTime()).expiration(jwtExpirationDate)
					.notBefore(jwtNotBeforeDate).issuer(issuer).subject(subject).audience().add(audience).and().header()
					.add("typ", "JWT").add("x5t", thumbprint).and().claims().add(claimsMap).and()
					.signWith(privKey, Jwts.SIG.RS256).compact();																					  
		} catch (Exception e) {
			logger.info("Exception while building JWT Token ", e);
			throw new BSCServiceException(this, e, BSCErrorLevel.ROLLBACK,
					"Failed to Generate the JWT Token from Private Key");
		}
	}

	/**
	 * Processes OAUTH login response and derives Authorization header.
	 * 
   
	 * @param oAuthRespMessage
	 * @see BSCMessage
	 * 
	 * @param inMessage BSCMessage
	 */
	public BSCMessage processOAuthResponse(BSCMessage oAuthRespMessage, BSCMessage inMessage) {
		logger.info("Processing OAuth Response {}", oAuthRespMessage.toString());
		String loginToken = "", instanceURL = "";
		Matcher accessMatcher = accessPattern.matcher(oAuthRespMessage.toString());
		Matcher instanceURLMatcher = instanceURLPattern.matcher(oAuthRespMessage.toString());
		if (accessMatcher.matches() && accessMatcher.groupCount() > 0) {
			loginToken = accessMatcher.group(1);
			this.setProperty("oauth.login.accesstoken", loginToken);
			inMessage.setProperty("REQUEST.HTTP.HEADER.Authorization",
					getProperty("oauth.login.token.type", "") + " " + loginToken);
		}

		if (instanceURLMatcher.matches() && instanceURLMatcher.groupCount() > 0) {
			instanceURL = instanceURLMatcher.group(1);
			this.setProperty("oauth.login.instanceurl", instanceURL);
			inMessage.setProperty("REQUEST.HTTP.URL", instanceURL);

		}

		if (!getProperty("oauth.token.header", "").isEmpty()) {
			loginToken = oAuthRespMessage.getProperty("RESPONSE.HTTP.HEADER." + getProperty("oauth.token.header"));
			this.setProperty("oauth.login.accesstoken", loginToken);
			inMessage.setProperty("REQUEST.HTTP.HEADER." + getProperty("oauth.token.header"), loginToken);
			inMessage.setProperty("REQUEST.HTTP.HEADER.Authorization", auth);
		}

		loginCookie = oAuthRespMessage.getProperty("RESPONSE.HTTP.HEADER.SET-COOKIE");

		if (loginCookie != null)
			inMessage.setProperty("REQUEST.HTTP.HEADER.Cookie", loginCookie);

		return inMessage;

	}

	/**
	 * Set access token to input message.
	 * 
	 * @param token     - OAuth access token
	 * @param inMessage - message to set the token
	 * @return - message with token set
	 */
	private BSCMessage setLoginToken(String token, BSCMessage inMessage) {

		if (!getProperty("oauth.login.token.type", "").isEmpty()) {
			inMessage.setProperty("REQUEST.HTTP.HEADER.Authorization",
					getProperty("oauth.login.token.type") + " " + token);
		}
		if (!getProperty("oauth.login.instanceurl", "").isEmpty()) {
			inMessage.setProperty("REQUEST.HTTP.URL", getProperty("oauth.login.instanceurl", ""));
		}

		if (!getProperty("oauth.token.header", "").isEmpty()) {
			inMessage.setProperty("REQUEST.HTTP.HEADER." + getProperty("oauth.token.header"), token);
		}
		if (loginCookie != null)
			inMessage.setProperty("REQUEST.HTTP.HEADER.Cookie", loginCookie);
		if (auth != null)
			inMessage.setProperty("REQUEST.HTTP.HEADER.Authorization", auth);

		return inMessage;
	}

	/**
	 * Resets login headers .
	 *
	 */

	public BSCMessage logout(BSCMessage inMessage) {

		logger.info("Logging out - ");
		this.setProperty("oauth.login.accesstoken", "");
		this.setProperty("oauth.login.authorization", "");
		this.setProperty("oauth.login.intsanceurl", "");
		this.setProperty("reset.session", "false");
		inMessage.setProperty("REQUEST.HTTP.HEADER.Cookie", null);
		inMessage.setProperty("REQUEST.HTTP.HEADER.Authorization", null);
		loginCookie = null;
		auth = null;
		logger.info("Logged out successfully - ");
		return inMessage;
	}
}
