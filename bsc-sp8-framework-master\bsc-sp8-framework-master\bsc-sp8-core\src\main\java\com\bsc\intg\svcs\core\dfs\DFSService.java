package com.bsc.intg.svcs.core.dfs;

import java.util.List;
import java.util.Map;

import org.apache.zookeeper.CreateMode;

/**
 * 
 * ZookeeperDFSService class defines abstract behavior for the DFSService
 * 
 */

public interface DFSService {
	
	public void create(String path, byte[] data, String mode) throws DFSServiceException;
	
	public byte[] getData(String path) throws DFSServiceException;
	
	public boolean pathExists(String path) throws DFSServiceException;
	
	public void setData(String path, byte[] data) throws DFSServiceException;
	
	public void delete(String path) throws DFSServiceException;
	
	public List<String> getChildren(String path) throws DFSServiceException;
	
	
	public void setLogLevel(String level);
	
	public DFSServiceProvider getDFSServiceProvider();
}
