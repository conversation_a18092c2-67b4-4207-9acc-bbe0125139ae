package com.bsc.intg.svcs.core.test;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;

import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentSubType;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestinationBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCPropertyHelper;

public class BSCSOAPDestinationStub extends BSCDestinationBase {
	
	HashMap<String, Integer> tcMap = new HashMap<String, Integer>();

	public BSCSOAPDestinationStub(BSCComponentType componentType, BSCComponent parentComponent, String configName) {
		super(componentType, parentComponent, configName);
	}

	public void initialize() throws BSCComponentInitializationException {

		super.initialize();

		logger.info("Initializing SOAP Stub");
	}

	@Override
	public BSCMessage onSend(BSCMessage message) throws BSCDestinationException {

		logger.info("Executing onSend method - SOAP Stub");	
		
		String	tcName 				= System.getProperty("user.testcase","Default");
		String	serviceStatus 		= System.getProperty("user.ServiceStatus","Up");
		
		Map<String, Object> soapProps = new HashMap<>();
		String	url = message.getProperty("REQUEST.SOAP.URL",getProperty("soap.endpoint.address"));
		String	wsdl = message.getProperty("REQUEST.SOAP.WSDL",getProperty("soap.wsdl"));
		String	namespace = message.getProperty("REQUEST.SOAP.NAMESPACE",getProperty("soap.target.namespace"));
		String	port = message.getProperty("REQUEST.SOAP.PORT",getProperty("soap.port"));
		String	operation = message.getProperty("REQUEST.SOAP.OPERATION",getProperty("soap.operation"));
		String	bindingType = message.getProperty("REQUEST.SOAP.BINDING.TYPE",getProperty("soap.binding.type"));
		String	messageMode = message.getProperty("REQUEST.SOAP.MESSAGE.MODE",getProperty("soap.message.mode"));
		soapProps.put("TestCase", tcName);
		soapProps.put("ServiceStatus", serviceStatus);
		soapProps.put("URL", url);
		soapProps.put("WSDL", wsdl);
		soapProps.put("Namespace", namespace);
		soapProps.put("Port", port);
		soapProps.put("Operation", operation);
		soapProps.put("MessageMode", messageMode);
		soapProps.put("BindingType", bindingType);

		BSCMessage outMessage = null;
		String customSoapPayload = System.getProperty("custom.soap.payload");
		
		try {
			outMessage = message.cloneMessage(false);
		} catch (BSCMessageException e1) {
			logger.error("Message cloning failed for test case {}",tcName,e1);
			e1.printStackTrace();
			outMessage = message;
		}
		
		
			try {
				if(customSoapPayload!=null){
					outMessage.loadMessage(customSoapPayload);
				}
				else{
				logger.info("Stubbing for service status {}",serviceStatus);	
				
				DocumentBuilderFactory docFactory = DocumentBuilderFactory.newInstance();
				DocumentBuilder docBuilder = docFactory.newDocumentBuilder();
				Document doc = docBuilder.newDocument();
				Element envelope = doc.createElementNS("http://schemas.xmlsoap.org/soap/envelope/", "soapenv:Envelope");
				Element header = doc.createElementNS("http://schemas.xmlsoap.org/soap/envelope/", "soapenv:Header");
				Element body = doc.createElementNS("http://schemas.xmlsoap.org/soap/envelope/", "soapenv:Body");
				Element bodyResp = doc.createElement("StubResponse");
				Node node = doc.appendChild(envelope);
				node.appendChild(header);
				node.appendChild(body);
				body.appendChild(bodyResp);
				
				soapProps.forEach((k,v)->{
					Element tag = doc.createElement(k);
					tag.appendChild(doc.createTextNode((String)v));
					bodyResp.appendChild(tag);
				});
				TransformerFactory transformerFactory = TransformerFactory.newInstance();
				Transformer transformer = transformerFactory.newTransformer();
				transformer.setOutputProperty(OutputKeys.INDENT, "yes");
				DOMSource source = new DOMSource(doc);
				StreamResult result = new StreamResult(new StringWriter());
				transformer.transform(source, result);
				String xmlString = result.getWriter().toString();
				System.out.println(xmlString);
			    outMessage.loadMessage(xmlString);
				}
			    	            
				
			} catch (BSCMessageException | Exception e) {
				logger.error("Failed constructing response stub message for test case {}",tcName,e);
				e.printStackTrace();
				
			}
		
			if (serviceStatus.contains("Down")) {
				logger.info("Stubbing for service status {}",serviceStatus);	
				outMessage.setProperty("RESPONSE.SOAP.HEADER.STATUS","500");
	            outMessage.setProperty("RESPONSE.SOAP.HEADER.ERRORDESC","Internal server error");
			}else{
				outMessage.setProperty("RESPONSE.SOAP.HEADER.STATUS","200");
			}
		
		if(! (message.getMessageOwner().getComponentSubType().equals(BSCComponentSubType.REST_CONTROLLER)
				|| message.getMessageOwner().getComponentSubType().equals(BSCComponentSubType.SOAP_CONTROLLER))){
			String sep = File.separator;
			String workingDir = System.getProperty("user.dir");
			String outputDir = workingDir + sep + "test" + sep + "output" + sep;
			Map<String, Object> props = new HashMap<String, Object>();
			props.putAll(BSCPropertyHelper.getPropertiesStartingWith(outMessage.getProperties(),"REQUEST.SOAP.",false));
			props.putAll(BSCPropertyHelper.getPropertiesStartingWith(outMessage.getProperties(),"RESPONSE.SOAP.",false));
			try {
				writeToFile(props, outMessage.getStringMessage(), outputDir, tcName,serviceStatus);
			} catch (BSCMessageException | IOException e) {
				e.printStackTrace();
			} 
		}
		
		if (serviceStatus.contains("Down")) {
			logger.info("Stubbing for service status {}",serviceStatus);	
			throw new BSCDestinationException(this, BSCErrorLevel.CRITICAL,"Service is down");
			
		}
		
		return outMessage;

	}

	
	public void writeToFile(Map<String, Object> props, String data, String fileDir, String tcName, String serviceStatus) throws IOException {
		
		if (!new File(fileDir).exists()) {
			new File(fileDir).mkdir();
		}
		
		boolean matchFound = true;
		if (tcMap.size() < 1) {
			tcMap.put(tcName, 0);
		}

		int fileCtr = 1;
		for (Map.Entry<String, Integer> entry : tcMap.entrySet()) {
			String key = entry.getKey();
			if (key.equals(tcName)) {
				matchFound = true;
				fileCtr = Integer.parseInt(entry.getValue().toString()) + 1;
				entry.setValue(fileCtr);
				break;
			} else {
				matchFound = false;
			}
		}
		if (!matchFound) {
			fileCtr = 1;
			tcMap.put(tcName, fileCtr);
		}

		String fileName = "SOAPResponse_"+tcName+"_"+serviceStatus+"_"+String.format("%02d", fileCtr) +".txt";
	

		logger.info("Output file name - {}", fileName);
		
		
		BufferedWriter writer = new BufferedWriter(new FileWriter(fileDir+fileName));
		if(props!=null){
			for (Entry<String, Object> p : props.entrySet()) {
				writer.write(p.getKey()+"="+(String)p.getValue() + "\n");
			}
		}
		writer.write(data + "\n");
		writer.close();
	}
	
	@Override
	public List<BSCMessage> onSend(List<BSCMessage> message) throws BSCDestinationException {
		// TODO Auto-generated method stub
		return null;
	}
}
