package com.bsc.intg.svcs.core.test;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Map.Entry;
import java.util.concurrent.atomic.AtomicInteger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.jdbc.datasource.embedded.EmbeddedDatabase;
import org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseBuilder;
import org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType;
import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentSubType;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestinationBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCPropertyHelper;

public class BSCSQL2JSONDestinationStub extends BSCDestinationBase {

	private NamedParameterJdbcTemplate template;
	private EmbeddedDatabase db;

	AtomicInteger SQL2JSONDstcount = new AtomicInteger(0);
	ArrayList<String> tcList = new ArrayList<String>();

	protected String destinationURL = null;
	protected String destinationDriver = null;

	public BSCSQL2JSONDestinationStub(BSCComponentType componentType, BSCComponent parentComponent, String configName) {
		super(componentType, parentComponent, configName);
	}

	@Override
	public void initialize() throws BSCComponentInitializationException {

		super.initialize();

		logger.info("Initializing SQL2JSON stub destination count - {}", SQL2JSONDstcount.get());

		this.destinationURL = getNonEmptyProperty("datasource.url");
		this.destinationDriver = getNonEmptyProperty("datasource.driver");

		try {

			Boolean isKudutable = getNonEmptyProperty("kudutable")!=null?Boolean.valueOf(getNonEmptyProperty("kudutable")):false;
			if (!isKudutable)
				this.template = createJDBCTemplate();
			else {
				this.template = new NamedParameterJdbcTemplate(createKuduJDBCTemplate());
			}
		} catch (Exception e) {
			logger.error("Error occured while creating JDBC/Kudu Template", e);
			throw new BSCComponentInitializationException(this, e, BSCErrorLevel.CRITICAL,
					"Failed to initialize the destination " + getInstanceName());
		}

	}

	public NamedParameterJdbcTemplate createJDBCTemplate() {

		logger.info("Creating JDBC Template");

		db = new EmbeddedDatabaseBuilder().setType(EmbeddedDatabaseType.H2).addScript("scripts/create-db.sql")
				.addScript("scripts/insert-data.sql").build();
		NamedParameterJdbcTemplate template = new NamedParameterJdbcTemplate(db);

		return template;

	}

	public DriverManagerDataSource createKuduJDBCTemplate() {

		logger.info("Creating Kudu Template");

		DriverManagerDataSource dataSource = new DriverManagerDataSource(destinationURL);
		dataSource.setDriverClassName(destinationDriver);

		Properties props = new Properties();
		props.setProperty("initialSize", "3");
		props.setProperty("maxActive", "10");
		dataSource.setConnectionProperties(props);

		return dataSource;

	}

	@Override
	public BSCMessage onSend(BSCMessage message) throws BSCDestinationException {

		logger.info("Executing onSend method - SQL2JSON Stub");

		JSONArray array = new JSONArray();
		String serviceStatus = message.getProperty("JMS_CORRELATION_ID", "Up");

		boolean isRestController = message.getMessageOwner().getComponentSubType()
				.equals(BSCComponentSubType.REST_CONTROLLER);
		String tcName = message.getProperty("JMS.USER.TestCase");
		String sql = "";

		if (isRestController) {

			tcName = message.getProperty("HTTP.testcase");
			serviceStatus = message.getProperty("HTTP.servicestatus");
		}

		sql = message.getProperty("sql");

		logger.info("SQL Query to be executed - " + sql);

		String sep = File.separator;
		String workingDir = System.getProperty("user.dir");
		String outputDir = workingDir + sep + "test" + sep + "output" + sep;

		logger.info("Test case - {}", tcName);
		logger.info("Service status - {}", serviceStatus);
		logger.info("Output file location - {}", outputDir);
		logger.info("isRestController - {}", isRestController);
		logger.info("isRestController - {}", message);

		boolean matchFound = false;

		for (int idx = 0; idx < tcList.size(); idx++) {
			if (tcList.get(idx).equals(tcName)) {
				matchFound = true;
			}
		}

		if (!matchFound) {
			SQL2JSONDstcount.set(0);
			tcList.add(tcName);
		}

		String fileName = configName + "_" + tcName + "_" + String.format("%02d", SQL2JSONDstcount.incrementAndGet())
				+ ".txt";
		logger.info("Output file - {} ", fileName);

		String deliveryCount = message.getProperty("JMS.USER.JMSXDeliveryCount");
		logger.info("deliveryCount  - {} ", deliveryCount);

		try {
			if (!new File(outputDir).exists()) {
				new File(outputDir).mkdir();
			}

			String filePath = outputDir + fileName;

			if (serviceStatus.equals("JDBCDown") || serviceStatus.equals("Down")) {
				if (deliveryCount == null || Integer.parseInt(deliveryCount) < 3) {

					logger.error("SQL2JSONDestinationStub is down");
					writeToFile(message.getProperties(), "SQL2JSONDestinationStub is down", filePath);
					throw new BSCDestinationException(this, BSCErrorLevel.CRITICAL, "SQL2JSONDestinationStub is down");
				}
			} else {
				writeToFile(message.getProperties(), sql, filePath);
			}

		}

		catch (BSCDestinationException | IOException e) {

			message.setException(e);
			throw new BSCDestinationException(this, e, BSCErrorLevel.CRITICAL,
					"Failed to process the request - " + e.getMessage());
		}

		switch (message.getAction()) {

		case JDBC_SELECT:

			logger.info("Executing JDBC_SELECT Action");

			try {

				template.query(sql, new RowMapper<JSONArray>() {

					public JSONArray mapRow(ResultSet result, int rowNum) throws SQLException {

						JSONObject item = new JSONObject();
						int colCount = result.getMetaData().getColumnCount();
						for (int i = 1; i <= colCount; i++) {

							String colName = result.getMetaData().getColumnName(i);
							String colVal = result.getString(colName);
							item.put(colName, colVal == null ? JSONObject.NULL : colVal);

						}
						array.put(item);
						return array;
					}

				});

			} catch (Exception e) {
				logger.error("Unable to execute SQL query- {}", e.toString());
				throw new BSCDestinationException(this, e, BSCErrorLevel.CRITICAL,
						"Unable to execute SQL query- " + e.toString());
			}
			try {
				return (message.loadMessage(array.toString()));
			}

			catch (BSCMessageException e) {
				logger.error("Failed to load message. ");
				throw new BSCDestinationException(this, e, BSCErrorLevel.CRITICAL, "Failed to load message ");
			}

		case JDBC_INSERT:

			logger.info("Executing JDBC_UPDATE Action");

			try {
				Map<String, String> namedParameters = new HashMap<String, String>();
				template.update(sql, namedParameters);
			} catch (Exception e) {
				logger.error("Unable to execute SQL query- {}", e.toString());
				throw new BSCDestinationException(this, e, BSCErrorLevel.CRITICAL,
						"Unable to execute SQL query- " + e.toString());
			}
			return (message);

		default:
			logger.error("Unspecified destination action ");
			throw new BSCDestinationException(this, BSCErrorLevel.CRITICAL, "Unspecified destination action ");
		}
	}

	public void writeToFile(Map<String, Object> props, String data, String filePath) throws IOException {

		BufferedWriter writer = new BufferedWriter(new FileWriter(filePath));
		Map<String, Object> httpHeaders = (Map<String, Object>) BSCPropertyHelper.getPropertiesStartingWith(props,
				"HTTP", false);
		for (Entry<String, Object> p : httpHeaders.entrySet()) {
			writer.write(p + "\n");
		}
		Map<String, Object> jmsHeaders = (Map<String, Object>) BSCPropertyHelper.getPropertiesStartingWith(props, "JMS",
				false);
		for (Entry<String, Object> p : jmsHeaders.entrySet()) {
			writer.write(p + "\n");
		}
		writer.write(data + "\n");
		writer.close();

	}

	@Override
	public List<BSCMessage> onSend(List<BSCMessage> message) throws BSCDestinationException {
		return null;
	}

}
