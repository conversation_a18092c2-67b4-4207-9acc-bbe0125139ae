package com.bsc.intg.svcs.core.common.services;

import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestination;
import com.bsc.intg.svcs.core.BSCIntegrationServiceBase;
import com.bsc.intg.svcs.core.BSCService;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.jms.BSCJMSDestination;
import com.bsc.intg.svcs.core.message.BSCDestinationAction;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCPropertyHelper;
import com.bsc.intg.svcs.core.util.BSCStringUtils;


public class BSCREST2RESTService extends BSCIntegrationServiceBase {

	protected BSCDestination publish = null;
	protected static Map<String, String> destinationMap 	= new HashMap<String, String>();
	
	public BSCREST2RESTService(BSCComponentType componentType, BSCComponent parentComponent, String configName,
			String instance) {
		super(componentType, parentComponent, configName, instance);
	}

	
	@Override
	public BSCMessage service(BSCMessage inMessage) throws BSCServiceException {

		BSCMessage outMessage = null;
		BSCMessage inputMessage = null;
		List<BSCDestination> 	destList 			= new ArrayList<BSCDestination>();

		try {

			outMessage = inMessage.cloneMessage(true);

			String application = null;
			String messageType = null;
			String domain = null;
			String messageOwner = inMessage.getMessageOwner().getConfigName();
			
			String path = inMessage.getProperty("destination.route","");
			String destNames = destinationMap.get(path);
			
			 if(destNames!=null && !destNames.isEmpty()){
		        	ArrayList<String> destArrayList  = BSCStringUtils.convertCommaSeperated(destNames);
		        	for (String destObjName: destArrayList ) {
		        		destList.add(this.getDestination(destObjName));
		        	}
		        }

			inMessage.getProperties().forEach((k, v) -> logger.debug("Message properties are {}={}", k, (String) v));

			application = inMessage.getProperty("JS.SourceApp");
			messageType = inMessage.getProperty("JS.MessageType");
			domain = inMessage.getProperty("JS.Domain");

			if (application == null || messageType == null || domain == null) {
				
				throw new BSCServiceException(this, null, BSCErrorLevel.FATAL,
						"Mandatory Parameters { x-bsc-application, x-bsc-messagetype, x-bsc-domain} are missing in request");

			}

			//Read custom header values and URL parameters
			Map<String, Object> customHeaders = BSCPropertyHelper.getPropertiesStartingWith(inMessage.getProperties(),
					"HTTP.x-bsc-header-", true);
			Map<String, Object> customUrlParameters = BSCPropertyHelper
					.getPropertiesStartingWith(inMessage.getProperties(), "HTTP.Header-", true);

			if (customHeaders != null) {
				logger.debug("Rest request headers are");
				customHeaders.forEach((k, v) -> {
					inMessage.setProperty(messageOwner + ".Meta." + k, (String) v);
					logger.debug("{}={}", messageOwner + ".Meta." + k, (String) v);
				});
			}

			if (customUrlParameters != null) {
				logger.debug("Rest request headers are");
				customUrlParameters.forEach((k, v) -> {
					inMessage.setProperty(messageOwner + ".Meta." + k, (String) v);
					logger.debug("{}={}", messageOwner + ".Meta." + k, (String) v);
				});
			}
			
			
			inMessage.setProperty("include.properties.prefix", messageOwner + ".Meta./");
			inMessage.setAction(BSCDestinationAction.JMS_PUT);
			
			
			inputMessage = inMessage.cloneMessage(false);
			if(inMessage.getStringMessage()!= null && !inMessage.getStringMessage().isEmpty()){
				String hasAttachment = inMessage.getProperty("HTTP.x-bsc-hasattachment","");
				if(inMessage.getProperty("HTTP_REQUEST_METHOD").equalsIgnoreCase("post")&& hasAttachment.equalsIgnoreCase("YES") ){
					logger.info("Inside decoding attachment, has attachment:"+hasAttachment);
					byte[] bytes = Base64.getDecoder().decode(inMessage.getStringMessage());
					inputMessage.loadMessage(bytes);
				}else{
					inputMessage.loadMessage(inMessage.getStringMessage());
				}
			}
			
			
			
			if(!destList.isEmpty()){
				for(BSCDestination dest:destList){
					outMessage = inMessage.cloneMessage(false);
					 if(dest.getProperty("auth.service","").equals("")){
						 outMessage = dest.send(inputMessage);
					 }
					 else{
							 
					BSCMessage reqMessage = invokeAuthenticator(inputMessage, dest.getProperty("auth.service"), "false");
					outMessage = dest.send(reqMessage);
					if (isResetSession(outMessage, dest)) {
						reqMessage = invokeAuthenticator(inputMessage, dest.getProperty("auth.service"), "true");
						outMessage = dest.send(reqMessage);
						if (isResetSession(outMessage, dest)) {
							logger.error("Not able to establish the session ");
							throw new BSCDestinationException(this, BSCErrorLevel.CRITICAL,
									"Not able to establish the session. " + outMessage.getStringMessage());

						}

					}
				}
				}
			}else{
				
				outMessage = inMessage.cloneMessage(false);
				outMessage = publish.send(inputMessage);
			}
			String status = outMessage.getProperty("RESPONSE.HTTP.HEADER.STATUSCODE").toString();
			if (!(status.startsWith("2"))) {
				logger.error("Failure response status code:{}",
						outMessage.getProperty("RESPONSE.HTTP.HEADER.STATUSCODE").toString());
				throw new BSCServiceException(this, BSCErrorLevel.CRITICAL,
						"Failed to call the service.ErrorCode: " + outMessage.getProperty("RESPONSE.HTTP.HEADER.STATUSCODE")
								+ "- ErrorReason:" + outMessage.getProperty("RESPONSE.HTTP.HEADER.STATUS")+"- ErrorResponse="+ outMessage.getStringMessage());
			}
			
			

		} catch (BSCMessageException | BSCDestinationException | BSCServiceException| Exception e) {
			inMessage.setException(e);
			throw new BSCServiceException(this, e, BSCErrorLevel.ROLLBACK,
					"Failed to process rest request. " + e.getMessage());
		}
		return outMessage;
	}
	
	
	private boolean isResetSession(BSCMessage outMsg, BSCDestination dest) throws BSCMessageException {

		String resetCode = dest.getProperty("session.reset.codes", "");
		String resetValue = dest.getProperty("session.reset.values", "");

		if (!resetCode.isEmpty() && resetCode.contains(outMsg.getProperty("RESPONSE.HTTP.HEADER.STATUSCODE"))) {
			logger.info("HTTP response code matched with session reset codes, resetting the session ");
			return true;
		}

		if (!resetValue.isEmpty() && !outMsg.getStringMessage().isEmpty() && resetValue.contains(outMsg.getStringMessage())) {
			logger.info("HTTP response is matched with session reset values, resetting the session ");
			return true;
		}

		return false;

	}

	private BSCMessage invokeAuthenticator(BSCMessage inMessage, String authService, String resetFlag)
			throws BSCServiceException {

		BSCService service = null;
		BSCMessage responseMessage = null;
		try {
			service = this.getServiceContainer().getService(authService);
			service.setProperty("reset.session", resetFlag);
			responseMessage = service.execute(inMessage);
			return responseMessage;

		} catch (BSCServiceException e) {
			throw new BSCServiceException(this, e, e.getLevel(),
					"Failed to execute backend service: " + e.getMessage());

		} finally {
			if (service != null) {
				try {
					this.getServiceContainer().releaseService(authService, service);
				} catch (Exception | BSCServiceException e) {
					throw new BSCServiceException(this, e, BSCErrorLevel.ROLLBACK,
							"Failed to execute backend service" + e.getMessage());
				}
			}
		}

	}


/**
 * Initializes all the resources of the service
 * 
 * @throws BSCComponentInitializationException
 * 			if any exception occurs during initialization of the components or resources of the service.
 */
	@Override
	public void initialize() throws BSCComponentInitializationException {

		super.initialize();
		valid = true;
		publish = this.getDestination("Publish");
		String destRouteMapProp 	= 	this.getProperty("destination.route.map","");
		
		if (destinationMap.isEmpty()){
			if(destRouteMapProp!=null && !destRouteMapProp.isEmpty()){
				 String[] destAr = destRouteMapProp.split(";");
		            for(String destObject: destAr){
		                String destKey = destObject.split("\\:")[0];
		                String destValue = destObject.split("\\:")[1];
		                destinationMap.put(destKey, destValue);
		            }
			}
			
		}

	}

}
