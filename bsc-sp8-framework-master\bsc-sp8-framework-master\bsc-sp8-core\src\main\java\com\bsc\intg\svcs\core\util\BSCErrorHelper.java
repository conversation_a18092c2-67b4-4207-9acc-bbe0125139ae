package com.bsc.intg.svcs.core.util;

public class BSCErrorHelper {

	public static String getExceptionTrace(Throwable t, int traceSize) {
		String errorTrace = "";		

		if ( t != null ) {
			
		
			StackTraceElement trace[] = t.getStackTrace();
			errorTrace= t.getMessage().toString();
			int traceLength = trace.length > traceSize ? traceSize : trace.length;
			for (int i = 0; i < traceLength; i++) {
				errorTrace = errorTrace + trace[i] + "\n";
			}
			
		} else {
			errorTrace = "Unable to extract error details";		

		}
		
		return errorTrace;
	}
}
