CONFIG.disabled=false

APPLICATION.XA.required=false
APPLICATION.runtime.containers=Management

RESOURCE.XA=false
RESOURCE.jms.provider.name=MQ
RESOURCE.jms.connection.xa.factory.class=com.ibm.mq.jms.MQXAQueueConnectionFactory
RESOURCE.jms.connection.factory.class=com.ibm.mq.jms.MQQueueConnectionFactory
RESOURCE.jms.destination.class=com.ibm.mq.jms.MQQueue

CONTAINER.runtime.services=Event,JScript
CONTAINER.event.service.name=Event
CONTAINER.script.service.name=JScript


DESTINATION.primary=true


JScript.class=com.bsc.intg.svcs.core.container.services.BSCJScriptService
JScript.pool.size=1

#Event Service 
Event.destinations=EventDestination
Event.destination.type=JMS
Event.class=com.bsc.intg.svcs.core.container.services.BSCEventService
Event.pool.size=1

#Event Destination
EventDestination.class=com.bsc.intg.svcs.core.jms.BSCMultipleQueueDestination
EventDestination.destinations=EVENT1,EVENT2,EVENT3

EVENT1.destination.name=BSC.SERVICE.EVENT.1
EVENT1.connection=JMSEVENT
EVENT1.class=com.bsc.intg.svcs.core.jms.BSCJMSDestination
EVENT1.primary=false
EVENT1.transactional=false

EVENT2.destination.name=BSC.SERVICE.EVENT.2
EVENT2.connection=JMSEVENT
EVENT2.class=com.bsc.intg.svcs.core.jms.BSCJMSDestination
EVENT2.primary=false
EVENT2.transactional=false

EVENT3.destination.name=BSC.SERVICE.EVENT.3
EVENT3.connection=JMSEVENT
EVENT3.class=com.bsc.intg.svcs.core.jms.BSCJMSDestination
EVENT3.primary=false
EVENT3.transactional=false

#Default events
CONTROLLER.Event.Error.EVENT_Type=Error
LISTENER.Event.Error.EVENT_Type=Error

