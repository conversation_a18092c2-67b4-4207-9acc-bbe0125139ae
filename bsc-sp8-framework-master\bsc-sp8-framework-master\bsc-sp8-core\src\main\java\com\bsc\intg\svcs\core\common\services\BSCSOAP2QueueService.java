package com.bsc.intg.svcs.core.common.services;

import java.util.HashMap;
import java.util.Map;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestination;
import com.bsc.intg.svcs.core.BSCIntegrationServiceBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.jms.BSCJMSDestination;
import com.bsc.intg.svcs.core.message.BSCDestinationAction;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.soap.BSCSOAPGatewayResponse;
import com.bsc.intg.svcs.core.util.BSCPropertyHelper;
import com.bsc.intg.svcs.core.util.BSCStringUtils;


public class BSCSOAP2QueueService extends BSCIntegrationServiceBase {

	protected BSCDestination publishToQ = null;
	protected static Map<String, String> destinationMap 	= new HashMap<String, String>();

	
	public BSCSOAP2QueueService(BSCComponentType componentType, BSCComponent parentComponent, String configName,
			String instance) {
		super(componentType, parentComponent, configName, instance);
	}

	@Override
	public BSCMessage service(BSCMessage inMessage) throws BSCServiceException {

		BSCMessage outMessage = null;

		try {
			
    		BSCSOAPGatewayResponse soapGtwyResponse=(BSCSOAPGatewayResponse)inMessage.getMessageModel().getModel("soap.response").getObject();

			outMessage = inMessage.cloneMessage(false);
			outMessage.loadMessage(inMessage.getMessageModel());

			String application = null;
			String messageType = null;
			String domain = null;
			String messageOwner = inMessage.getMessageOwner().getConfigName();
			

			inMessage.getProperties().forEach((k, v) -> logger.debug("Message properties are {}={}", k, (String) v));

			application = inMessage.getProperty("JS.SourceApp");
			messageType = inMessage.getProperty("JS.MessageType");
			domain = inMessage.getProperty("JS.Domain");

			if (application == null || messageType == null || domain == null) {
				
				throw new BSCServiceException(this, null, BSCErrorLevel.FATAL,
						"Mandatory Parameters { x-bsc-application, x-bsc-messagetype, x-bsc-domain} are missing in request");

			}

			//Read custom header values and URL parameters
			Map<String, Object> customHeaders = BSCPropertyHelper.getPropertiesStartingWith(inMessage.getProperties(),
					"HTTP.x-bsc-header-", true);
			Map<String, Object> customUrlParameters = BSCPropertyHelper
					.getPropertiesStartingWith(inMessage.getProperties(), "HTTP.Header-", true);

			if (customHeaders != null) {
				logger.debug("Rest request headers are");
				customHeaders.forEach((k, v) -> {
					inMessage.setProperty(messageOwner + ".Meta." + k, (String) v);
					logger.debug("{}={}", messageOwner + ".Meta." + k, (String) v);
				});
			}

			if (customUrlParameters != null) {
				logger.debug("Rest request headers are");
				customUrlParameters.forEach((k, v) -> {
					inMessage.setProperty(messageOwner + ".Meta." + k, (String) v);
					logger.debug("{}={}", messageOwner + ".Meta." + k, (String) v);
				});
			}
			
			inMessage.setProperty("include.properties.prefix", messageOwner + ".Meta./");
			inMessage.setAction(BSCDestinationAction.JMS_PUT);
			
			publishToQ.send(inMessage);
			outMessage.loadMessage(inMessage.getProperty("response","<Response><Msg>Message is delivered</Msg></Response>"));

			soapGtwyResponse.setResponse(inMessage.getProperty("response","<Response><Msg>Message is delivered</Msg></Response>"));
			
			

		} catch (BSCMessageException | BSCDestinationException | BSCServiceException| Exception e) {
			inMessage.setException(e);
			throw new BSCServiceException(this, e, BSCErrorLevel.ROLLBACK,
					"Failed to process message into queue due to " + e.getMessage());
		}
		return outMessage;
	}


	@Override
	public void initialize() throws BSCComponentInitializationException {

		super.initialize();
		valid = true;
		publishToQ = this.getDestination("BscPublish");

	}

}
