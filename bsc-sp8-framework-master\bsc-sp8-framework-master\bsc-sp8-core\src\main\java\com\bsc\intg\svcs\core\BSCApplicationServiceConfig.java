package com.bsc.intg.svcs.core;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.util.BSCStringUtils;

public class BSCApplicationServiceConfig extends BSCComponentBase implements BSCComponent{

	protected Map<String, BSCApplicationService> applicationServicesMap  = new HashMap<String, BSCApplicationService>();
	
	protected ArrayList<String> 			applicationServices	=	null;
	
	protected  Map<String,Object>  			runtime				= new HashMap<String,Object>();
	
	public BSCApplicationServiceConfig(BSCComponentType componentType , Map<String,Object> runtime, String configName) {
		super(componentType, runtime, configName);
		this.runtime=runtime;
	}
	
	public List<String> getApplicationServices() {
		return applicationServices;
	}

	public  BSCApplicationService getApplicationService(String applicationService) {
		return applicationServicesMap.get(applicationService);
	}
	
	public  Object getRuntimeService(String name) {
		return runtime.get(name);
	}
	
	public void  initialize() throws BSCComponentInitializationException {
		
		String applicationServiceNames 		= 	getProperty("application.services","");
		
		this.applicationServices=BSCStringUtils.convertCommaSeperated(applicationServiceNames);
		
		for ( String applicationServiceObjName : applicationServices ) {
			
			logger.info("Creating the application service {}" , applicationServiceObjName );
			BSCApplicationService applicationService = new BSCApplicationServiceBase(BSCComponentType.APPLICATION, this, applicationServiceObjName);
			
			logger.info("Initializing the application service {}" , applicationServiceObjName );
			applicationService.initialize();
			
			logger.info("Adding the application service {}" , applicationServiceObjName);
			applicationServicesMap.put(applicationServiceObjName, applicationService);
		}
		
	}

	@Override
	public void start() throws BSCComponentInitializationException {
		for ( String applicationServiceObjName : applicationServices ) {
			logger.info("Starting the application service {}" , applicationServiceObjName );
			this.getApplicationService(applicationServiceObjName).start();
		}
	}

	@Override
	public void stop() {
		for ( String applicationServiceObjName : applicationServices ) {
			logger.info("Stopping the application service {}" , applicationServiceObjName );
			this.getApplicationService(applicationServiceObjName).stop();
		}
	}


	
}
