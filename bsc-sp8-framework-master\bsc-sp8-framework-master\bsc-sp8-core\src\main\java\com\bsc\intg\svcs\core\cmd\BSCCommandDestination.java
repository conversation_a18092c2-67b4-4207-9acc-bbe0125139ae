package com.bsc.intg.svcs.core.cmd;

import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Arrays;
import java.util.List;
import org.apache.commons.lang.ArrayUtils;
import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestinationBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.message.BSCMessage;

public class BSCCommandDestination extends BSCDestinationBase {


	protected String command = null;
	private int goodExit	= 0;

	private String				stdoutTxt	= "";

	private String				stderrTxt	= "";
	
	protected void setStderrTxt(String stderrTxt) {
		this.stderrTxt = stderrTxt;
	}

	protected void setStdoutTxt(String stdoutTxt) {
		this.stdoutTxt = stdoutTxt;
	}

	
	public BSCCommandDestination(BSCComponentType componentType, BSCComponent parentComponent, String configName) {
		super(componentType, parentComponent, configName);
	}

	@Override
	public void initialize() throws BSCComponentInitializationException {

		super.initialize();

		logger.info("Initializing Cmd Destination");

		this.command = getProperty("command","");
		this.goodExit = getIntProperty("good.exit",0);
	
	}


	@Override
	public BSCMessage onSend(BSCMessage message) throws BSCDestinationException {

		logger.info("Executing CMD Destination onSend method - ");

		BSCMessage outMessage = null;
				
		Process process = null;
		
		switch (message.getAction()) {
		
		case CMD_FILE:
			
			
			
			try {
				
				String command = message.getProperty("CMD.command");
				String args = message.getProperty("CMD.args", "");
				String path = message.getProperty("CMD.path");
				this.logger.info("Initial Arguments - {}", args);
		        
		        String[] argsSplit = args.split("\"?( |$)(?=(([^\"]*\"){2})*[^\"]*$)\"?");
		        this.logger.info("Argument Split - {}", Arrays.toString(argsSplit));
		        
		        String[] commandArray = { command };
		        String[] pathArray = { path };
		        
		        String[] concatArray = (String[])ArrayUtils.addAll(commandArray, argsSplit);
		        String[] finalArray = (String[])ArrayUtils.addAll(concatArray, pathArray);
				
				writeFile(path, true, true, message.getStringMessage());
				logger.info("Command : {} ",Arrays.toString(finalArray)); 
				
				
				process = Runtime.getRuntime().exec(finalArray);

				// set standard error output?
				OutputStreamHandler errorHandler = new OutputStreamHandler(process.getErrorStream(), "stderr", this);

				// set standard out output?
				OutputStreamHandler outputHandler = new OutputStreamHandler(process.getInputStream(), "stdout", this);

				// gather the output
				errorHandler.start();
				outputHandler.start();

				// Wait for the command to exit and get error value
				int exitValue = process.waitFor();

				errorHandler.join();
				outputHandler.join();

				process.getInputStream().close();
				process.getOutputStream().close();

				process.destroy();
				outMessage = message.cloneMessage(false);
			
				message.setProperty("CMD.exit.value", String.valueOf(exitValue));
				
				// Check exit value and handle bad exit values
				if (exitValue == goodExit) {
					logger.debug(" Command completed successfully");
					if (stderrTxt.length() > 0) {
						logger.warn( " Command stderr returned;\n {}", stderrTxt);
					} 
					
					outMessage.loadMessage(stderrTxt+stdoutTxt);
				}
				else {
					throw new  BSCDestinationException(this, BSCErrorLevel.CRITICAL, "Command failed returning: " + exitValue + "\nstderr:" + stderrTxt + "\nstdout:" + stdoutTxt);
				}
			}
			catch (Exception | BSCMessageException e) {
				throw new  BSCDestinationException(this, e, BSCErrorLevel.CRITICAL);
			}
			finally {
				try {
					process.getInputStream().close();
					process.getOutputStream().close();

					process.destroy();
				}
				catch (Exception exc) {
					logger.debug("Error closing process");
				}
			}
			
			
		break;
			
		case CMD_ONLY:
			
			try {
				
				String command = message.getProperty("CMD.command");
				
				this.logger.info("Command to execute -  {}", command);

				process = Runtime.getRuntime().exec(command);

				// set standard error output?
				OutputStreamHandler errorHandler = new OutputStreamHandler(process.getErrorStream(), "stderr", this);

				// set standard out output?
				OutputStreamHandler outputHandler = new OutputStreamHandler(process.getInputStream(), "stdout", this);

				// gather the output
				errorHandler.start();
				outputHandler.start();

				// Wait for the command to exit and get error value
				int exitValue = process.waitFor();

				errorHandler.join();
				outputHandler.join();

				process.getInputStream().close();
				process.getOutputStream().close();

				process.destroy();
				outMessage = message.cloneMessage(false);
			
				message.setProperty("CMD.exit.value", String.valueOf(exitValue));
				
				// Check exit value and handle bad exit values
				if (exitValue == goodExit) {
					logger.debug(" Command completed successfully");
					if (stderrTxt.length() > 0) {
						logger.warn( " Command stderr returned;\n {}", stderrTxt);
					} 
					
					outMessage.loadMessage(stderrTxt+stdoutTxt);
				}
				else {
					throw new  BSCDestinationException(this, BSCErrorLevel.CRITICAL, "Command failed returning: " + exitValue + "\nstderr:" + stderrTxt + "\nstdout:" + stdoutTxt);
				}
			}
			catch (Exception | BSCMessageException e) {
				throw new  BSCDestinationException(this, e, BSCErrorLevel.CRITICAL);
			}
			finally {
				try {
					process.getInputStream().close();
					process.getOutputStream().close();

					process.destroy();
				}
				catch (Exception exc) {
					logger.debug("Error closing process");
				}
			}
			
		break;
		
		default:
			break;
		}
		
		// execute the command
		return outMessage;
		

	}

	private class OutputStreamHandler extends Thread {
		/*
		 * private inner class to consume process output streams.
		 */
		InputStream				is;

		String					type;

		BSCCommandDestination	destination;

		OutputStreamHandler(InputStream is, String type, BSCCommandDestination	destination) {
			this.is = is;
			this.type = type;
			this.destination = destination;
		}

		@Override
		public void run() {
			StringBuffer buffer = new StringBuffer();

			try {
				logger.info(" Reading  {}", type);

				BufferedReader isr = new BufferedReader(new InputStreamReader(is));
				int ch;
				while ((ch = isr.read()) > -1) {
					buffer.append((char) ch);
				}
			}
			catch (IOException ioe) {
				logger.info( " Exception thrown while reading {} message: {}  cause: {}", type , ioe.getMessage() , ioe.getCause());
			}

			logger.info(" Setting  {}  to: ( {} )", type , buffer.toString() );
			if (this.type.equals("stderr")) {
				setStderrTxt(buffer.toString());
				logger.debug(buffer.toString());
			}
			else {
				setStdoutTxt(buffer.toString());
				logger.debug(buffer.toString());
			}
		}
	}
	
	public void writeFile(String path, boolean createDirs, boolean overwrite, String message) throws IOException {
		
		File file = new File(path);
		File dir = file.getParentFile();

		/* Check file existence */
		if (dir != null) {
			if (!dir.exists()) {
				if (createDirs) {
					logger.debug( " Creating directory ( {} )", dir.getAbsolutePath() );
					dir.mkdirs();
				}
				else {
					throw new IOException("Output directory: (" + dir.getAbsolutePath() + ") doesn't exist and this adapter is not configured to create it.");
				}
			}
		}
		else {
			logger.debug( " Using Working Directory:   System.getProperty(user.dir)");
			dir = new File(System.getProperty("user.dir"));
		}

		if (!dir.exists() || !dir.canWrite()) {
			IOException ioe = new IOException("Directory for File (" + dir.getAbsolutePath() + ") can't be created and/or written to!");
			throw ioe;
		}

		/* Check file existence */
		if (file.exists()) {
			if (overwrite) { // delete existing file
				logger.debug(" File ( {} ) exists and will be overwritten!", file.getAbsolutePath() );
				file.delete();
			}
		}

		if (!file.exists()) {
			
			logger.debug(" Creating file ( {} )", file.getAbsolutePath() );

			BufferedOutputStream fileStream = new BufferedOutputStream(new FileOutputStream(path));
		
			fileStream.write(message.getBytes());
		
			fileStream.close();
		}
	}
	

	
	@Override
	public List<BSCMessage> onSend(List<BSCMessage> message) throws BSCDestinationException {
		return null;
	}

}
