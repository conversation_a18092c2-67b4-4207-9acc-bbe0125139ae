package com.bsc.intg.svcs.core.soap;

import java.io.StringReader;

import javax.xml.transform.Source;
import javax.xml.transform.stream.StreamSource;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;

public class BSCSOAPGatewayController extends BSCSOAPControllerBase implements BSCSOAPGatewaySEI{

	protected String serviceName;
    public BSCSOAPGatewayController(BSCComponentType componentType, BSCComponent parentComponent,
            String configName) {
    	
        super(componentType, parentComponent, configName);
        this.serviceName=getProperty("service.name");
    }

	@Override
    public Source invoke(Source request){
		
		BSCSOAPGatewayResponse response = new BSCSOAPGatewayResponse();
		BSCSOAPGatewayRequest requestMsg = new BSCSOAPGatewayRequest(request);
			        	
			try {
			dispatchRequest(this.serviceName,requestMsg,response);
			} catch (Exception e) {
				logger.error("Failed to invoke the request for"+this.serviceName,e);
				response.setResponse("<Error>"+e.getMessage()+"</Error>");
			}
			
			return response.getResponse();
			
					  
    }


}
