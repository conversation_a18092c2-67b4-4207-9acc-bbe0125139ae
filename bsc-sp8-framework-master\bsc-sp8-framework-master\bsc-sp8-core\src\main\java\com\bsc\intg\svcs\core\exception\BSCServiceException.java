package com.bsc.intg.svcs.core.exception;

import com.bsc.intg.svcs.core.BSCComponent;

public class BSCServiceException extends BSCExceptionBase {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;


	public BSCServiceException(BSCComponent source,Throwable ex,BSCErrorLevel l, String message) {
		super(source, ex,l,message);
	};

	public BSCServiceException(BSCComponent source, BSCErrorLevel l, String message) {
		super(source, l, message);
	};
	
	public BSCServiceException(BSCComponent source, Throwable ex, BSCErrorLevel l) {
		super(source, ex,l);
	}
	
}
