package com.bsc.intg.svcs.core.util;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

import org.slf4j.Logger;
import org.springframework.core.env.CompositePropertySource;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.EnumerablePropertySource;
import org.springframework.core.env.PropertySource;

import com.bsc.intg.svcs.core.message.BSCMessage;

public class BSCPropertyHelper {

	public static Map<String,Object> getPropertiesStartingWith( ConfigurableEnvironment env,
			String keyPrefix,  boolean removePrefix )
	{
		Map<String,Object> result = new HashMap<String, Object>();

		Map<String,Object> map = getAllProperties( env );

		for (Entry<String, Object> entry : map.entrySet()) 	{
			String key = entry.getKey();

			if ( key.startsWith( keyPrefix ) )	{
				if (removePrefix) {
					result.put( key.substring(keyPrefix.length()) , entry.getValue() );
				} else {
					result.put( key, entry.getValue() );
				}
			}
		}

		return result;
	}

	public static Map<String, Object> getPropertiesStartingWith(Map<String, Object> properties, String keyPrefix,
			boolean removePrefix) {
		Map<String, Object> result = new HashMap<>();

		for (Entry<String, Object> entry : properties.entrySet()) {
			String key = entry.getKey();

			if (key.startsWith(keyPrefix)) {
				try {
					String value = String.valueOf(entry.getValue());
					if (removePrefix) {
						result.put(key.substring(keyPrefix.length()), BSCCryptoHelper.getSecret(value));
					} else {
						result.put(key, BSCCryptoHelper.getSecret(value));
					}
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
		return result;
	}
	
	public static Map<String,Object> getPropertiesEndingWith( ConfigurableEnvironment env,
			String keyPrefix,  boolean removePrefix )
	{
		Map<String,Object> result = new HashMap<String, Object>();

		Map<String,Object> map = getAllProperties( env );

		for (Entry<String, Object> entry : map.entrySet()) 	{
			String key = entry.getKey();

			if ( key.endsWith( keyPrefix ) )	{
				if (removePrefix) {
					result.put( key.substring(keyPrefix.length()) , entry.getValue() );
				} else {
					result.put( key, entry.getValue() );
				}
			}
		}

		return result;
	}

	public static Map<String,Object> getPropertiesEndingWith( Map<String,Object> properties,
			String keyPrefix,  boolean removePrefix )
	{
		Map<String,Object> result = new HashMap<String, Object>();

			for (Entry<String, Object> entry : properties.entrySet()) 	{
			String key = entry.getKey();

			if ( key.endsWith( keyPrefix ) )	{
				if (removePrefix) {
					result.put( key.substring(keyPrefix.length()) , entry.getValue() );
				} else {
					result.put( key, entry.getValue() );
				}
			}
		}

		return result;
	}
	

	
	public static Map<String,Object> getAllProperties( ConfigurableEnvironment aEnv )
	{
		Map<String,Object> result = new HashMap<String, Object>();
		aEnv.getPropertySources().forEach( ps -> addProperties( result, getAllProperties( ps ) , false) );
		return result;
	}

	public static Map<String,Object> getAllProperties( PropertySource<?> aPropSource )
	{
		Map<String,Object> result = new HashMap<String, Object>();

		if ( aPropSource instanceof CompositePropertySource)
		{
			CompositePropertySource cps = (CompositePropertySource) aPropSource;
			cps.getPropertySources().forEach( ps -> addProperties( result, getAllProperties( ps ), false ) );
			return result;
		}

		if ( aPropSource instanceof EnumerablePropertySource<?> )
		{
			EnumerablePropertySource<?> ps = (EnumerablePropertySource<?>) aPropSource;
			Arrays.asList( ps.getPropertyNames() ).forEach( key -> result.put( key, ps.getProperty( key ) ) );
			return result;
		}

		return result;

	}

	
	public static void addProperties(Map<String, Object> base, Map<String,Object> toBeAdded, boolean overwrite) {
		
		for (Entry<String, Object> entry : toBeAdded.entrySet())
		{
			if ( base.containsKey( entry.getKey() )  && !overwrite)
			{
				continue;
			}

			base.put( entry.getKey(), entry.getValue() );
		}
	}
	
	public static void printMessageContext(BSCMessage message, Logger logger) {
		logger.info("Message Context ID ({}} TIME ({}) OWNER({}) INSTANCE ({})  ",		message.getProperty("CONTEXT.MESSAGE.ID"), 	message.getProperty("CONTEXT.MESSAGE.TIME"), message.getProperty("CONTEXT.MESSAGE.OWNER.NAME"), message.getProperty("CONTEXT.MESSAGE.OWNER.INSTANCE")  ); 
	}
	
	
}
