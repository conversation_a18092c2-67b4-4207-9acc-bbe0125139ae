package com.bsc.intg.svcs.core.boot;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

import org.apache.cxf.Bus;
import org.apache.cxf.bus.spring.SpringBus;
import org.apache.cxf.spring.boot.autoconfigure.CxfAutoConfiguration;
import org.apache.cxf.transport.servlet.CXFServlet;
import org.glassfish.jersey.server.ResourceConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.web.SpringDataWebAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.PropertySource;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;

import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.util.BSCCryptoHelper;
import com.bsc.intg.svcs.core.util.NativeLibrayHandler;
import com.bsc.intg.svcs.core.util.OSCheck;


@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class,  DataSourceTransactionManagerAutoConfiguration.class, SpringDataWebAutoConfiguration.class, HibernateJpaAutoConfiguration.class, CxfAutoConfiguration.class })
@PropertySource( name = "bscServiceProperties", value = { "classpath:base.properties", "classpath:services.properties" })
@EnableWebSecurity
public class BSCServiceApplication extends WebSecurityConfigurerAdapter {

	protected ConfigurableApplicationContext	configAppContext;
	
	protected Map<String,Object> runtime = new HashMap<String,Object>();
	

    @Override
    protected void configure(HttpSecurity http) throws Exception {
       
    	http.csrf().disable().sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS);
    	
    	 if (System.getProperty("bsc.security.basic.auth", "false").equals("false")) {	
    		 System.out.println( "Disabling HTTP basic authentication " );
    		 http.httpBasic().disable();
    	 }
        
    	 if (System.getProperty("bsc.security.ssl.mutual", "false").equals("true")) {
    		 System.out.println( "Enabling SSL mutual authentication" );
    		 http.x509().x509AuthenticationFilter(new BSCX509AuthenticationFilter());
    	 }
        
    }
    

	@Bean(name=Bus.DEFAULT_BUS_ID)
	public Bus cxfBus() {
		Bus cxfBus = new SpringBus(); 
		return cxfBus;
	}

	@Bean
	public ServletRegistrationBean dispatcherServlet() {
		return new ServletRegistrationBean(new CXFServlet(), "/int/services/SOAP/*");
	}

	
	@Bean 
	public ResourceConfig jersyResourceConfig() {
		ResourceConfig jersyResourceConfig = new ResourceConfig(); 
		return jersyResourceConfig;
	}
	
	
	@Bean
	public  BSCServiceConfiguration bscServiceConfiguration(ConfigurableApplicationContext configAppContext, ResourceConfig jersyResourceConfig, Bus cxfBus ) throws  BSCComponentInitializationException {
		return  new BSCServiceConfiguration(configAppContext,jersyResourceConfig,cxfBus);
		
	}
	
	
	public static void start(String ...args) {
		
		System.out.println( "*********** JVM OS ENVIRONMENT ************* " );
		
		Map<String, String> env = System.getenv();
		for (String envName : env.keySet()) {
		    System.out.format("%s=%s%n",     envName,  env.get(envName));
		}
		
		System.out.println( "*********** JVM OS ENVIRONMENT ************* " );

		System.out.println( "*********** JVM SYSTEM PROPERTIES ************* " );
		
		Properties p = System.getProperties();
		Enumeration<?> keys = p.keys();
		while (keys.hasMoreElements()) {
		    String key = (String)keys.nextElement();
		    String value = (String)p.get(key);
		    System.out.println(key + ": " + value);
		}
		
		System.out.println( "*********** JVM SYSTEM PROPERTIES ************* " );
		
		System.out.println( "*********** BUILD INFO ************* " );
		
		BSCBuildInfo.buildInfo();
		
		System.out.println( "*********** BUILD INFO ************* " );
		
		OSCheck.OSType ostype=OSCheck.getOperatingSystemType();
		String libraries= System.getProperty("native.libraries","");
		

		
		switch (ostype) {
		    
			case Windows:
				
				System.out.println("loading the native libraries from adapter framework resources directory- /windows/x64/  " + libraries);
						
				try {
					NativeLibrayHandler.loadLibrariesFromJar("/windows/x64/",libraries,"",".dll");
				} catch (IOException e) {
					
					e.printStackTrace();
					System.exit(-1);
				}
				
				break;
					
		    case MacOS: 
		    	break;
		    case Linux: 

		    	
				System.out.println("loading the native libraries from adapter framework resources directory- /linux/x64/  " + libraries);

				try {
					NativeLibrayHandler.loadLibrariesFromJar("/linux/x64/", libraries,"lib",".so");
				} catch (IOException e) {
					e.printStackTrace();
					System.exit(-1);
				}
				
		    	break;
		    	
		    case Other: 
		    	break;
		}
		
		loadProperties (System.getProperty("bsc.service.sites"));
		
		loadExtProperties(args);
		
		SpringApplication.run(BSCServiceApplication.class, args);
	}
	
	
	public static void loadProperties (String site) {
		
		System.out.println(new Date() + "  Site : " + site + "Configuration before applying the profile properties");
		
		try { 
		
			
			String key									=	null;		
			
			String sitePrefix							=	site  + ".";
			
			Properties effectiveProperties = new Properties();
		
			
			//Load the adapter global properties
			if (!System.getProperty("bsc.service.properties","").equals("")) {
				
				File servicePropertiesFile = new File(System.getProperty("bsc.service.properties"));
			
				if (servicePropertiesFile.exists()) {
						
					System.out.println(new Date() + " " + " service common properties from - " + servicePropertiesFile.getAbsolutePath() + " loaded"  );
					
					Properties serviceProperties = new Properties();
					
					serviceProperties.load(new FileInputStream(servicePropertiesFile));
						
					for (Enumeration<?> e = serviceProperties.propertyNames(); e.hasMoreElements(); ) {
				    	  
			    	  key = (String) e.nextElement();
			    	  
			    	  if (key.startsWith(sitePrefix) ) {
			    		System.out.println("Loading service common property with site prefix Name : " + key + " Value:" + serviceProperties.getProperty(key) );
			            effectiveProperties.setProperty(key, serviceProperties.getProperty(key));
			          } else {
			    		System.out.println("Loading service common property with Name : " + key + " Value:" + serviceProperties.getProperty(key) );
			            effectiveProperties.setProperty(key, serviceProperties.getProperty(key));
			          }
			    	  
					} 
				}	
			}		
			
		
			System.out.println( "Site :" + site + " ----------------effective properties---------------------------"); 
			
			for (Enumeration<?> e = effectiveProperties.propertyNames(); e.hasMoreElements(); ) {
				key = (String) e.nextElement();
				System.out.println( key + "=" +  effectiveProperties.getProperty(key)   ); 
				System.out.println( "Loading effective properties into system"  ); 
				
			}
			
			System.getProperties().putAll(effectiveProperties);			
			
			if ( !System.getProperty("javax.net.ssl.keyStore","").equals("")) {
				System.setProperty("server.ssl.key-store", System.getProperty("javax.net.ssl.keyStore"));
				
				if (System.getProperty("keystore.password","").contains("ss://")) {
					System.setProperty("javax.net.ssl.keyStorePassword", BSCCryptoHelper.getSecret(System.getProperty("keystore.password"))  );
				} 
				System.setProperty("server.ssl.key-store-password", System.getProperty("javax.net.ssl.keyStorePassword"));
			}
			
			if ( !System.getProperty("javax.net.ssl.trustStore","").equals("")) {
				
				System.setProperty("server.ssl.trust-store", System.getProperty("javax.net.ssl.trustStore"));
				
				if (System.getProperty("truststore.password","").contains("ss://")) {
					System.setProperty("javax.net.ssl.trustStorePassword", BSCCryptoHelper.getSecret(System.getProperty("truststore.password"))  );
				}
				
				System.setProperty("server.ssl.trust-store-password", System.getProperty("javax.net.ssl.trustStorePassword"));
			}
					
					
		} catch (Exception e) {
			e.printStackTrace();
		}
		
	}

	public static void loadExtProperties(String[] args){
		
		String appRootDir = System.getProperty("bsc.service.root","");
		String extConfDir =  "";
		
		
		if(!appRootDir.equals("")) extConfDir = appRootDir+"etc/";
		
		String extConfigPath = System.getProperty("bsc.service.ext.config", extConfDir );
		
		if (!"".equals(extConfigPath)) {
			
			File extConfigDir = new File(extConfigPath);
			
			File[] extConfigFiles = extConfigDir.listFiles();
			 
			if (extConfigFiles != null) {
				
				for (File extConfigFile : extConfigFiles) {
					
					if (extConfigFile.isFile()) {
						
						if (extConfigFile.getName().endsWith(".properties")) {
							loadExtProperties(args,extConfigFile);
							
						} 
					}

				}
						
				
			}else{
				System.err.println(new Date() +"Service config files are missing from directory: "+extConfigDir);
			}
		}
		
		if (!"".equals(appRootDir)) {			
			File manifestFile = new File(appRootDir+"manifest.properties");
			loadExtProperties(args,manifestFile);							
		 }		
		
	}
	
	
	public static void loadExtProperties (String[] args, File propFile) {
		
		System.out.println(new Date() + "  Loading configuration for property file : " + propFile);
		
		try {

			String key = null;
			String profilePrefix = "profile.";
			
			String activeProfile = System.getProperty("spring.profiles.active", "");
			
			String serviceInstance = System.getProperty("bsc.instance.name","");
					
			if(!serviceInstance.equals("")) {
				serviceInstance = serviceInstance.substring(serviceInstance.lastIndexOf(".")+1);
			}

			Properties effectiveProperties = new Properties();
			
			if (propFile.exists()) {

				System.out.println(new Date() + " " + " external properties from - " + propFile.getAbsolutePath() + " loading..");
				
				Properties contProperties = new Properties();

				contProperties.load(new FileInputStream(propFile));

				for (Enumeration<?> e = contProperties.propertyNames(); e.hasMoreElements();) {

					key = (String) e.nextElement();
					
					if (key.contains(profilePrefix)) {
						if (key.contains(profilePrefix + activeProfile + ".")) {
							System.out.println("Loading external properties for profile (" + activeProfile + ") : "
									+ key + " Value:" + contProperties.getProperty(key));
							if(!"".equals(serviceInstance) && key.startsWith(serviceInstance)){
								effectiveProperties.setProperty(
										key.substring((serviceInstance+"."+profilePrefix + activeProfile + ".").length()),
										contProperties.getProperty(key));
							}else if(key.startsWith(profilePrefix + activeProfile + ".")){
								effectiveProperties.setProperty(
										key.substring((profilePrefix + activeProfile + ".").length()),
										contProperties.getProperty(key));
							}else{
								System.out.println("Ignoring external properties for key: " + key
										+ " Value:" + contProperties.getProperty(key));
							}
							
						} else {
							System.out.println("Ignoring external properties for non active profile : " + key
									+ " Value:" + contProperties.getProperty(key));
						}

					} else {						
						System.out.println("Loading non profile external properties : " + key + " Value:"
								+ contProperties.getProperty(key));												
						effectiveProperties.setProperty(key, contProperties.getProperty(key));
					}

				}
						
				System.out.println("External property file :" + propFile.getAbsolutePath()
						+ " ----------------effective properties---------------------------");

				for (Enumeration<?> e = effectiveProperties.propertyNames(); e.hasMoreElements();) {
					key = (String) e.nextElement();
					System.out.println(key + "=" + effectiveProperties.getProperty(key));					

				}

				System.getProperties().putAll(effectiveProperties);
			
			}
			

		} catch (Exception e) {
			System.err.println(new Date() + "  Error Loading configuration for property file : " + propFile);
			System.err.println(new Date() + "  Skipping load of configuration for property file : " + propFile +" . Loading failed with error "+e.getMessage());
			e.printStackTrace();
			
		}
		

	}
	
	

	
}
