package com.bsc.intg.svcs.core.locking;

import ch.qos.logback.classic.Level;



import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.recipes.locks.InterProcessMutex;
import org.apache.curator.framework.state.ConnectionState;
import org.apache.curator.utils.CloseableUtils;
import org.slf4j.LoggerFactory;

import com.bsc.intg.svcs.core.zookeeper.ZooKeeperService;


/**
 * 
 * ZooKeeperLockService class wraps locking services using Zookeeper distributed locking recipe InterProcessMutex 
 * 
 * @see ServiceLockService
 * 
 * <AUTHOR> Sircilla
 */

public class ZooKeeperLockService implements LockService,LockServiceProvider  {
	
	private  static CuratorFramework client = null;
	private  static boolean started = false;
	private  static ConnectionState connState;
	private static LockService zooLock = null;
	
	
	
	private ZooKeeperLockService(){
		initialize();
	}
	
	
	public static void initialize(){
		client = ZooKeeperService.getClient();
		started = ZooKeeperService.getStatus();
		connState = ZooKeeperService.getConnState();
	}
	
	
	/**
	 * Get static instance of the lockService
	 * 
	 */
	public static LockService getInstance() {
		if(zooLock!=null)
			return zooLock;
		else{
			zooLock = new ZooKeeperLockService();
			return zooLock;
		}
		
	}
	
	
	/**
	 * Acquires the lock within the given timeout
	 * 
	 * @param timeout
	 * 			int
	 * @param path
	 *         	java.lang.String
	 */
	@Override
	public ServiceLock acquire(int timeout, String path) throws ServiceLockException {
		
		if (started ) {
			
			InterProcessMutex ipcMutex= new InterProcessMutex(client, path);
			
			ServiceLock lock = new ZooKeeperLock(ipcMutex, path);
			
			try {
				
				if ( !lock.acquire(timeout) ) {
					ServiceLockException ale = new ServiceLockException("Locking failed, could not acquire the lock on the path " + path );
					throw ale;
				}

			} catch (Exception e) {
				ServiceLockException ale = new ServiceLockException("Locking failed,  exception occured while aquiring the lock on " + path +   e.getMessage());
				ale.initCause(e);
				throw ale;
			}
	
			return lock;
			
		} else {
			ServiceLockException ale = new ServiceLockException("Locking failed,  exception occured while aquiring the lock on " + path );	
			throw ale;
		}
		
	}
	/**
	 * Acquires the lock and blocks until acquired
	 * 
	 * @param path
	 *         	java.lang.String
	 */
	@Override
	public ServiceLock acquire(String path) throws ServiceLockException {
		
		if (started ) {
			
			InterProcessMutex ipcMutex= new InterProcessMutex(client, path);
			
			ServiceLock lock = new ZooKeeperLock(ipcMutex, path);
			
			try {
				
				if ( !lock.acquire() ) {
					ServiceLockException ale = new ServiceLockException("Locking failed, could not acquire the lock on the path " + path );
					throw ale;
				}

			} catch (Exception e) {
				ServiceLockException ale = new ServiceLockException("Locking failed,  exception occured while aquiring the lock on " + path +   e.getMessage());
				ale.initCause(e);
				throw ale;
			}
	
			return lock;
			
		} else {
			ServiceLockException ale = new ServiceLockException("Locking failed,  exception occured while aquiring the lock on " + path );	
			throw ale;
		}
		
	}

	/**
	 * Acquires the lock and block until acquired
	 * 
	 * @param lock
	 *         	ServiceLock
	 */
	@Override
	public void release(ServiceLock lock) throws ServiceLockException {
	
		try {
			if (lock!=null) lock.release();
		} catch (Exception e) {
			ServiceLockException ale = new ServiceLockException("Locking failed,  exception occured while releasing the lock " + lock.toString() +   e.getMessage());
			ale.initCause(e);
			throw ale;
		}
	}

	/**
	 * Allows you to specify the logging level for Zookeeper/Curator frameworks
	 * 
	 * @param level
	 *         	java.lang.String
	 */
	@Override
	public void setLogLevel(String level) {
	
		ZooKeeperService.setLogLevel(level);
	}

	
	/**
	 * Checks the acquired lock status
	 * If a SUSPENDED state is reported you cannot be certain that you still hold the lock unless you subsequently receive a RECONNECTED state. 
	 * If a LOST state is reported it is certain that you no longer hold the lock
	 * 
	 * @return true if acquired lock is still valid
	 *
	 */
	@Override
	public boolean lockExists(ServiceLock lock) {
		
		if (connState == ConnectionState.LOST) {
			return false;
		}	else if(connState == ConnectionState.SUSPENDED) {
			
			try {
				Thread.sleep(100);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
			if (connState == ConnectionState.RECONNECTED ) 
				return true;
			else
				return false;
		}
		
		return true;
	}

	@Override
	public void start() {
		// TODO Auto-generated method stub
	}

	@Override
	public void shutdown() {
		ZooKeeperService.shutdown();
	}

	/**
	 * Provides LockServiceProvider instance to control start and shutdown of the CuratorFramework  client instance
	 */
	@Override
	public LockServiceProvider getLockServiceProvider() {
		return (LockServiceProvider)zooLock;
	}

	
}



