package com.bsc.intg.svcs.core;

import java.lang.reflect.Constructor;

import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.slf4j.Logger;

import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;


public  class BSCServiceFactory extends BasePooledObjectFactory<BSCService> {
    
    
    Logger logger	   = null;
    String serviceName = null;
    String serviceClassName = null;
    BSCServiceContainer serviceContainer= null;
    int instanceCounter=0;
    
    public BSCServiceFactory(BSCServiceContainer serviceContainer, String serviceName, String serviceClassName) {
    	this.serviceClassName=serviceClassName;
    	this.serviceName=serviceName;
    	this.serviceContainer=serviceContainer;
    	this.logger=((BSCServiceContainerBase)serviceContainer).getLogger();
    }
    
    @SuppressWarnings("unchecked")
	@Override
    public BSCService create() throws Exception {
    	
    	Class<BSCService> tempClass = (Class<BSCService>) Class.forName(serviceClassName);
    	Constructor<BSCService> ctor = tempClass.getDeclaredConstructor(BSCComponentType.class,BSCComponent.class, String.class, String.class);
    	
    	BSCService instance = ctor.newInstance(BSCComponentType.SERVICE, serviceContainer, serviceName, String.valueOf(++this.instanceCounter) );
    	
    	try {
    		logger.info("Constructing/Initializing the service instance {}.{}",serviceName, this.instanceCounter );
    		instance.initialize();
    	} catch (BSCComponentInitializationException  e) {
			logger.error("Failed to initialize service object {} ", e);
			throw new Exception("Failed to initialize service object" + serviceName + "." + this.instanceCounter,   e);
		}
    	
    	return instance;
    }
   
    @Override
    public PooledObject<BSCService> wrap(BSCService service) {
        return new DefaultPooledObject<BSCService>(service);
    }
    @Override
    public void passivateObject(PooledObject<BSCService> service) throws Exception {
        service.getObject().reset();
    }
    @Override
    public boolean validateObject(PooledObject<BSCService> service) {
        return service.getObject().isValid();
    }
}
