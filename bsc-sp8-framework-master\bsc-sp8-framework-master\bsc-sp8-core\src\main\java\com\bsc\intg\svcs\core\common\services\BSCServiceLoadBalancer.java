package com.bsc.intg.svcs.core.common.services;



import java.util.ArrayList;
import java.util.List;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestination;
import com.bsc.intg.svcs.core.BSCService;
import com.bsc.intg.svcs.core.BSCServiceBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCStringUtils;



public class BSCServiceLoadBalancer extends BSCServiceBase {


	public BSCServiceLoadBalancer(BSCComponentType componentType, BSCComponent parentComponent, String configName,
			String instance) {
		super(componentType, parentComponent, configName, instance);
	}

	protected int lastOne = 0;
	
	protected String loadBalancingPolicy = "ACTIVE_PASSIVE";
	
	protected List<String> destinations = new ArrayList<String>();

	
	@Override
	public BSCMessage service (BSCMessage message) throws BSCServiceException {
		
		boolean previousCallSuccess		=	false;
		
		int totalServices 	=  	destinations.size();
		int serviceCounter	=	0;
		
		for ( String serviceName: this.destinations ) {	
		
			if (! previousCallSuccess) {
					
				serviceCounter++;
				
				BSCService service = null;
		
				try {
				
					service = this.getServiceContainer().getService(serviceName);
					logger.info("Executing the service call on {} ", serviceName );
					BSCMessage response= service.execute(message);
					previousCallSuccess=true;
					return response;
					
				} catch (BSCServiceException e) {
					
					previousCallSuccess=false;
					
					logger.warn("Service call failed witn the service {}" , serviceName);
					
					if( totalServices == serviceCounter ) {
						logger.warn("Exchausted with all the services configured " );
						throw new BSCServiceException(this, BSCErrorLevel.CRITICAL, "Failed to invoke the service call on all of the services configured");
					}
					
				} finally {
					
					if(service != null) {
						
						try {
							this.getServiceContainer().releaseService(serviceName, service);
						} catch (Exception e) {
							logger.warn("Failed to release the service to the pool - {}, might cause some issues", serviceName );
						}
					}
					
				}
			}	
		}
		
		return message;
		
	}


	
	@Override
	public void initialize() throws BSCComponentInitializationException {

		super.initialize();
		
		String destinationNames = getProperty("destinations");
		
		this.destinations = BSCStringUtils.convertCommaSeperated(destinationNames);
		
		valid=true;
	}





}
