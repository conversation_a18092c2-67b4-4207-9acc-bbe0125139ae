package com.bsc.intg.svcs.core.vfs;

import java.io.File;
import java.net.URI;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.StringTokenizer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.vfs2.FileSystemException;
import org.apache.commons.vfs2.FileSystemOptions;
import org.apache.commons.vfs2.auth.StaticUserAuthenticator;
import org.apache.commons.vfs2.impl.DefaultFileSystemConfigBuilder;
import org.apache.commons.vfs2.impl.DefaultFileSystemManager;
import org.apache.commons.vfs2.impl.StandardFileSystemManager;
import org.apache.commons.vfs2.provider.ftp.FtpFileProvider;
import org.apache.commons.vfs2.provider.ftp.FtpFileSystemConfigBuilder;
import org.apache.commons.vfs2.provider.local.DefaultLocalFileProvider;
import org.apache.commons.vfs2.provider.sftp.IdentityInfo;
import org.apache.commons.vfs2.provider.sftp.SftpFileProvider;
import org.apache.commons.vfs2.provider.sftp.SftpFileSystemConfigBuilder;
import org.slf4j.Logger;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.util.BSCCryptoHelper;
import com.bsc.intg.svcs.core.vfs.aws.s3.S3FileName;
import com.bsc.intg.svcs.core.vfs.aws.s3.S3FileProvider;
import com.bsc.intg.svcs.core.vfs.aws.s3.S3FileSystemConfigBuilder;
import com.bsc.intg.svcs.core.vfs.azure.AzConstants;
import com.bsc.intg.svcs.core.vfs.azure.AzFileProvider;

public class BSCDefaultVFSProvider extends BSCVFSProvider {
	
	 protected static Map<String,String> secretList = new HashMap<String,String>();
		 
	 public BSCDefaultVFSProvider(Logger logger,String providers,BSCComponent component) throws BSCComponentInitializationException, FileSystemException { 
		 
		 this.logger=logger;
		 
		  fileSystemManager = new DefaultFileSystemManager(); 
			 
		  DefaultLocalFileProvider localFileProvider = new DefaultLocalFileProvider();
		  fileSystemManager.addProvider("file", localFileProvider); 
 		  
		  StringTokenizer  tokenizer = new StringTokenizer(providers, ","); 
		  
		  String provider=null;
		  
		  try { 
			  
			  while (tokenizer.hasMoreTokens()) { 

				  	provider=tokenizer.nextToken();
			
		 			switch (provider) {
		 	         
		 	         case "sftp":
		 	        	 
		 	        	 
		 	        	 SftpFileProvider sftpProvider = new SftpFileProvider(); 
		 	        	 fileSystemManager.addProvider("sftp", sftpProvider);
		 	        	 fileSystemManager.setDefaultProvider(sftpProvider);
		 	        	 break;
		 	
		 	         case "ftp":
		 	        	 FtpFileProvider ftpProvider = new FtpFileProvider();
		 	        	 fileSystemManager.addProvider("ftp", ftpProvider);
		 	        	 fileSystemManager.setDefaultProvider(ftpProvider);
		 	        	 break;
		 	         
		 	         case "azsb":
		 	        	fileSystemManager.addProvider(AzConstants.AZSBSCHEME, new AzFileProvider());
		 	        	break;
		 	         
		 	         case "s3":
		 	        	fileSystemManager.addProvider(S3FileName.SCHEME, new S3FileProvider());
		 	        	 break;
		          }
 			  
 			  
			}
			  
			  
			 
			  
		  } catch (FileSystemException e) { 
			   throw new IllegalStateException(e); 
		  } 
		  
 		  
		 this.logger=logger; 
	 } 
	 
	 
 
	 
		 
	 public static FileSystemOptions createFileSystemOptions(final String prefix, BSCComponent component ) throws BSCComponentInitializationException, Exception{

         FileSystemOptions options = new FileSystemOptions();
         
         String provider = null;
         
         
		 if (prefix.indexOf(".") > 0) 
			 provider = prefix.substring(0,prefix.indexOf("."));
		 else
			 provider=prefix;
		 
         switch (provider) {
         
	         case "sftp":
	        	 
	             SftpFileSystemConfigBuilder.getInstance().setStrictHostKeyChecking(options, component.getProperty(prefix+".stricthostkeychecking","no") );
	             SftpFileSystemConfigBuilder.getInstance().setUserDirIsRoot(options, component.getBooleanProperty(prefix+".userdirisroot",true));
	             SftpFileSystemConfigBuilder.getInstance().setTimeout(options, component.getIntProperty(prefix+".timeout",3000));
	             SftpFileSystemConfigBuilder.getInstance().setPreferredAuthentications(options, component.getProperty(prefix+".preferredauthentications","publickey,password"));
	             SftpFileSystemConfigBuilder.getInstance().setDisableDetectExecChannel(options, component.getBooleanProperty(prefix+".disabledetectexecchannel",true));
	             SftpFileSystemConfigBuilder.getInstance().setSessionTimeoutMillis(options, component.getIntProperty(prefix+".sessiontimeoutms",0));
	             SftpFileSystemConfigBuilder.getInstance().setConnectTimeoutMillis(options, component.getIntProperty(prefix+".connectiontimeoutms",0));
		             
	             String keyPath=component.getProperty(prefix+".keypath");
	             String passphrase = "";
	             
	             if (keyPath != null) {
	            	 passphrase = component.getProperty(prefix+".passphrase");
	            	 //Moved this line for null check
	            	 //passphrase = getSecret(component.getProperty(prefix+".passphrase"));
	            	 IdentityInfo identityInfo = null;
	                 if(passphrase!=null){
	                	 passphrase = getSecret(component.getProperty(prefix+".passphrase"));
	                     identityInfo = new IdentityInfo(new File(keyPath), passphrase.getBytes());
	                 }else{
	                     identityInfo =  new IdentityInfo(new File(keyPath));
	                 }
	                 
	                 SftpFileSystemConfigBuilder.getInstance().setIdentityInfo(options, identityInfo);
	             }
	        	 
	        	 
	        	 break;
	
	         case "ftp":
	        	 	//Added lines for ftp support
	        	 	String username = component.getProperty(prefix + ".username");
	        	    String password = component.getProperty(prefix + ".password");
	        	    StaticUserAuthenticator ftpAuth = new StaticUserAuthenticator(null, username, password);
	        	    DefaultFileSystemConfigBuilder.getInstance().setUserAuthenticator(options, ftpAuth);
	        	 break;
	        	 
	         case "azsb":
	        	 
	        	 String accountName= component.getProperty(prefix + ".account.name"); 
	             String accountKey = "" ;
	            		 
   	             accountKey = getSecret(component.getProperty(prefix+ ".account.key"));
				  
	   	              
	             StaticUserAuthenticator auth = new StaticUserAuthenticator("", accountName, accountKey);
	             DefaultFileSystemConfigBuilder.getInstance().setUserAuthenticator(options, auth); 

	            
	        	 break;
	         case "s3":
	        	 
					String accessKey = getSecret(component.getProperty(prefix + ".access.key"));
					String secretKey = getSecret(component.getProperty(prefix + ".secret.key"));
					BasicAWSCredentials awsCreds = new BasicAWSCredentials(accessKey, secretKey);
					AWSStaticCredentialsProvider credProvider = new AWSStaticCredentialsProvider(awsCreds);
					S3FileSystemConfigBuilder.getInstance().setCredentialsProvider(options, credProvider); 	             
	        	 break;
        	 
         
         }

         return options;
     }
	 

	 public static String createConnectionString(String prefix, BSCComponent component,String inURI) throws Exception {
		 
		 
		 
		
         String provider=null,connectionString="",uri="",hostName="",port="", username="",password="";
         
		 if (prefix.indexOf(".") > 0) 
			 provider = prefix.substring(0,prefix.indexOf("."));
		 else
			 provider=prefix;
		 
         
         if (inURI==null) {
        	 uri= component.getProperty("file.uri");
         } else {
        	 uri=inURI;
         }
         
         switch (provider) {
         	
         	 case "file":   
         		 	uri = uri.replaceAll("file://", "");
	         		if (!uri.startsWith("/")) {
						File relativeFilePath = new File(uri);
						uri = relativeFilePath.getAbsolutePath();
					}
         		 	File file =new File(uri);
         		 	
         			connectionString=file.toURI().toString();
         			break;
         			
	         case "sftp":
	        	 
	        	  String keyPath=component.getProperty(prefix+".keypath");
	              
	              hostName= component.getProperty(prefix+".hostname");
	              port	= component.getProperty(prefix+".port");
	              username = component.getProperty(prefix+".username");
				  
	              password = component.getProperty(prefix+".password","");
	              
	              if ( !password.equals("") ) {
		        		password = getSecret(password);
				  }
	              
	              if (keyPath != null) {	            	  
	            	 if (port != null && port.trim().length() > 0) {
	  					connectionString = "sftp://" + username + "@" + hostName + ":" + port + uri;
	  				} else {
	  					connectionString = "sftp://" + username + "@" + hostName + uri;
	  				}	            	
	              } else {
		            	 String userInfo = username + ":" + password;
			            	
		            	 if(port != null && port.trim().length() > 0){
		            		 int portInt = Integer.parseInt(port);
		            		 URI sftpUri = new URI("sftp", userInfo,  hostName, portInt, uri, null, null);
		            		 
		            		 connectionString=  sftpUri.toString();
		            	 } else {
		            		 URI sftpUri = new URI("sftp", userInfo,  hostName, -1, uri, null, null);
		            		 connectionString= "sftp://" + username + ":" + password + "@" + hostName+uri;
		            	 }
		             }  
              
	        	 break;
	
	         case "ftp":
	        	 //Added lines for ftp support
	              hostName= component.getProperty(prefix+".hostname");
	              port	= component.getProperty(prefix+".port");
	              username = component.getProperty(prefix+".username");
				  
	              password = component.getProperty(prefix+".password","");
	              
	              if ( !password.equals("") ) {
		        		password = getSecret(password);
				  }
	              
	              String userInfo = username + ":" + password;

	              if(port != null && port.trim().length() > 0){
	            	  int portInt = Integer.parseInt(port);
	            	  URI ftpUri = new URI("ftp", userInfo,  hostName, portInt, uri, null, null);

	            	  connectionString=  ftpUri.toURL().toString();
	              } else {
	            	  URI ftpUri = new URI("ftp", userInfo,  hostName, -1, uri, null, null);
	            	  //connectionString= "sftp://" + username + ":" + password + "@" + hostName+uri;
	            	  connectionString=  ftpUri.toURL().toString();
	              }
	        	 break;	
	        	 
	         case "azsb":
	        	 
	        	 String containerName = component.getProperty(prefix+ ".container.name");
	             hostName = component.getProperty(prefix+ ".hostname");  
	             connectionString = String.format("%s://%s/%s%s", AzConstants.AZSBSCHEME, hostName, containerName, uri);
	             
	             break;
	         case "s3":
				String bucketName = component.getProperty(prefix + ".bucket.name");
				String region = component.getProperty(prefix + ".region");
				if(uri.startsWith(S3FileName.SCHEME)){
					connectionString = uri;
				}else{
					connectionString = String.format("%s://%s.%s.amazonaws.com/%s/%s", S3FileName.SCHEME,S3FileName.SCHEME, region,bucketName,uri);

				}
	        	break;         
         }
         
         return connectionString;
         
     }
	 
	 
		public static String getURLStringProperty(String url) {
			

			Pattern pattern = Pattern.compile("(.*://)([^:^/]*)(:\\d*)?(.*)?");
			Matcher matcher = pattern.matcher(url);
		
			matcher.find();
		
			String protocol = matcher.group(1);            
			String domain   = matcher.group(2);
			String port     = matcher.group(3);
		
			StringBuffer urlStringProperty = new StringBuffer();
			
			if(protocol!=null)	urlStringProperty.append(protocol.replaceAll(":", ".").replaceAll("/",""));
			if(domain!=null)	urlStringProperty.append(domain.replaceAll("@", "."));
			if(port!=null)	urlStringProperty.append(port.replaceAll(":", ""));
			
		
			return urlStringProperty.toString();
		}
		
		public static String getProviderFromURL(String url) {
			

			Pattern pattern = Pattern.compile("(.*://)([^:^/]*)(:\\d*)?(.*)?");
			Matcher matcher = pattern.matcher(url);
			matcher.find();
			String protocol = matcher.group(1);            
			return protocol.replaceAll(":", "").replaceAll("/","");
		}
		
		
		public static String getTempFilename(String filename) {
			
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd_HHmmssSSS");

			int i = filename.lastIndexOf('.');
		    String ext = "",name=filename;
		    
		    if(i > 0) {
		    	ext= filename.substring(i);
		    	name=filename.substring(0,i );
		    }
		    
			return name + "_" + simpleDateFormat.format(new Date()) + ext ;
		}
		
		public static String getArchiveFileName(String filename) {
			

			SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd_HHmmssSSS");

			int i = filename.lastIndexOf('.');
		    String ext = "",name=filename;
		    
		    if(i > 0) {
		    	ext= filename.substring(i);
		    	name=filename.substring(0,i );
		    }
		    
			return name + "_" + simpleDateFormat.format(new Date()) + ext ;
		}
		
		
		public static String getSecret(String secretURL) throws Exception {
			
			  String secret = null;
			  try {
				    secret = secretList.get(secretURL);
				    if (secret==null) {
				    	secret = BSCCryptoHelper.getSecret(secretURL);
				    	secretList.put(secretURL, secret);
				    }
					
			  } catch (Exception e) {
					throw e;
			  }
			   
			return secret;
			
		}	
		
		public static void main(String[] args) {
			System.out.println(getTempFilename("abc"));
		}
}
