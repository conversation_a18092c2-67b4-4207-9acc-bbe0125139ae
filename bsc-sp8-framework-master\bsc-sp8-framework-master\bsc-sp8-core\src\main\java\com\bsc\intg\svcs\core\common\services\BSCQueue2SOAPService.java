package com.bsc.intg.svcs.core.common.services;

import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestination;
import com.bsc.intg.svcs.core.BSCIntegrationServiceBase;
import com.bsc.intg.svcs.core.BSCService;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCDestinationAction;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCCryptoHelper;
import com.bsc.intg.svcs.core.util.BSCPropertyHelper;
import com.bsc.intg.svcs.core.util.BSCStringUtils;


public class BSCQueue2SOAPService extends BSCIntegrationServiceBase {

	protected BSCDestination respDestination = null;
	protected BSCDestination reqDestination = null;
	protected int connectionRetry = 0;
	protected int deliveryRetry = 0;
	protected String deliveryRetryCodes = "500,503,403,401";
	protected int retryWait = 0;
	protected static Map<String, String> soapDestinationMap = new HashMap<String, String>();
	BSCMessage reqMessage = null;

	protected String user = null;
	protected String password = null;

	
	public BSCQueue2SOAPService(BSCComponentType componentType, BSCComponent parentComponent, String configName,
			String instance) {
		super(componentType, parentComponent, configName, instance);
	}

	
	@SuppressWarnings("rawtypes")
	@Override
	public BSCMessage service(BSCMessage inMessage) throws BSCServiceException {
		logger.debug("Executing Queue2SOAPService service");

		List<BSCDestination> destList = new ArrayList<BSCDestination>();

			try {

				BSCMessage outMessage = inMessage.cloneMessage(false);
				// Setting the destination properties based on the destination
				// route key
				String path = inMessage.getProperty("destination.route", "");
				String destNames = soapDestinationMap.get(path);

				if (destNames != null && !destNames.isEmpty()) {
					ArrayList<String> destArrayList = BSCStringUtils.convertCommaSeperated(destNames);
					for (String destObjName : destArrayList) {
						destList.add(this.getDestination(destObjName));
					}
				}

				for (BSCDestination dest : destList) {
					outMessage = performSOAPCall(dest, inMessage);
				}

				if (destList.isEmpty() && reqDestination != null)
					outMessage = performSOAPCall(reqDestination, inMessage);

				return outMessage;

			} catch (BSCDestinationException | BSCMessageException | Exception e) {
				inMessage.setException(e);
				throw new BSCServiceException(this, e, BSCErrorLevel.ROLLBACK,
						"Failed to execute the app service: " + e.getMessage());
			}
		
			

	}

	private BSCMessage performSOAPCall(BSCDestination dest, BSCMessage inMessage)
			throws BSCServiceException, BSCDestinationException, BSCMessageException {

		int retryCycle = 0;
		int retryMax = 0;
		BSCMessage respMsg = inMessage.cloneMessage(false);
		boolean isConnectionRetry = true;
		// Sending the message to REST destination and retrying in case of
		// failure
		do {
			Throwable connectionExcp = null;
			try {
				if (retryCycle > 0) {
					logger.warn("Executing retry cycle {} and snoozing for {} second before retrying", retryCycle,
							retryWait);
					if (retryMax - retryCycle < 0) {
						if (isConnectionRetry) {
							throw new BSCServiceException(this, connectionExcp, BSCErrorLevel.ROLLBACK,
									"Retry exhausted so rolling back the message");
						} else {
							if (respMsg.getProperty("failure.propagate.response", "false").equalsIgnoreCase("true"))
								propogateResponse(inMessage, respMsg);
							if (respMsg.getProperty("failure.generate.event", "true").equalsIgnoreCase("false"))
								inMessage.disableEvent();
							throw new BSCServiceException(this, BSCErrorLevel.ROLLBACK,
									"Delivery retry exhausted so rolling back the message");
						}
					}

					try {
						TimeUnit.SECONDS.sleep(retryWait);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}

				}
					inMessage = setAuth(inMessage);
					respMsg = dest.send(inMessage);
				
				logger.info("executed SOAP Destination");

			} catch (BSCDestinationException e) {
				logger.info("Destination invocation failed for CONNECTION error with description [{}] ",
						e.getMessage());
				retryMax = connectionRetry;
				retryCycle++;
				connectionExcp = e;
				isConnectionRetry = true;
				continue;
			}

			if (respMsg != null && !respMsg.getProperty("RESPONSE.SOAP.HEADER.STATUS").startsWith("2")) {
				if (deliveryRetryCodes.contains(respMsg.getProperty("RESPONSE.SOAP.HEADER.STATUS"))) {
					logger.error("Destination invocation failed for DELIVERY error with response status code [{}] ",
							respMsg.getProperty("RESPONSE.SOAP.HEADER.STATUS"));
					retryMax = deliveryRetry;
					retryCycle++;

					isConnectionRetry = false;
					continue;
				} else {
					if (respMsg.getProperty("failure.propagate.response", "false").equalsIgnoreCase("true"))
						propogateResponse(inMessage, respMsg);
					if (respMsg.getProperty("failure.generate.event", "true").equalsIgnoreCase("false"))
						inMessage.disableEvent();

					throw new BSCServiceException(this, BSCErrorLevel.CRITICAL,
							"Failed to process the message with status code: "
									+ respMsg.getProperty("RESPONSE.SOAP.HEADER.STATUS"));
				}

			}

			if (respMsg.getProperty("success.propagate.response", "false").equalsIgnoreCase("true"))
				propogateResponse(inMessage, respMsg);
			break;

		} while (retryMax - retryCycle >= -1);

		return respMsg;

	}
	
	public BSCMessage setAuth(BSCMessage message) throws BSCDestinationException {

		try{
		if (password.contains("ss://"))
			password = BSCCryptoHelper.getSecret(password);
		
		String Auth = user + ":" + password;
		String pwd = Base64.getEncoder().encodeToString(Auth.getBytes());
		Auth = "Basic " + pwd;
		message.setProperty("REQUEST.SOAP.HEADER.Authorization", Auth);
		} catch (Exception e) {
			this.getLogger().error("Error decrypting the password");
			throw new BSCDestinationException(this, e, BSCErrorLevel.FATAL,
					"Authentication Failed " + e.toString());
		}
		return message;
		
	}

	
	private void propogateResponse(BSCMessage inMessage, BSCMessage respMsg)
			throws BSCMessageException, BSCDestinationException {
		logger.info("Response from HTTP Destination is :" + respMsg.getStringMessage());
		respMsg.setProperty("include.properties.prefix", inMessage.getMessageOwner().getConfigName() + ".Meta./");
		respMsg.setAction(BSCDestinationAction.JMS_PUT);
		this.respDestination = this
				.getDestination(respMsg.getProperty("response.destination", this.getProperty("response.destination")));
		respDestination.send(respMsg);
	}

	/**
	 * Initializes all the resources of the service
	 * 
	 * @throws BSCComponentInitializationException
	 *             if any exception occurs during initialization of the
	 *             components or resources of the service.
	 */
	@Override
	public void initialize() throws BSCComponentInitializationException {

		super.initialize();
		valid = true;

		String reqDest = this.getProperty("request.destination");
		if (reqDest != null)
			this.reqDestination = this.getDestination(reqDest);

		this.connectionRetry = Integer.parseInt(this.getProperty("retry.connection", "0"));
		this.deliveryRetry = Integer.parseInt(this.getProperty("retry.delivery", "0"));
		this.deliveryRetryCodes = this.getProperty("retry.delivery.codes", deliveryRetryCodes);
		this.retryWait = Integer.parseInt(this.getProperty("retry.wait", "30"));
		String destRouteMapProp = this.getProperty("destination.route.map", "");
		password = getProperty("password");
		user = getProperty("user");

		// Load the route key to destination mapping

		if (soapDestinationMap.isEmpty()) {
			if (destRouteMapProp != null && !destRouteMapProp.isEmpty()) {
				String[] destAr = destRouteMapProp.split(";");
				for (String destObject : destAr) {
					String destKey = destObject.split("\\:")[0];
					String destValue = destObject.split("\\:")[1];
					soapDestinationMap.put(destKey, destValue);
				}
			}
		}

	}

}
