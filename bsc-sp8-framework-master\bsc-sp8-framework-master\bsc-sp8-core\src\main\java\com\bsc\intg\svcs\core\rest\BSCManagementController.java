package com.bsc.intg.svcs.core.rest;

import javax.inject.Singleton;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.rest.BSCRESTControllerBase;

@Singleton
@Path("/management")
public class BSCManagementController extends BSCRESTControllerBase  {
	

	public BSCManagementController(BSCComponentType componentType, BSCComponent parentComponent, String configName) {
		super(componentType, parentComponent, configName);
	}


	@POST
	@Path("/stop")
	@Consumes({MediaType.TEXT_PLAIN})
	@Produces({MediaType.TEXT_PLAIN})
	public Response stopContainer(String request) {
		this.getServiceContainer().getApplicationService().getContainer("FAC").stop();
		
		return Response.ok(request,MediaType.TEXT_PLAIN).build();
	}
		
	@POST
	@Path("/start")
	@Consumes({MediaType.TEXT_PLAIN})
	@Produces({MediaType.TEXT_PLAIN})
	public Response startContainer(String request) throws BSCComponentInitializationException {
		
		this.getServiceContainer().getApplicationService().getContainer("FAC").start();
		
		return Response.ok(request,MediaType.TEXT_PLAIN).build();
	}
	
	
}
