function initializeNotificationProps(env){
	
	//MES notification config
	if(message.getProperty("JMS.USER.APP_Application")!=null&& message.getProperty("JMS.USER.EVENT_DestinationApp")!=null ){
		if(message.getProperty("JMS.USER.APP_Application").equals("MES") || message.getProperty("JMS.USER.EVENT_DestinationApp").equals("MES")){
			
			if(message.getProperty("JMS.USER.EVENT_SourceApp").equals("MI50") || (message.getProperty("JMS.USER.EVENT_IndexTag")!=null && message.getProperty("JMS.USER.EVENT_IndexTag").equals("MI50") ) ){				
				if(env.equals("PRD")){
					SP("error.notification.email.to","<EMAIL>");					
				}else{
           
					SP("error.notification.email.to","<EMAIL>");					
				}
				
			if(message.getProperty("JMS.USER.EVENT_SourceApp").equals("D525") || (message.getProperty("JMS.USER.EVENT_IndexTag")!=null && message.getProperty("JMS.USER.EVENT_IndexTag").equals("D525") ) ){				
				if(env.equals("PRD")){
					SP("error.notification.email.to","<EMAIL>");					
				}else{
           
					SP("error.notification.email.to","<EMAIL>");					
				}
        
        
        if(message.getProperty("JMS.USER.EVENT_Object").equals("MESReplyListener") || message.getProperty("JMS.USER.EVENT_StackTrace").contains("ErrorCode: 400")|| message.getProperty("JMS.USER.EVENT_StackTrace").contains("ErrorCode: 500")){
						SP("error.notification.email.cc","");
        }
					
			}		
			
		}
	}
	
}