package com.bsc.intg.svcs.core.jdbc;

import java.sql.SQLException;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;

import javax.sql.DataSource;

import org.springframework.boot.autoconfigure.jdbc.DataSourceBuilder;

import com.atomikos.jdbc.AtomikosDataSourceBean;
import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCResourceBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.util.BSCCryptoHelper;
import com.bsc.intg.svcs.core.util.BSCPropertyHelper;

public class BSCJDBCDataSourceResource extends BSCResourceBase {
	
	public DataSource dataSource = null ;
	private String dataSourceClass;

	private String user;
	private String password;
	private String url;
	private String connectionName;
	
	public BSCJDBCDataSourceResource(BSCComponentType componentType, BSCComponent parent, String configName) {
		super(componentType, parent, configName);
	}
	
	public DataSource getDataSource() {
		logger.debug("Returning the data source as {}",dataSource.toString());
		return dataSource;
	}
	
	@Override
	public void initialize(String prefix ) throws BSCComponentInitializationException  { 
			
			this.initialize();
		
			logger.info("Initializing the connection group ({})({}) ", prefix, configName);
		
			this.dataSourceClass		  	= getNonEmptyProperty("datasource.driver.class");	
			this.connectionName   			= getNonEmptyProperty("connection.name");
			
			boolean XA=getBooleanProperty(configName +".XA",false);
	
			user=getGlobalProperty( prefix + "." + connectionName + "datasource.username",getNonEmptyProperty("datasource.username"));
			
			try{
				password = BSCCryptoHelper.getSecret(getGlobalProperty( prefix + "." + connectionName + "datasource.password",getProperty("datasource.password")));
			} catch (Exception e) {
				logger.error("Error decrypting the password");
				throw new BSCComponentInitializationException(this, e, BSCErrorLevel.FATAL,	"Error decrypting the password: " + e.toString());
			}			
			
			url=getGlobalProperty(prefix + "." + connectionName + "datasource.url",getNonEmptyProperty("datasource.url"));			
			
			Map<String,Object>  datasourceProperties = BSCPropertyHelper.getPropertiesStartingWith(getProperties(), "datasource.", true);			
			StringBuffer propertyBuffer = new StringBuffer();
			propertyBuffer.append("\n");
			for (Entry<String, Object> entry : datasourceProperties.entrySet()) 	{
				propertyBuffer.append("\n");
				propertyBuffer.append(entry.getKey());
				propertyBuffer.append("=");
				propertyBuffer.append(entry.getValue());
			}
			propertyBuffer.append("\n");
			logger.info("Datasource Properties  : {}", propertyBuffer.toString());
		
			if (XA) {
				
				AtomikosDataSourceBean ads=new AtomikosDataSourceBean();
			    ads.setXaDataSourceClassName(dataSourceClass);
			    ads.setUniqueResourceName(configName);
			    Properties p = new Properties();
			    p.put("user", user);
			    p.put("URL", url);
			    p.put("password",password);
			    
			    for (Entry<String, Object> entry : datasourceProperties.entrySet()) 	{
					logger.info("Loading datasource property {} : {}",entry.getKey(),entry.getValue() );
			    	p.put(entry.getKey(), entry.getValue());
				}
			    
			    
				ads.setXaProperties(p);    
				dataSource=ads;
			    
			} else {
	
				try {
					DataSourceBuilder dataSourceBuilder = DataSourceBuilder.create();
			        dataSourceBuilder.driverClassName(dataSourceClass);
			        dataSourceBuilder.url(url);
			        dataSourceBuilder.username(user);
			        dataSourceBuilder.password(password);
			       									    
				    dataSource = dataSourceBuilder.build();
				    
				    logger.debug("DataSource {} created successfully", dataSource.getConnection());
					
				} catch (IllegalArgumentException | SecurityException | SQLException  e) {
					throw new BSCComponentInitializationException(this, e, BSCErrorLevel.FATAL,	"Error creating data source: " + e.toString());
				}
			}
				
	}

	@Override
	public void start() throws BSCComponentInitializationException {
		
	}

	@Override
	public void stop() {
		
	}

	@Override
	public void initialize() throws BSCComponentInitializationException {
		// TODO Auto-generated method stub
		
	}




}
