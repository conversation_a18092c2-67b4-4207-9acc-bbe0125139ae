package com.bsc.intg.svcs.core.util;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import org.apache.commons.compress.archivers.ArchiveEntry;
import org.apache.commons.compress.archivers.ArchiveInputStream;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.compress.archivers.zip.ZipArchiveInputStream;
import org.apache.commons.compress.compressors.gzip.GzipCompressorInputStream;
import org.apache.commons.compress.utils.IOUtils;

import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCMessage;

public class BSCFileUtils {

	public static String extractFromCompressedFile(String compressedFilePath, String compressedFileName, String extractionFilePattern)
			throws BSCServiceException, IOException {

		if (compressedFileName.endsWith(".zip")) {
			return extractFromZipFile(compressedFilePath, compressedFileName, extractionFilePattern);
		} else if (compressedFileName.endsWith(".tar.gz")) {
			return extractFromTarGzFile(compressedFilePath, compressedFileName, extractionFilePattern);
		} else if (compressedFileName.endsWith(".tar")) {
			return extractFromTarFile(compressedFilePath, compressedFileName, extractionFilePattern);
		}
		return null;
	}

	private static String extractFromTarFile(String compressedFilePath, String compressedFileName, String extractionFilePattern)
			throws IOException {
		File file = new File(compressedFilePath + compressedFileName);
		try (InputStream fi = Files.newInputStream(file.toPath());
				BufferedInputStream bi = new BufferedInputStream(fi);
				ArchiveInputStream inputStream = new TarArchiveInputStream(bi)) {
			return extractFiles(inputStream, compressedFilePath, extractionFilePattern);
		}
	}

	private static String extractFromTarGzFile(String compressedFilePath, String compressedFileName, String extractionFilePattern)
			throws IOException {
		File file = new File(compressedFilePath + compressedFileName);
		try (InputStream fi = Files.newInputStream(file.toPath());
				BufferedInputStream bi = new BufferedInputStream(fi);
				GzipCompressorInputStream gzi = new GzipCompressorInputStream(bi);
				ArchiveInputStream inputStream = new TarArchiveInputStream(gzi)) {
			return extractFiles(inputStream, compressedFilePath, extractionFilePattern);
		}
	}

	private static String extractFromZipFile(String compressedFilePath, String compressedFileName, String extractionFilePattern)
			throws IOException {
		File file = new File(compressedFilePath + compressedFileName);
		try (InputStream fi = Files.newInputStream(file.toPath());
				BufferedInputStream bi = new BufferedInputStream(fi);
				ArchiveInputStream inputStream = new ZipArchiveInputStream(bi)) {
			return extractFiles(inputStream, compressedFilePath, extractionFilePattern);
		}
	}

	private static String extractFiles(ArchiveInputStream stream, String compressedFilePath, String extractionFilePattern)
			throws IOException {
		ArchiveEntry entry;
		while ((entry = stream.getNextEntry()) != null) {
			if (!entry.isDirectory()) {
				String entryName = entry.getName();
				if (entryName.matches(extractionFilePattern)) {
					if (stream.canReadEntryData(entry)) {
						BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(compressedFilePath + entryName));
						IOUtils.copy(stream, bos);
						bos.close();
					} else {
						return null;
					}
					return compressedFilePath + entryName;
				}
			}
		}
		return null;
	}
	
	public static void deleteExtractedFile(String compressedFilePath, String extractedFilePath) {
		try {
			File compressedFile = new File(compressedFilePath);
			if (compressedFile.exists()) {
				compressedFile.delete();
			}
			File extractedFile = new File(extractedFilePath);
			if (extractedFile.exists()) {
				extractedFile.delete();
			}
		} catch (Exception e) {
			// Ignore
		}
		
	}

	public static void prepareBSCMessageWithExtractedFile(String extractedFilePath, boolean extractContent,
			BSCMessage message) throws BSCMessageException, IOException {
		Path path = Paths.get(extractedFilePath);
		if (extractContent) {
			byte[] content = Files.readAllBytes(path);
			message.reset();
			message.loadMessage(content);
		} else {
			message.setProperty("EXTRACTED.FILE.NAME", path.getFileName().toString());
			message.setProperty("EXTRACTED.FILE.URL", "file:///" + extractedFilePath);
			message.reset();
			message.loadMessage("file:///" + extractedFilePath);
		}

	}
	
}
