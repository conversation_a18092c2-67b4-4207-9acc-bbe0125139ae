package com.bsc.intg.svcs.core.boot;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

import org.apache.cxf.Bus;
import org.glassfish.jersey.server.ResourceConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MapPropertySource;

import com.bsc.intg.svcs.core.BSCApplicationServiceConfig;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.util.BSCStringUtils;


public class BSCServiceConfiguration  {

	private static final Logger logger = LoggerFactory.getLogger(BSCServiceConfiguration.class);
	
	protected List<BSCApplicationServiceConfig> applicationServiceConfigs = new ArrayList<BSCApplicationServiceConfig>();

	protected Map<String,Object> runtime = new HashMap<String,Object>();
	
	public BSCServiceConfiguration(ConfigurableApplicationContext configAppContext, ResourceConfig jersyResourceConfig, Bus cxfBus ) throws  BSCComponentInitializationException {
		
		runtime.put("configAppContext",configAppContext);
		runtime.put("jersy", new BSCRESTRuntime("jersy", (Object) jersyResourceConfig) );
		runtime.put("cxf", new BSCSOAPRuntime("cxf", (Object) cxfBus));
		runtime.put("quartz", new BSCSchedulerRuntime("quartz"));
		
		
		
		System.out.println("************************* ACTIVE APP PROPERTIES ******************************");

		ConfigurableEnvironment env = configAppContext.getEnvironment();
        List<MapPropertySource> propertySources = new ArrayList<>();

        env.getPropertySources().forEach(it -> {
            if (it instanceof MapPropertySource && it.getName().contains("applicationConfig")) {
                propertySources.add((MapPropertySource) it);
            }
        });

        propertySources.stream()
                .map(propertySource -> propertySource.getSource().keySet())
                .flatMap(Collection::stream)
                .distinct()
                .sorted()
                .forEach(key -> {
                    try {
                        System.out.println(key + "=" + env.getProperty(key));
                    } catch (Exception e) {
                        logger.warn("{} -> {}", key, e.getMessage());
                    }
                });
        System.out.println("******************************************************************************");

		
		

		String configNames = configAppContext.getEnvironment().getProperty("bsc.config.name");
		
		ArrayList<String> configs=BSCStringUtils.convertCommaSeperated(configNames);
		
		for (String configObjName: configs) {
			
			logger.info("Creating BSC applications services config {}", configObjName);
			BSCApplicationServiceConfig applicationServiceConfig = new BSCApplicationServiceConfig( BSCComponentType.CONFIG,runtime,configObjName );
			
			logger.info("Initializing BSC applications services config {}", applicationServiceConfig);
			applicationServiceConfig.initialize();
			
			logger.info("Adding BSC applications services config {}", applicationServiceConfig);
			applicationServiceConfigs.add(applicationServiceConfig);
			
			logger.info("Starting BSC applications services config {}", applicationServiceConfig);
			applicationServiceConfig.start();
			
			
		}
		
	}
	
	public List<BSCApplicationServiceConfig> getApplicationConfigs() {
		return applicationServiceConfigs;
	}

	
	@PreDestroy
	public void destroy() throws Exception {
		logger.info("*BSC application configuration bean is destroyed......");
	}

	
	@PostConstruct
	public void init() throws Exception {
		logger.info("*BSC application configuration bean is created......");
	}
	

	

}
