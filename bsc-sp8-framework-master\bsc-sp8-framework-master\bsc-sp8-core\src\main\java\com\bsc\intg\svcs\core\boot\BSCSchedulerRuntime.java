package com.bsc.intg.svcs.core.boot;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class BSCSchedulerRuntime {
	
	private static final Logger logger = LoggerFactory.getLogger(BSCServiceConfiguration.class);
	
	String runtime = null ;
	Object runtimeObject = null;
	
	public BSCSchedulerRuntime(String runtime) {
		this.runtime=runtime;
	}
	
	public BSCScheduler getScheduler() throws Exception {
			BSCScheduler scheduler=null;
			
			if (runtime.equals("quartz")) {
				org.quartz.impl.StdSchedulerFactory qsf=new org.quartz.impl.StdSchedulerFactory();
				org.quartz.Scheduler qs = qsf.getScheduler();
				scheduler=new BSCQuartzScheduler(qs);
			}
			
			return scheduler;
	}
	
	
}
