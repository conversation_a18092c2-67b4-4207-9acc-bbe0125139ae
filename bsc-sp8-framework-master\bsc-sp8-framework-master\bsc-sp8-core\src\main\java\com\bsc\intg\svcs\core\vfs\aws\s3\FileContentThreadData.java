package com.bsc.intg.svcs.core.vfs.aws.s3;

import org.apache.commons.vfs2.FileSystemException;
import org.apache.commons.vfs2.RandomAccessContent;

import java.io.InputStream;
import java.util.ArrayList;

/**
 * Holds the data which needs to be local to the current thread
 */
class FileContentThreadData {

	private final ArrayList<InputStream> inputStreamList = new ArrayList<>();
	private final ArrayList<RandomAccessContent> randomAccessContentList = new ArrayList<>();
	private DefaultFileContent.FileContentOutputStream outputStream;

	FileContentThreadData() {
	}

	void addInstr(final InputStream inputStream) {
		this.inputStreamList.add(inputStream);
	}

	void setOutstr(final DefaultFileContent.FileContentOutputStream outputStream) {
		this.outputStream = outputStream;
	}

	DefaultFileContent.FileContentOutputStream getOutstr() {
		return this.outputStream;
	}

	void addRastr(final RandomAccessContent randomAccessContent) {
		this.randomAccessContentList.add(randomAccessContent);
	}

	int getInstrsSize() {
		return this.inputStreamList.size();
	}

	public Object removeInstr(final int pos) {
		return this.inputStreamList.remove(pos);
	}

	public void removeInstr(final InputStream inputStream) {
		this.inputStreamList.remove(inputStream);
	}

	public Object removeRastr(final int pos) {
		return this.randomAccessContentList.remove(pos);
	}

	public void removeRastr(final RandomAccessContent randomAccessContent) {
		this.randomAccessContentList.remove(randomAccessContent);
	}

	public boolean hasStreams() {
		return inputStreamList.size() > 0 || outputStream != null || randomAccessContentList.size() > 0;
	}

	public void closeOutstr() throws FileSystemException {
		outputStream.close();
		outputStream = null;
	}

	int getRastrsSize() {
		return randomAccessContentList.size();
	}
}
