package com.bsc.intg.svcs.core.jms;



import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestination;
import com.bsc.intg.svcs.core.BSCDestinationBase;
import com.bsc.intg.svcs.core.BSCIntegrationService;
import com.bsc.intg.svcs.core.BSCIntegrationServiceBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCStringUtils;



public class BSCMultipleQueueDestination extends BSCDestinationBase {

	
	protected int lastOne = 0;
	
	protected String loadBalancingPolicy = "ACTIVE_PASSIVE";
	protected List<String> destinations = new ArrayList<String>();
	Map<String, BSCDestination> destinationMap = new LinkedHashMap<String,BSCDestination >();
	int totalDestinations =  0;
	int currDest=0;

	public BSCMultipleQueueDestination(BSCComponentType componentType, BSCComponent parentComponent, String configName) {
		super(componentType, parentComponent, configName);
	}

	@Override
	public void initialize() throws BSCComponentInitializationException {
		super.initialize();
		String destinationNames		=   getProperty("destinations");
		
		destinations			=   BSCStringUtils.convertCommaSeperated(destinationNames);
		
		for (String destinationObjName : destinations) {
			
			BSCDestination destination = null;
			
			logger.info("Creating the destination {}", destinationObjName);
			
			String destinationClassName=getGlobalProperty(destinationObjName+".class");
			
			try {
				Class<?> sc = Class.forName(destinationClassName);
				Constructor<?>	ctor=sc.getConstructor(BSCComponentType.class, BSCComponent.class, String.class );
				destination= (BSCDestination) ctor.newInstance(BSCComponentType.DESTINATION, this.getService(),destinationObjName);
			} catch (Exception e) {
				logger.error("Error creating the destination {}", destinationObjName,e);
				throw new BSCComponentInitializationException(this, e, BSCErrorLevel.CRITICAL, "Destination creation failed -" + destinationObjName);
			}
		
			logger.info("Initializing the destination {}", destinationObjName);
		
			

			destination.initialize();
			
			logger.info("Adding the destination {} to list", destinationObjName);
			
			destinationMap.put(destinationObjName, destination);
			
		}
		
		totalDestinations = this.destinations.size();
		if ( totalDestinations == 0 ) {
			throw new BSCComponentInitializationException(this, BSCErrorLevel.CRITICAL,  "No destinations are defined for the destination load balancer " + getInstanceName() );
		}
	}
	
	public BSCDestination getDestination(String destinationObjName) {
		
		return destinationMap.get(destinationObjName);
	}

	@Override
	public BSCMessage onSend(BSCMessage message) throws BSCDestinationException {
		
		BSCMessage response = null;
		String destinationName = null;
		BSCDestination destination= null;
		for(int cnt=currDest;cnt<totalDestinations;cnt++){
			try {
				
				destinationName = this.destinations.get(currDest); 
				logger.info("Executing the destination call on {} ", destinationName );
				response=null;
				
				destination =this.getDestination(destinationName);
				response= destination.send(message);
				currDest++;
				if(currDest == totalDestinations) {
				    currDest = 0;
				}   
				break;
				
			} catch (RuntimeException | BSCDestinationException e) {

				logger.warn("destination call failed with the destination {}" , destinationName);

				if( response !=null ) response.close();
				currDest=cnt;
				if(totalDestinations==cnt+1) {
					cnt =0;
					logger.warn("Exchausted with all the destinations configured " );
					throw new BSCDestinationException(this, e, BSCErrorLevel.CRITICAL, "Failed to invoke the destination service call on all of the destinations configured" + e.toString());
				}
			} 
		}
		return response;

	}


	@Override
	public List<BSCMessage> onSend(List<BSCMessage> message) throws BSCDestinationException {
		return null;
	}


	

	

}
