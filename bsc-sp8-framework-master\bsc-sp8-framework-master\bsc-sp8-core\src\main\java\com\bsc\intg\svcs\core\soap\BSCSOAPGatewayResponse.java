package com.bsc.intg.svcs.core.soap;

import java.io.StringReader;

import javax.xml.transform.Source;
import javax.xml.transform.stream.StreamSource;

import com.bsc.intg.svcs.core.message.BSCModel;

public class BSCSOAPGatewayResponse implements BSCModel {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	public String response;

	public BSCSOAPGatewayResponse() {
		super();
	}
	
	public Source getResponse(){
		
		return new StreamSource(new StringReader(response));
	}
	
	public void setResponse(String response){
		
		this.response=response;
	}
	
	
	

}
