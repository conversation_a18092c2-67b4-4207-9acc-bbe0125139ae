/*
 * Added by <PERSON><PERSON><PERSON> as a part of VFS Quartz Scheduler
 */

package com.bsc.intg.svcs.core.boot;

import java.text.ParseException;
import java.util.Date;
import java.util.TimeZone;

import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.quartz.CronExpression;
import org.slf4j.Logger;

public class BSCCronExpression extends CronExpression{
	private static final long serialVersionUID = 1L;
	CronExpression _orgCronExpression;
	
    public  BSCCronExpression(String cronExpression) throws ParseException
    {
        super(cronExpression);
		if (System.getenv("TZ") != null)
			setTimeZone(TimeZone.getTimeZone(StringUtils.substringAfter((System.getenv("TZ")), "zoneinfo/")));
		else
			setTimeZone(TimeZone.getDefault()); 
        _orgCronExpression = new CronExpression(cronExpression);

    }
	@Override
    public Date getTimeAfter(Date date)
    {
		
    	 Date nextDate = super.getTimeAfter(date);
    	    if(nextDate == null){
    	      return null;
    	    }
    	    DateTime date1 = new DateTime(nextDate);
    	    if (getTimeZone().inDaylightTime(date1.toDate()) && !getTimeZone().inDaylightTime(date)) {

    	      DateTimeZone dtz = DateTimeZone.forTimeZone(getTimeZone());
    	      DateTime dstEndDateTime = new DateTime(new Date(dtz.nextTransition(date.getTime())));

    	      int dstEndHour = dstEndDateTime.getHourOfDay();
    	      int dstDuration = (dtz.getOffset(date1.getMillis()) - dtz.getStandardOffset(date1.getMillis())) / (60 * 60 * 1000);
    	      int hour = date1.getHourOfDay();
    	     
    	      // Verifies if the scheduled hour is within a phantom hour (dissapears upon DST change)
    	      if (hour < dstEndHour && hour >= dstEndHour-dstDuration){       
    	        // Verify if the date is  a skip, otherwise it is a date in the future (like threads that run once a year)
    	        if(dstEndDateTime.getDayOfYear() == date1.minusDays(1).getDayOfYear()){
    	     
    	          return dstEndDateTime.toDate();
    	        }else{
    	          return nextDate;
    	        }

    	      }else{
    	        return nextDate;
    	      }
    	    } else{
    	        return nextDate;
    	    }	      }


}
