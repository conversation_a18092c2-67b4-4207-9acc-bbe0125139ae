
package com.bsc.intg.svcs.core.test;

import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
import org.apache.commons.vfs2.FileObject;
import org.apache.commons.vfs2.impl.StandardFileSystemManager;
import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestinationBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.message.BSCMessage;


public class BSCFileDestinationStub extends BSCDestinationBase {

	public BSCFileDestinationStub(BSCComponentType componentType, BSCComponent parentComponent, String configName) {
		super(componentType, parentComponent, configName);
	}

	public void initialize() throws BSCComponentInitializationException {

		super.initialize();

		logger.info("Initializing File Destination");		

	}

	@Override
	public BSCMessage onSend(BSCMessage message) throws BSCDestinationException {
		logger.info("Executing File Destination onSend method - ");
		
		try{

			StandardFileSystemManager fsManager = new StandardFileSystemManager();
			fsManager.init();
			
			logger.info("Write data to - File Path - {} , File Name - {} ", getProperty("url") , getProperty("file.name"));	
			FileObject fileObj = fsManager.resolveFile(getProperty("url") + getProperty("file.name"));

			OutputStream out = fileObj.getContent().getOutputStream(Boolean.valueOf(getProperty("append")));
			out.write(message.getStringMessage().getBytes());
			out.close();
			fileObj.close();
			
			logger.info("File write complete ");
			fsManager.close();

		}catch (IOException | BSCMessageException  e) {
			logger.error("File creation failure. " + e.toString());
			throw new BSCDestinationException(this, e, BSCErrorLevel.CRITICAL,"File creation failure." + e.toString());
		}

		return message;        
	}

	@Override
	public List<BSCMessage> onSend(List<BSCMessage> message) throws BSCDestinationException {
		return null;
	}



}
