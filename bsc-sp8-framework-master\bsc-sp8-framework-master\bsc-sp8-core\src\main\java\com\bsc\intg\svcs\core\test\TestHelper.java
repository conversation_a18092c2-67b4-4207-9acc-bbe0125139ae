package com.bsc.intg.svcs.core.test;

/**
 * 
 * Test helper to run test cases* 
 * 
 * <AUTHOR>
 *         <p>
 *         $Revision: 1.2 $
 *         <p>
 */

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.lang.reflect.Method;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.atomic.AtomicReference;

import javax.jms.Destination;
import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.QueueConnectionFactory;
import javax.jms.Session;
import javax.jms.TextMessage;
import javax.net.ssl.HttpsURLConnection;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.xml.soap.MessageFactory;
import javax.xml.soap.MimeHeaders;
import javax.xml.soap.SOAPConnection;
import javax.xml.soap.SOAPConnectionFactory;
import javax.xml.soap.SOAPException;
import javax.xml.soap.SOAPMessage;

import org.apache.commons.io.FileUtils;
import org.junit.Assert;
import org.junit.rules.TestName;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.jms.core.MessageCreator;

import com.bsc.intg.svcs.core.jms.JMSMQProvider;

public class TestHelper {

	private Logger logger =  null;
	
	String sep = File.separator;
	String workingDir = System.getProperty("user.dir");
	String inputDir = workingDir + sep + "test"+ sep + "input" + sep;
	String outputDir = workingDir + sep + "test"+ sep + "output" + sep;
	String expectedDir = workingDir + sep +"test" + sep + "expected" + sep;
	String reportDir = workingDir + sep +"test" + sep + "report" + sep;
	
	protected String serviceName = null,bootClassName=null;
	
	public TestHelper(String testCase) {
		logger=LoggerFactory.getLogger(testCase + ".TestHelper");
	}

	public TestHelper(String serviceName, String bootClassName) {
		this.serviceName=serviceName;
		this.bootClassName=bootClassName;
		logger=LoggerFactory.getLogger( serviceName );
	}

	
	public void clnOutputDir() throws Exception {

		logger.info("Delete all files from output directory - {}", outputDir);
		FileUtils.cleanDirectory(new File(outputDir));	

	}
	
	public void clnReportDir() throws Exception {

		logger.info("Delete all files from report directory - {}", reportDir);
		FileUtils.cleanDirectory(new File(reportDir));	

	}
	
	

	public void invokeSpringBoot(String clsName, String methodName,String[] prgArgs) throws Exception{
		
		Class<?> cls = Class.forName(clsName);
		Object obj = cls.newInstance();			
		Method method = cls.getDeclaredMethod(methodName,new Class[]{String[].class});

		logger.info("Invoking  method - {} from class - {} with program arguments - {}", methodName, clsName, prgArgs);
		method.invoke(obj, new Object[]{prgArgs});

		logger.info("Sleep 10 seconds while method - {} is invoked successfully", methodName);		
		Thread.sleep(10000);
	}


	public String readInputData(String tcName) throws Exception {

		FileInputStream fis = null;
		FileWriter fout = null;
		String inputFileName = inputDir + tcName + ".txt";

		try {

			File inputFile = new File(inputFileName);

			fis = new FileInputStream(inputFile);
			byte b[] = new byte[(int) inputFile.length()];
			fis.read(b);
			String inputContent = new String(b);
			inputContent = inputContent.trim();

			logger.debug("Input file - " + inputFile.getAbsolutePath());
			logger.debug("Input Data - " + inputContent);

			return inputContent;

		} 
		catch (Exception e) {
			logger.error("Error reading input data - ", e);
			e.printStackTrace();
			throw new Exception("Error reading input data - ", e);
		} 
		finally {

			if (fis != null)
				fis.close();
			if (fout != null)
				fout.close();
		}

	}

	public ArrayList<String> getExpectedFilesList(String tcName){

		ArrayList<String> tcFiles = new ArrayList<String>();
		File[] expectedFiles = new File(expectedDir).listFiles();


		for(File f : expectedFiles){
			if(f.getName().toLowerCase().contains(tcName.toLowerCase()))
			{
				tcFiles.add(f.getName());
				logger.info("Expected file - {}", f.getName());
			}
		}

		return tcFiles;

	}

	public boolean checkFileExstAndCompare(String fileName, ArrayList<String> acceptedDiff) throws Exception{

		boolean status = false;	

		File outputFile = new File(outputDir + fileName); 

		logger.info("Check if expected file is available in output folder - {}", fileName);

		if (outputFile.exists()) { 
			logger.info("Compare file - {}", outputFile.getName());
			status = compareFile(outputFile.getName(),acceptedDiff);
		}
		else
		{	
			logger.info("Sleep 3 seconds for process to complete execution");
			Thread.sleep(3000);	

			int maxRetry = 3;
			int retryCount = 1;
			boolean exists = false;
			while (!((retryCount > maxRetry + 1) || exists))
			{ 
				exists = outputFile.exists();  

				if (!exists) { 

					logger.info("Sleep 10 seconds for process to complete execution");
					Thread.sleep(10000);			
				}
				else
				{	
					exists = true;
					logger.info("Compare file - {}", outputFile.getName());
					status = compareFile(outputFile.getName(),acceptedDiff);
				}

				retryCount = retryCount + 1;
			}

			if(!exists){
				logger.error("Output file is not available - " + outputFile.toString());
				throw new Exception("Output file is not available - " + outputFile.toString());
			}
		}

		return status;
	}


	public boolean compareFile(String fileName, ArrayList<String> acceptedDiff) throws Exception{

		boolean cmpStat = false;

		String outputFile = outputDir + fileName;
		String expectedFile = expectedDir + fileName;
		BufferedWriter writer = new BufferedWriter(new FileWriter(reportDir + fileName));
		BufferedReader outputBufferReader = new BufferedReader(new FileReader(outputFile));
		BufferedReader expectedBufferReader = new BufferedReader(new FileReader(expectedFile));
		String sCurrentLine;
		List<String> outputList = new ArrayList<String>();
		List<String> expectedList = new ArrayList<String>();
		int outputLineCount=1;
		int expectedLineCount =1;

		acceptedDiff.add("JMS.");
		acceptedDiff.add("JMS_");
		acceptedDiff.add("HTTP.");
		acceptedDiff.add("HTTP_");

		String ignoreCondition ="(!(";

		for(int idx=0;idx<acceptedDiff.size();idx++){

			if(idx==0){
				ignoreCondition = ignoreCondition + "sCurrentLine.contains(\"" + acceptedDiff.get(idx) + "\")";
			}
			else{
				ignoreCondition = ignoreCondition + "||sCurrentLine.contains(\"" + acceptedDiff.get(idx) + "\")";
			}

		}
		ignoreCondition = ignoreCondition + "))";

		logger.info("ignore" + ignoreCondition);

		ScriptEngineManager sem = new ScriptEngineManager();
		ScriptEngine engine = sem.getEngineByName("JavaScript");

		while ((sCurrentLine = outputBufferReader.readLine()) != null) {

			engine.put("sCurrentLine", sCurrentLine);
			String result = engine.eval(ignoreCondition).toString();
			if(result.equals("true")){    	 
				outputList.add("Line Number " + outputLineCount +" :"+ sCurrentLine);
				outputLineCount++;
			}

		}
		while ((sCurrentLine = expectedBufferReader.readLine()) != null) {
			engine.put("sCurrentLine", sCurrentLine);
			String result = engine.eval(ignoreCondition).toString();
			if(result.equals("true")){    	 
				expectedList.add("Line Number " + expectedLineCount +" :"+ sCurrentLine);
				expectedLineCount++;
			}
		}
		List<String> outputarrayList = new ArrayList<String>(outputList);
		outputarrayList.removeAll(expectedList);
		List<String> expectedarrayList = new ArrayList<String>(expectedList);
		expectedarrayList.removeAll(outputList);
		if(outputarrayList.size()>0 ||expectedarrayList.size()>0)
		{
			writer.write("Output/Expected file contents doesn't match. Below are the differences\n");
			writer.write("=========================================================================================================================================\n");
			writer.write("Contents of output file(" + outputFile + ") - \n");
			for(int i=0;i<outputarrayList.size();i++){
				writer.write(outputarrayList.get(i)+"\n"); 
			}
			writer.write("Contents of expected file(" + expectedFile + ") - \n");
			for(int i=0;i<expectedarrayList.size();i++){
				writer.write(expectedarrayList.get(i)+"\n"); 
			}
			writer.write("=========================================================================================================================================\n");
		}
		else
		{
			writer.write("Output/Expected file contents match\n");
			cmpStat = true;
		}

		outputBufferReader.close();
		expectedBufferReader.close();
		writer.close();
		return cmpStat;

	}
	
	public void invokeRESTCall(String tcName, String requestType, String URL, HashMap<String, String> requestHeaders,
			String requestBody) throws IOException {
		
		HttpURLConnection conn = null;
			
		for (HashMap.Entry<String, String> e : requestHeaders.entrySet()) {
			if(e.getKey().startsWith("?") && requestType.equals("GET")){
				URL = URL + e.getKey() + "=" + e.getValue();	
			}
		}
		
		for (HashMap.Entry<String, String> e : requestHeaders.entrySet()) {
			if(e.getKey().startsWith("&") && requestType.equals("GET")){
				URL = URL + e.getKey() + "=" + e.getValue();
				
			}
		}
		logger.info("URL - " + URL);

		URL urlObj = new URL(URL);
		if (URL.contains("https"))
			conn = (HttpsURLConnection) urlObj.openConnection();
		else
			conn = (HttpURLConnection) urlObj.openConnection();
		
		for (HashMap.Entry<String, String> e : requestHeaders.entrySet()) {
			if(!(e.getKey().startsWith("?") || e.getKey().startsWith("&"))){
			conn.setRequestProperty(e.getKey(), e.getValue());
			}
		}

		conn.setRequestProperty("connection", "Keep-Alive");
		conn.setRequestMethod(requestType);

		// HTTP request Body
		if (requestType.equals("POST") || requestType.equals("PUT")) {
			if (requestBody.length() > 0) {
				conn.setDoOutput(true);
				OutputStream os = conn.getOutputStream();
				os.write(requestBody.getBytes());
				os.flush();
			}
		}

		int responseCode = conn.getResponseCode();
		BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
		String inputLine;
		StringBuffer responseData = new StringBuffer();

		while ((inputLine = in.readLine()) != null) {
			responseData.append(inputLine);
		}
		in.close();

		BufferedWriter writer = new BufferedWriter(new FileWriter(outputDir + "RESTResponse_" + tcName + "_01.txt"));

		if (conn.getResponseCode() == HttpURLConnection.HTTP_OK) {
			logger.info("REST call is invoked succesfully with response code - {} ", responseCode);
		} else {
			logger.info("REST call failed with error code - {} ", responseCode);
		}
		
		logger.info("Response Data", responseData.toString());
		writer.write(responseData.toString());
		writer.close();

		conn.disconnect();
	}

	public void invokeSOAPCall(String tcName, HashMap<String, String> requestHeaders, String Url, String requestBody) throws SOAPException,IOException{
		
		InputStream is = new ByteArrayInputStream(requestBody.getBytes());
		SOAPMessage request = MessageFactory.newInstance().createMessage(null, is);
		
		 MimeHeaders headers = request.getMimeHeaders();
	     for (HashMap.Entry<String, String> e : requestHeaders.entrySet()) {				
	    	 headers.addHeader(e.getKey(), e.getValue());		
		 }  
	     request.saveChanges();
	     
		 SOAPConnectionFactory soapConnectionFactory = SOAPConnectionFactory.newInstance();
         SOAPConnection soapConnection = soapConnectionFactory.createConnection();

         // Send SOAP Message to SOAP Server
         SOAPMessage soapResponse = soapConnection.call(request, Url);
         // Print the SOAP Response
         logger.info("Response SOAP Message:" );
         
         BufferedWriter writer = new BufferedWriter(new FileWriter(outputDir + "SOAPResponse_" + tcName + "_01.txt"));
         ByteArrayOutputStream baos = new ByteArrayOutputStream();
         soapResponse.writeTo(baos);
         logger.info("Response SOAP Message: " + baos.toString());
         writer.write(baos.toString());
         writer.close();
         
         soapConnection.close();		
	}

	public void initializeTests() {
		
		
		logger.info("Executing setup class" );
		
		try {
			
			// Clean output/report directory
			clnOutputDir();
			clnReportDir();
			String serviceProperties=" --bsc.service.name="+ serviceName + " --bsc.instance.name=" + serviceName + ".1 --bsc.config.name="+serviceName ;

			// Invoke KMSGatewayService_v1
			String serviceTestArgs = System.getenv("TEST_APP_ENV") + serviceProperties;
			String[] args = serviceTestArgs.split(" ");
			invokeSpringBoot("com.bsc.intg.svcs.boot."+ serviceName.replaceAll("_v1","") +"Boot", "main", args);

		} catch (Exception e) {
			logger.info("Exception occured while constructing setup class - " + e.toString());
			System.exit(1);
		}
	}
	
	public void runTest(TestName name, String connectionName, String publishQueue, String serviceStatus, String ignoreLinesString) throws Exception {
		
		Logger logger = LoggerFactory.getLogger(name + "." + name.getMethodName());
		logger.info("Executing test case - " + name.getMethodName());
		
		String tcName=name.getMethodName();
		
		ArrayList<String> ignoreLines = (ArrayList<String>)Arrays.asList(ignoreLinesString.split("\\s*,\\s*"));
		
		final String messageContent = readInputData(tcName);
		
		publishMessageToJMSQueue(messageContent,connectionName, publishQueue, tcName, serviceStatus);
		
		Thread.sleep(3500);
		
		ArrayList<String> tcFiles = getExpectedFilesList(name.getMethodName());
		
		for (int idx = 0; idx < tcFiles.size(); idx++) {
			Assert.assertTrue("Failure - Result doesn't match with expected result - " + tcFiles.get(idx),
					checkFileExstAndCompare(tcFiles.get(idx), ignoreLines));
		}
	}
	
	public String publishMessageToJMSQueue(final String messageContent,String connectionName, String destinationName, String tcName, String correlID) throws Exception {

			Map<String,String>	connectionProperties = new HashMap<String,String>();
			Map<String,String>	destinationProperties = new HashMap<String,String>();
			JMSMQProvider jmsProvider = new JMSMQProvider(logger);
			Properties globalProperties = new Properties();
			
			String serviceGlobalProperties=System.getenv("SP8_GLOBAL_PROPERTIES");
			
			String site=System.getProperty("bsc.service.sites","map");
			
			if (serviceGlobalProperties==null) 
				serviceGlobalProperties="/intServices/toolkits/SP8/etc/sp8.properties";
			
			File globalPropertiesFile = new File(serviceGlobalProperties);
		
			if (globalPropertiesFile.exists()) {
				logger.info("Loading global properties from {} " , globalPropertiesFile.getAbsolutePath() );
				
				globalProperties.load(new FileInputStream(globalPropertiesFile));
				
			} else{
				throw new Exception("Failed to load the global properties file");
			}
	
			for ( int pi=1; pi<10;pi++) {
				
				String property =  globalProperties.getProperty(site + "." + connectionName + ".jms.connection.properties."+pi, "");
				int index=property.indexOf("=");
				if (!property.equals("")) {
					if (index < 0) {
						logger.info("Skipping connection property {} as it is formatted incorrectly",property);
					} else {
						String propertyName=property.substring(0,index);
						String propertyValue=property.substring(index+1,property.length());
						logger.info("Adding property name :{} value :{}",propertyName,propertyValue);
						connectionProperties.put(propertyName.trim(), propertyValue.trim());
					}
				
				}
			}
		
			for ( int pi=1; pi<10;pi++) {
				String property =  globalProperties.getProperty(site + "." + connectionName + ".jms.destination.properties."+pi, "");
				int index=property.indexOf("=");
				if (!property.equals("")) {
					if (index < 0) {
						logger.info("Skipping destination property {} as it is formatted incorrectly",property);
					} else {
						String propertyName=property.substring(0,index);
						String propertyValue=property.substring(index+1,property.length());
						logger.info("Adding property name :{} value :{}",propertyName,propertyValue);
						destinationProperties.put(propertyName.trim(), propertyValue.trim());
					}
				}
			}
			
			QueueConnectionFactory queueConnectionFactory = (QueueConnectionFactory) jmsProvider.getConnectionFactory();
					
			jmsProvider.setConnectionProperties(queueConnectionFactory, connectionProperties);
			
			Destination destination=jmsProvider.getDestination(destinationName, "queue");
			jmsProvider.setDestinationProperties(destination, destinationProperties);	
			
			JmsTemplate jmsTemplate = new JmsTemplate();
			final AtomicReference<TextMessage> arJMSMessage = new AtomicReference<TextMessage>();
			jmsTemplate.send(destination,new MessageCreator() {
	
			public Message createMessage(Session session) throws JMSException {
		
					TextMessage jmsTextMessage;
					jmsTextMessage = session.createTextMessage(messageContent);
					jmsTextMessage.setJMSCorrelationID(correlID);
					jmsTextMessage.setStringProperty("TestCase", tcName);
					arJMSMessage.set(jmsTextMessage);
		
					return arJMSMessage.get();
				}
			});
	
		logger.info("Message is published to {} with JMS properties - CORRELATIONID - {} , TestCase - {}" , destination, correlID, tcName);
		
		return arJMSMessage.get().getJMSMessageID();
		
	}
	
	
	
}
