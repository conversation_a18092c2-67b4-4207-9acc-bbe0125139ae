service.name=AWSEventService_v1
service.bundle=../EventService_v1/EventService_v1-8.0.0.jar
service.context.uri=/AWSEventService/v1
service.port=true
service.args=--bsc.config.name=EventService_v1
runtime.args=-Xmx4096m -Dbsc.service.ext.js.common=config/EventService_v1/etc -Dcom.atomikos.icatch.no_file=true -Dcom.atomikos.icatch.service=com.atomikos.icatch.standalone.UserTransactionServiceFactory -Dcom.atomikos.icatch.output_dir=logs/AWSEventService_v1.1/ -Dcom.atomikos.icatch.log_base_dir=logs/AWSEventService_v1.1/ -Dcom.atomikos.icatch.max_timeout=600000 -Dcom.atomikos.icatch.default_jta_timeout=600000
service.platform=SP
service.target=AWS/FGW
service.groups=CORP
service.framework=sp8
service.context.uri.override=true
service.pool.size=10

EventService.resources=JMSMGW,J<PERSON>EVENT,JMSMGWXA

EventServiceContainer.listeners=MGW_BSE1_Listener,MGW_BSE2_Listener,MGW_BSE3_Listener,MGW_BSEE_Listener,MGW_BEE_Listener,MGW_S4E_Listener,MGW_MSE_Listener,MGW_PSE_Listener,EventServiceFileListener

profile.dev.ReplayDestination.http.url=https://dev-int-services.bsci.bossci.com/api/AWSReplayService/v1/event
profile.val.ReplayDestination.http.url=https://val-int-aws-services.bsci.bossci.com/api/AWSReplayService/v1/event
profile.stg.ReplayDestination.http.url=https://stg-int-aws-services.bsci.bossci.com/api/AWSReplayService/v1/event
profile.prd.ReplayDestination.http.url=https://prd-int-aws-services.bsci.bossci.com/api/AWSReplayService/v1/event

profile.prd.EventServiceFileListener.file.uri=/s4_ps4/BODS/outbound/RR/EVENTLOG/

profile.prd.FetchPayloadService.default.allowed.groups=eai_prd_admn,eai_prd_rmgt,eai_prd_usrs,ei_p_h_s4

EventAWSESDispatcherService.es.batch.size=100

S4ESGETDestionation.get.timeout=-1
MESESGETDestionation.get.timeout=-1
PublishESGETDestionation.get.timeout=-1
CommonESGETDestionation.get.timeout=-1
