package com.bsc.intg.svcs.core.boot;

import org.glassfish.jersey.server.ResourceConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class BSCRESTRuntime {
	
	private static final Logger logger = LoggerFactory.getLogger(BSCServiceConfiguration.class);
	
	String runtime = null ;
	Object runtimeObject = null;
	
	public BSCRESTRuntime(String runtime, Object jersyConfig) {
		this.runtime=runtime;
		this.runtimeObject=jersyConfig;
	}
	
	public void registerRESTEndpoint(Object restImpl) throws Exception {
	
			if (runtime.equals("jersy")) {
				
				ResourceConfig resourceConfig	 = ((ResourceConfig)runtimeObject);
				
				if(resourceConfig !=null ) {
					
					logger.info("Registering the endpoint object with REST runtime");
					
					if (!resourceConfig.isRegistered(restImpl))
						resourceConfig.register(restImpl);
				} else {
					throw new Exception("BSC REST runtime is not initialized");
				}
				
			}
	}
}
