package com.bsc.intg.svcs.core.jdbc;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Properties;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestinationBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.message.BSCMessageProperties;

public class BSCSQL2JSONDestination extends BSCDestinationBase {

	private JdbcTemplate jdbcTemplate;
	protected String destinationURL = null;
	protected String destinationDriver = null;

	public BSCSQL2JSONDestination(BSCComponentType componentType, BSCComponent parentComponent, String configName) {
		super(componentType, parentComponent, configName);
	}

	@Override
	public void initialize() throws BSCComponentInitializationException {

		super.initialize();

		logger.info("Initializing SQL2JSON Destination");

		this.destinationURL = getNonEmptyProperty("datasource.url");
		this.destinationDriver = getNonEmptyProperty("datasource.driver");

		try {
			this.jdbcTemplate = createJDBCTemplate();
		} catch (Exception e) {
			logger.error("Error occured while creating JDBC Template", e);
			throw new BSCComponentInitializationException(this, e, BSCErrorLevel.CRITICAL,
					"Failed to initialize the destination " + getInstanceName());
		}

	}

	public JdbcTemplate createJDBCTemplate() {

		logger.info("Creating JDBC Template");

		JdbcTemplate jdbcTemplate = new JdbcTemplate();

		DriverManagerDataSource dataSource = new DriverManagerDataSource(destinationURL);
		dataSource.setDriverClassName(destinationDriver);

		Properties props = new Properties();
		props.setProperty("initialSize", "3");
		props.setProperty("maxActive", "10");
		dataSource.setConnectionProperties(props);
		jdbcTemplate.setDataSource(dataSource);

		return jdbcTemplate;

	}

	@Override
	public BSCMessage onSend(BSCMessage message) throws BSCDestinationException {

		logger.info("Executing SQLJ2JSON Destination onSend method - ");

		JSONArray array = new JSONArray();
//		String requestURI = message.getProperty(BSCMessageProperties.HTTP_REQUEST_URI);
//		String uriPath = requestURI.substring(requestURI.lastIndexOf("/") + 1);
//		if (uriPath.contains("?")) {
//			uriPath = uriPath.substring(0, uriPath.indexOf("?"));
//		}

		String sql = message.getProperty("sql");

		logger.info("SQL Query to be executed - " + sql);

		switch (message.getAction()) {

		case JDBC_SELECT:

			logger.info("Executing JDBC SELECT ");

			try {

				jdbcTemplate.query(sql, new RowMapper<JSONArray>() {

					public JSONArray mapRow(ResultSet result, int rowNum) throws SQLException {

						JSONObject item = new JSONObject();
						int colCount = result.getMetaData().getColumnCount();
						for (int i = 1; i <= colCount; i++) {

							String colName = result.getMetaData().getColumnName(i);
							String colVal = result.getString(colName);
							item.put(colName, colVal == null ? JSONObject.NULL : colVal);

						}
						array.put(item);
						return array;
					}

				});

			} catch (Exception e) {
				logger.error("Unable to execute SQL query - {}", e.toString());
				throw new BSCDestinationException(this, e, BSCErrorLevel.CRITICAL,
						"Unable to execute SQL query - " + e.toString());
			}
			try {
				return (message.loadMessage(array.toString()));
			}

			catch (BSCMessageException e) {
				logger.error("Failed to load message. ");
				throw new BSCDestinationException(this, e, BSCErrorLevel.CRITICAL, "Failed to load message. ");
			}

		case JDBC_INSERT:
			logger.info("Executing JDBC INSERT ");
			
			try {
				jdbcTemplate.setQueryTimeout(100);
				jdbcTemplate.update(sql);
			} catch (Exception e) {
				logger.error("Unable to execute SQL query - {}", e.toString());
				throw new BSCDestinationException(this, e, BSCErrorLevel.CRITICAL,
						"Unable to execute SQL query - " + e.toString());
			}

			return (message);

		default:
			logger.error("Unspecified destination action ");
			throw new BSCDestinationException(this, BSCErrorLevel.CRITICAL, "Unspecified destination action ");
		}

	}

	@Override
	public List<BSCMessage> onSend(List<BSCMessage> message) throws BSCDestinationException {
		return null;
	}

}
