package com.bsc.intg.svcs.core;

import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;

public abstract class BSCControllerBase extends BSCComponentBase implements BSCController {


	public BSCControllerBase(BSCComponentType componentType ,BSCComponent parentComponent, String configName) {
		super(componentType, parentComponent, configName);
	}
	
	public BSCServiceContainer getServiceContainer() {
		return (BSCServiceContainer)this.parentComponent;
	}

	public void initialize(BSCComponentSubType componentSubType) throws BSCComponentInitializationException {
		loadParentProperties();
		super.setComponentSubType(componentSubType);
	}

}
