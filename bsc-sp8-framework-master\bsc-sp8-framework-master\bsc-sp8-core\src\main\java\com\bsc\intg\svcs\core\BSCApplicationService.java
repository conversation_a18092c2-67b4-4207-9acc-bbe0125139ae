package com.bsc.intg.svcs.core;

import java.util.Map;

import org.springframework.transaction.PlatformTransactionManager;

import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;

public interface BSCApplicationService extends BSCComponent{

	public void initialize() throws BSCComponentInitializationException;
	
	public void start() throws BSCComponentInitializationException;
	
	public void stop();
	
	public BSCApplicationServiceConfig getApplicationServiceConfig();
	
	public String getInstanceName();
	
	public String getConfigName();
	
	public Map<String,Object> getProperties();
	
	public PlatformTransactionManager getTransactionManager();

	public BSCResource getResource(boolean  primary, String name);
	
	public BSCResource getResource(String name);
	
	public BSCServiceContainer getContainer(String container);

	public BSCServiceContainer getRuntimeContainer(String container);
}
