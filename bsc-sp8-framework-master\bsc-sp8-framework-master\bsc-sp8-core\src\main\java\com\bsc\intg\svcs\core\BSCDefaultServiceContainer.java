package com.bsc.intg.svcs.core;

import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import javax.ws.rs.core.Response;

import org.apache.commons.pool2.impl.GenericObjectPoolConfig;

import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCStringUtils;

public class BSCDefaultServiceContainer extends BSCServiceContainerBase implements BSCServiceContainer {


	private boolean ready = false;
	
	private ArrayList<String> listeners=null;
	private ArrayList<String> services=null;
	private ArrayList<String> controllers=null;
	
	private ArrayList<String> runtimeServices=null;
	
	
	private Map<String,BSCListener> initializedListeners=new HashMap<String,BSCListener>();
	
	private Map<String,BSCController> initializedControllers=new HashMap<String,BSCController>();
	
	
	private Map<String,BSCServicePool> servicePools=new HashMap<String,BSCServicePool>();
	
	private Map<String,BSCService> 	singletonPools=new HashMap<String,BSCService>();
	
	private Map<String,BSCServiceType> serviceTypes =new HashMap<String,BSCServiceType>();
	
	public BSCDefaultServiceContainer(BSCComponentType componentType ,BSCComponent parentComponent,String name) {
		super(componentType,parentComponent, name ) ;	

		String serviceNames			=	this.getProperty("services");
		String listenerNames		=	this.getProperty("listeners");
		String controllerNames		=	this.getProperty("controllers");
		String runtimeServiceNames	=	this.getProperty("runtime.services");
		
		this.services				=	BSCStringUtils.convertCommaSeperated(serviceNames);
		this.listeners				=	BSCStringUtils.convertCommaSeperated(listenerNames);
		this.controllers			=	BSCStringUtils.convertCommaSeperated(controllerNames);
		this.runtimeServices		=	BSCStringUtils.convertCommaSeperated(runtimeServiceNames);
		
	}
	

	@Override
	public void initialize() throws BSCComponentInitializationException  {
		
		super.initialize();
		
		//Initialize container specific runtime services
		
		for (String serviceObjName:runtimeServices) {
			
			String serviceClassName=getGlobalProperty(serviceObjName +".class");
			int poolSize = Integer.parseInt(getGlobalProperty(serviceObjName+".pool.size", "2"));
			
			logger.info("Initializing the service {}",serviceObjName);
			
			if (poolSize == 0 ) {
				serviceTypes.put(serviceObjName, BSCServiceType.NEW_INSTANCE);
			}else if ( poolSize == -1) {
				serviceTypes.put(serviceObjName, BSCServiceType.SINGLETON);
			} else {
				serviceTypes.put(serviceObjName, BSCServiceType.POOLED_INSTANCE);
			}
			
			if ( serviceTypes.get(serviceObjName)==BSCServiceType.POOLED_INSTANCE ) {
				
				GenericObjectPoolConfig config = new GenericObjectPoolConfig();
		        config.setMaxIdle(poolSize);
		        config.setMaxTotal(poolSize);
		        config.setTestOnBorrow(true);
		        config.setTestOnReturn(true);
		        config.setBlockWhenExhausted(true);
		        
				BSCServicePool pool = new BSCServicePool(new BSCServiceFactory(this,serviceObjName,serviceClassName), config, this, serviceObjName );
		         
		        servicePools.put(serviceObjName, pool);
		        
			}  else if ( serviceTypes.get(serviceObjName)==BSCServiceType.SINGLETON ) {
				
				try {
					Class<?> sc = Class.forName(serviceClassName);
					Constructor<?>	ctor=sc.getConstructor(BSCComponentType.class,BSCComponent.class,String.class,String.class );
					BSCService service = (BSCService) ctor.newInstance(BSCComponentType.SERVICE, this,serviceObjName,"S");
					service.initialize();
					singletonPools.put(serviceObjName, service);
				} catch (Exception | BSCComponentInitializationException  e) {
					throw new BSCComponentInitializationException(this, e, BSCErrorLevel.CRITICAL,"Service creation failed -" + serviceObjName);
				}
			}
		}


		//Services list
		for (String serviceObjName:services) {
			
			logger.info("Creating the service pool {} ", serviceObjName);
			
			String serviceClassName=getGlobalProperty(serviceObjName +".class");
			int poolSize = Integer.parseInt(getGlobalProperty(serviceObjName+".pool.size", "2"));
			
			
			if (poolSize == 0 ) {
				serviceTypes.put(serviceObjName, BSCServiceType.NEW_INSTANCE);
			}else if ( poolSize == -1) {
				serviceTypes.put(serviceObjName, BSCServiceType.SINGLETON);
			} else {
				serviceTypes.put(serviceObjName, BSCServiceType.POOLED_INSTANCE);
			}
			
			if ( serviceTypes.get(serviceObjName)==BSCServiceType.POOLED_INSTANCE ) {
				
				logger.info("Creating the service pool {}",serviceObjName);
				
				GenericObjectPoolConfig config = new GenericObjectPoolConfig();
		        config.setMaxIdle(poolSize);
		        config.setMaxTotal(poolSize);
		        config.setTestOnBorrow(true);
		        config.setTestOnReturn(true);
		        config.setBlockWhenExhausted(true);
		        
				BSCServicePool pool = new BSCServicePool(new BSCServiceFactory(this,serviceObjName,serviceClassName), config, this, serviceObjName );
		         
		        servicePools.put(serviceObjName, pool);
		        
			}  else if ( serviceTypes.get(serviceObjName)==BSCServiceType.SINGLETON ) {
				
				try {
					logger.info("Creating the singleton service  {}",serviceObjName);
					Class<?> sc = Class.forName(serviceClassName);
					Constructor<?>	ctor=sc.getConstructor(BSCComponentType.class,BSCComponent.class,String.class,String.class );
					BSCService service = (BSCService) ctor.newInstance(BSCComponentType.SERVICE, this,serviceObjName,"S");
					logger.info("Initializing the singleton service {}",serviceObjName);
					service.initialize();
					singletonPools.put(serviceObjName, service);
				} catch (Exception | BSCComponentInitializationException  e) {
					throw new BSCComponentInitializationException(this, e, BSCErrorLevel.CRITICAL,"Service creation failed -" + serviceObjName);
				}
			}
		}
		

		
		boolean listenersDefined=false;
		
		for (String listenerObjName:listeners) {	
			
			logger.info("Creating the listener class {} ", listenerObjName);
			
			listenersDefined 			= 	true;
			
			BSCListener listener 		= 	null;
		
			String listenerClassName	=	getGlobalProperty(listenerObjName+".class");
			
			try {
				Class<?> sc = Class.forName(listenerClassName);
				Constructor<?>	ctor=sc.getConstructor(BSCComponentType.class,BSCComponent.class, String.class );
				listener = (BSCListener) ctor.newInstance(BSCComponentType.LISTENER, this,listenerObjName);
			} catch (Exception e) {
				throw new BSCComponentInitializationException(this, e, BSCErrorLevel.CRITICAL, "Listener creation failed -" + listenerObjName);
			}	
		
			listener.initialize();
				
			initializedListeners.put(listenerObjName, listener);
		}
	
		if( listenersDefined && initializedListeners.size() == 0 ) {
			throw new BSCComponentInitializationException(this, BSCErrorLevel.CRITICAL,"No listeners were initialized");
		}
		

		for (String controllerObjName : controllers) {
			
			logger.info("Creating the controller class {} ", controllerObjName);

			BSCController controller= null;
			String controllerClassName	=	getGlobalProperty(controllerObjName + ".class");
			
			try {
				Class<?> sc = Class.forName(controllerClassName);
				Constructor<?>	ctor=sc.getConstructor(BSCComponentType.class,BSCComponent.class,String.class );
				controller = (BSCController) ctor.newInstance(BSCComponentType.CONTROLLER, this,controllerObjName);
				controller.initialize();
				initializedControllers.put(controllerObjName, controller);
			} catch (Exception | BSCComponentInitializationException e) {
				logger.info("Failed to create Creating the controller class {} ", controllerObjName);
				throw new BSCComponentInitializationException(this, e, BSCErrorLevel.CRITICAL, "Controller creation failed -" + controllerObjName);
			}
			
		}
		
	}
		


	public void initializeService(String serviceName) throws BSCComponentInitializationException {	
		
		BSCService service = null;
		try {
			service = getService(serviceName);
		} catch (BSCServiceException e) {
			throw new BSCComponentInitializationException(this, e, BSCErrorLevel.FATAL, "Failed to initialize the service instance "+ serviceName);
		} finally {
			
			if(service != null) {
				try {
					 this.releaseService(serviceName, service);
				} catch (BSCServiceException e) {
					throw new BSCComponentInitializationException(this, BSCErrorLevel.FATAL, "Failed to initialized the service instance "+ serviceName);
				}
			}
		}
	}
	
	@Override
	public void start() throws BSCComponentInitializationException {
		
		this.ready=true;
		
		logger.info("Starting the runtime services in the container {} " , getConfigName());
		
		for (String serviceObjName:runtimeServices) {
			if ( serviceTypes.get(serviceObjName)==BSCServiceType.POOLED_INSTANCE ) {
		        logger.info("Starting the runtime service from the pool {}",serviceObjName);
		        initializeService(serviceObjName);
			}
		}
		
		logger.info("Starting the services in the container {} " , getConfigName());
		
		for (String serviceObjName:services) {
			if ( serviceTypes.get(serviceObjName)==BSCServiceType.POOLED_INSTANCE ) {
		        logger.info("Starting the service from the pool {}",serviceObjName);
		        initializeService(serviceObjName);
			}
		}
		
		logger.info("Starting the listeners in the container {} " , getConfigName());
		for (Entry<String, BSCListener> entry : initializedListeners.entrySet()) 	{

			String listenerName = entry.getKey();
			BSCListener listener=entry.getValue();
			
			logger.info("Listener {} is being started", listenerName);
			listener.start();
			logger.info("{} is started", listenerName);
			
		}
		
		logger.info("Starting the controllers in the container {} " , getConfigName());
		for (Entry<String, BSCController> entry : initializedControllers.entrySet()) 	{

			String controllerName = entry.getKey();
			BSCController controller=entry.getValue();
			
			logger.info("Controller {} is being started", controllerName);
			controller.start();
			logger.info("{} is started", controllerName);
		}
		
	}

	@Override
	public void stop() {
		
		logger.info("Stopping the listeners in the container {} " , getConfigName());
		
		for (Entry<String, BSCListener> entry : initializedListeners.entrySet()) 	{
			String listenerName = entry.getKey();
			BSCListener listener=entry.getValue();
			logger.info("Listener {} is being stopped", listenerName);
			listener.stop();
			logger.info("{} is stopped", listenerName);
		}
		
		logger.info("Stopping the controllers in the container {} " , getConfigName());
		for (Entry<String, BSCController> entry : initializedControllers.entrySet()) 	{

			String controllerName = entry.getKey();
			BSCController controller=entry.getValue();
			
			logger.info("Controller {} is being stopped", controllerName);
			controller.stop();
			logger.info("{} is stopped", controllerName);
			
		}
		
	}
	
	
	public BSCService getService(String serviceObjName) throws BSCServiceException {
		
		if(ready) {

			logger.info("{} is being requested " , serviceObjName);
			BSCService service = null;
			
			if ( serviceTypes.get(serviceObjName)== BSCServiceType.POOLED_INSTANCE ) {
				try {
					
					service = servicePools.get(serviceObjName).borrowObject();
					logger.info("Pooled service {} aquired ( {} )" , service.getInstanceName(), service);
					return service;
				} catch (Exception e) {
					logger.error("Error occurred while aquiring pooled service {}  " , serviceObjName,e);
					throw new BSCServiceException(this, e, BSCErrorLevel.CRITICAL);
				}
				
			}  else if ( serviceTypes.get(serviceObjName)==BSCServiceType.SINGLETON ) {
				
				service = singletonPools.get(serviceObjName);
				
				logger.info("Singleton service {}  aquired ( {} )" , service.getInstanceName(),service);
				
				return service;
				
			} else {
				
				try {
					
					logger.info("Creating new service {} " , serviceObjName);
					
					String serviceClassName=getNonEmptyProperty(serviceObjName +".class");
					
					Class<?> sc = Class.forName(serviceClassName);
					Constructor<?>	ctor=sc.getConstructor(BSCComponentType.class,BSCComponent.class,String.class, String.class );
					service = (BSCService) ctor.newInstance(BSCComponentType.SERVICE, this,serviceObjName,"N");
					
					logger.info("new service {} aquired  ( {} )" , service.getInstanceName(), service );
					
					service.initialize();
					
					return service;
					
				} catch (Exception | BSCComponentInitializationException e) {
					logger.error("Error occured while returning new service {}  " , serviceObjName, e);
					throw new BSCServiceException(this, e, BSCErrorLevel.CRITICAL);
				}
			}
				
		} else {
			logger.info("Service pool is not ready for services aquisition  {}  " , this.getInstanceName());
			throw new BSCServiceException(this, BSCErrorLevel.CRITICAL,"service pool is not ready for services aquisition");
		}

	}
	
	public void releaseService(String serviceObjName, BSCService service) throws BSCServiceException {
		try {
			if ( serviceTypes.get(serviceObjName)==BSCServiceType.POOLED_INSTANCE ) {
				logger.info("{} is being released ( {} )" , service.getInstanceName(), service );
				servicePools.get(serviceObjName).returnObject(service);
			}	
		} catch(Exception e) {
			throw new BSCServiceException(this,e,BSCErrorLevel.CRITICAL, "Failed to release the service : " + serviceObjName );
			
		}
	}

	@Override
	public BSCApplicationService getApplicationService() {
		return (BSCApplicationService)parentComponent;
	}

	public List<String> getListenerNames() {
		return new ArrayList<String>(initializedListeners.keySet());
	}
	
	public BSCListener getListener(String name) {
		return initializedListeners.get(name);
	}
	

	public List<String> getControllerNames() {
		return new ArrayList<String>(initializedControllers.keySet());
	}
	
	public BSCController getController(String name) {
		return initializedControllers.get(name);
	}
	
	public List<String> getServiceNames() {
		return new ArrayList<String>(servicePools.keySet());
	}
	
	


}
