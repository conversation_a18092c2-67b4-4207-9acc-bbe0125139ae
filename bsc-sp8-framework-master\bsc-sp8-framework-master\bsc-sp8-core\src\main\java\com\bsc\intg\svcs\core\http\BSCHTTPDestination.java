package com.bsc.intg.svcs.core.http;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.net.Socket;
import java.security.KeyStore;
import java.security.Principal;
import java.security.PrivateKey;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.List;
import java.util.Map;

import javax.net.ssl.KeyManager;
import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509KeyManager;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPatch;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.InputStreamEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestinationBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCCryptoHelper;
import com.bsc.intg.svcs.core.util.BSCPropertyHelper;

public class BSCHTTPDestination extends BSCDestinationBase {

	protected String url = null;
	protected String method = null;
	protected int timeout = 0;
	protected int retry = 0;
	protected String sslProtocol = null;
	protected String sslClientAuth = null;
	protected String sslClientAlias = null;
	protected String sslKeystore = null;
	protected String sslKeystoreType = null;
	protected String sslKeystorePwd = null;
	protected String sslTruststore = null;
	protected String sslTruststoreType = null;
	protected String sslTruststorePwd = null;
	protected String proxyURL = null;
	protected String proxyUser = null;
	protected String proxyPwd = null;
	protected Map<String, Object> headers = null;
	protected String responseEncoding = null;

	protected SSLConnectionSocketFactory sslSocketFactory = null;

	public BSCHTTPDestination(BSCComponentType componentType, BSCComponent parentComponent, String configName) {
		super(componentType, parentComponent, configName);
	}

	@Override
	public void initialize() throws BSCComponentInitializationException {

		super.initialize();

		logger.info("Initializing HTTP Destination ");

		url = getProperty("http.url", "");
		method = getProperty("http.method", "");
		timeout = getIntProperty("http.timeout", 120);
		retry = getIntProperty("http.connection.retries", 0);
		sslProtocol = getProperty("ssl.protocol", "TLSv1.2");
		sslClientAuth = getProperty("ssl.client.auth", "");
		sslClientAlias = getProperty("ssl.client.alias", "client");
		sslKeystore = getProperty("ssl.keystore", getGlobalProperty("javax.net.ssl.keyStore"));
		sslKeystoreType = getProperty("ssl.keystore.type", "JKS");
		sslKeystorePwd = getProperty("ssl.keystore.password", getGlobalProperty("javax.net.ssl.keyStorePassword"));
		sslTruststore = getProperty("ssl.truststore", getGlobalProperty("javax.net.ssl.trustStore"));
		sslTruststoreType = getProperty("ssl.truststore.type", "JKS");
		sslTruststorePwd = getProperty("ssl.truststore.password",
				getGlobalProperty("javax.net.ssl.trustStorePassword"));
		proxyURL = getProperty("proxy.url", "");
		proxyUser = getProperty("proxy.user", "");
		proxyPwd = getProperty("proxy.password", "");
		headers = BSCPropertyHelper.getPropertiesStartingWith(this.getProperties(), "http.header.", true);
		try {
			if (sslKeystorePwd.contains("ss://"))
				sslKeystorePwd = BSCCryptoHelper.getSecret(sslKeystorePwd);
			if (sslTruststorePwd.contains("ss://"))
				sslTruststorePwd = BSCCryptoHelper.getSecret(sslTruststorePwd);
			if (proxyPwd.contains("ss://"))
				proxyPwd = BSCCryptoHelper.getSecret(proxyPwd);

			class FilteredKeyManager implements X509KeyManager {

				private final X509KeyManager originatingKeyManager;
				private final X509Certificate sslCertificate;
				private final String SSLCertificateKeyStoreAlias;

				public FilteredKeyManager(X509KeyManager originatingKeyManager, X509Certificate sslCertificate,
						String SSLCertificateKeyStoreAlias) {
					this.originatingKeyManager = originatingKeyManager;
					this.sslCertificate = sslCertificate;
					this.SSLCertificateKeyStoreAlias = SSLCertificateKeyStoreAlias;
				}

				@Override
				public String chooseClientAlias(String[] keyType, Principal[] issuers, Socket socket) {
					return SSLCertificateKeyStoreAlias;
				}

				@Override
				public String chooseServerAlias(String keyType, Principal[] issuers, Socket socket) {
					return originatingKeyManager.chooseServerAlias(keyType, issuers, socket);
				}

				@Override
				public X509Certificate[] getCertificateChain(String alias) {
					return new X509Certificate[] { sslCertificate };
				}

				@Override
				public String[] getClientAliases(String keyType, Principal[] issuers) {
					return originatingKeyManager.getClientAliases(keyType, issuers);
				}

				@Override
				public String[] getServerAliases(String keyType, Principal[] issuers) {
					return originatingKeyManager.getServerAliases(keyType, issuers);
				}

				@Override
				public PrivateKey getPrivateKey(String alias) {
					return originatingKeyManager.getPrivateKey(alias);
				}
			}

			if (getProperty("ssl.enable", "true").equals("true")) {

				KeyStore keyStore = KeyStore.getInstance(sslKeystoreType);
				java.io.FileInputStream keyStoreInputStream = new java.io.FileInputStream(sslKeystore);
				keyStore.load(keyStoreInputStream, sslKeystorePwd.toCharArray());

				KeyStore trustStore = KeyStore.getInstance(sslTruststoreType);
				java.io.FileInputStream trustStoreInputStream = new java.io.FileInputStream(sslTruststore);
				trustStore.load(trustStoreInputStream, sslTruststorePwd.toCharArray());

				keyStoreInputStream.close();
				trustStoreInputStream.close();

				KeyManagerFactory keyManagerFactory = KeyManagerFactory
						.getInstance(KeyManagerFactory.getDefaultAlgorithm());
				TrustManagerFactory trustManagerFactory = TrustManagerFactory
						.getInstance(TrustManagerFactory.getDefaultAlgorithm());

				keyManagerFactory.init(keyStore, sslKeystorePwd.toCharArray());
				trustManagerFactory.init(trustStore);

				String sslType = sslProtocol;

				SSLContext sslContext = SSLContext.getInstance(sslType);

				sslContext.init(new KeyManager[] {
						new FilteredKeyManager((X509KeyManager) keyManagerFactory.getKeyManagers()[0],
								(X509Certificate) keyStore.getCertificateChain(sslClientAlias)[0], sslClientAlias) },
						trustManagerFactory.getTrustManagers(), new SecureRandom());

				sslSocketFactory = new SSLConnectionSocketFactory(sslContext, new String[] { sslType }, null,
						SSLConnectionSocketFactory.getDefaultHostnameVerifier());

			}

		} catch (Exception e) {
			throw new BSCComponentInitializationException(this, BSCErrorLevel.CRITICAL, "Failed to initialize SSL");
		}
	}

	@Override
	public BSCMessage onSend(BSCMessage message) throws BSCDestinationException {

		logger.info("Executing HTTP Destination onSend method - ");

		BSCMessage outMsg = null;

		// Override URL from Message
		url = message.getProperty("REQUEST.HTTP.URL", url);
		method = message.getProperty("REQUEST.HTTP.METHOD", method);

		RequestConfig config = RequestConfig.custom().setConnectTimeout(timeout * 1000)
				.setConnectionRequestTimeout(timeout * 1000).setSocketTimeout(timeout * 1000).build();

		try (CloseableHttpClient httpclient = HttpClientBuilder.create().setDefaultRequestConfig(config)
				.setSSLSocketFactory(sslSocketFactory).build()) {


			// Read all HTTP header from Message
			Map<String, Object> customHeaders = BSCPropertyHelper.getPropertiesStartingWith(message.getProperties(),
					"REQUEST.HTTP.HEADER.", true);

			if (logger.isDebugEnabled()) {
				logger.debug("HTTP Destination headers");
				headers.forEach((k, v) -> {
					logger.debug("{}={}", k, v);
				});
				logger.debug("HTTP Message headers");
				customHeaders.forEach((k, v) -> {
					logger.debug("{}={}", k, v);
				});
			}

			// Add dynamic headers from message and overwrite if one already
			// exist at destination
			headers.putAll(customHeaders);
			ByteArrayInputStream bis = new ByteArrayInputStream(message.toByteArray());
			HttpEntity ent = new InputStreamEntity(bis, message.toByteArray().length);
			outMsg = message.cloneMessage(false);

			method = method.toLowerCase();
			
			switch (method) {

			case "post":
				logger.debug("Executing HTTP Destination POST method");

				HttpPost req = new HttpPost(url);
				req.setEntity(ent);

				if (headers != null) {
					logger.debug("HTTP effective request headers are");
					headers.forEach((k, v) -> {
						req.setHeader(k, (String) v);
						logger.debug("{}={}", k, v);
					});
				}

				try (CloseableHttpResponse response = httpclient.execute(req)) {
					HttpEntity entity = response.getEntity();
					String content=StringUtils.EMPTY;
					if(entity != null) {
						if(message.getProperty("response.encoding")!=null)
							content = EntityUtils.toString(entity,message.getProperty("response.encoding"));
						else
							content = EntityUtils.toString(entity);
					} else
						content = response.getStatusLine().toString();
					logger.debug("Response {}", content);
					outMsg.loadMessage(content);
					Header[] headers = response.getAllHeaders();
					setResponseHeader(outMsg, headers);
					logger.debug("Response status message {}", response.getStatusLine().toString());
					outMsg.setProperty("RESPONSE.HTTP.HEADER.STATUSCODE",
							String.valueOf(response.getStatusLine().getStatusCode()));
					outMsg.setProperty("RESPONSE.HTTP.HEADER.STATUS", response.getStatusLine().toString());
				}

				break;

			case "put":
				logger.debug("Executing HTTP Destination PUT method");
				HttpPut putreq = new HttpPut(url);
				putreq.setEntity(ent);

				if (headers != null) {
					logger.debug("HTTP effective request headers are");
					headers.forEach((k, v) -> {
						putreq.setHeader(k, (String) v);
						logger.debug("{}={}", k, v);
					});
				}

				try (CloseableHttpResponse response = httpclient.execute(putreq)) {
					HttpEntity entity = response.getEntity();
					String content=StringUtils.EMPTY;
					if(entity != null) {
						if(message.getProperty("response.encoding")!=null)
							content = EntityUtils.toString(entity,message.getProperty("response.encoding"));
						else
							content = EntityUtils.toString(entity);
					} else
						content = response.getStatusLine().toString();
					logger.info("Response {}", content);
					outMsg.loadMessage(content);
					Header[] headers = response.getAllHeaders();
					setResponseHeader(outMsg, headers);

					logger.debug("Response status message {}", response.getStatusLine().toString());
					outMsg.setProperty("RESPONSE.HTTP.HEADER.STATUSCODE",
							String.valueOf(response.getStatusLine().getStatusCode()));
					outMsg.setProperty("RESPONSE.HTTP.HEADER.STATUS", response.getStatusLine().toString());
				}

				break;

			case "patch":
				logger.debug("Executing HTTP Destination PATCH method");

				HttpPatch patchreq = new HttpPatch(url);
				patchreq.setEntity(ent);

				if (headers != null) {
					logger.debug("HTTP effective request headers are");
					headers.forEach((k, v) -> {
						patchreq.setHeader(k, (String) v);
						logger.debug("{}={}", k, v);
					});
				}

				try (CloseableHttpResponse response = httpclient.execute(patchreq)) {
					HttpEntity entity = response.getEntity() != null ? response.getEntity() : ent;
					String content=null;
					if(entity != null) {
						if(message.getProperty("response.encoding")!=null)
							content = EntityUtils.toString(entity,message.getProperty("response.encoding"));
						else
							content = EntityUtils.toString(entity);
					} else
						content = response.getStatusLine().toString();
					logger.info("Response {}", content);
					outMsg.loadMessage(content);
					Header[] headers = response.getAllHeaders();
					setResponseHeader(outMsg, headers);

					logger.debug("Response status message {}", response.getStatusLine().toString());
					outMsg.setProperty("RESPONSE.HTTP.HEADER.STATUS", response.getStatusLine().toString());
					outMsg.setProperty("RESPONSE.HTTP.HEADER.STATUSCODE",
							String.valueOf(response.getStatusLine().getStatusCode()));
				}

				break;

			case "get":

				logger.debug("Executing HTTP Destination GET method");
				HttpGet greq = new HttpGet(url);

				if (headers != null) {
					logger.debug("HTTP request headers are");
					headers.forEach((k, v) -> {
						greq.setHeader(k, (String) v);
						logger.debug("{}={}", k, v);
					});
				}

				try (CloseableHttpResponse response = httpclient.execute(greq)) {
					HttpEntity entity = response.getEntity();
					outMsg.loadMessage(EntityUtils.toByteArray(entity));
					Header[] headers = response.getAllHeaders();
					setResponseHeader(outMsg, headers);
					logger.debug("Response status message {}", response.getStatusLine().toString());

					outMsg.setProperty("RESPONSE.HTTP.HEADER.STATUS", response.getStatusLine().toString());
					outMsg.setProperty("RESPONSE.HTTP.HEADER.STATUSCODE",
							String.valueOf(response.getStatusLine().getStatusCode()));
				}

				break;
				
			case "delete":
				logger.debug("Executing HTTP Destination DELETE method");
				HttpDelete delreq = new HttpDelete(url);

				if (headers != null) {
					logger.debug("HTTP effective request headers are");
					headers.forEach((k, v) -> {
						delreq.setHeader(k, (String) v);
						logger.debug("{}={}", k, v);
					});
				}

				try (CloseableHttpResponse response = httpclient.execute(delreq)) {
					HttpEntity entity = response.getEntity();
					String content=StringUtils.EMPTY;
					if(entity != null) {
						if(message.getProperty("response.encoding")!=null)
							content = EntityUtils.toString(entity,message.getProperty("response.encoding"));
						else
							content = EntityUtils.toString(entity);
					} else
						content = response.getStatusLine().toString();
					logger.info("Response {}", content);
					outMsg.loadMessage(content);
					Header[] headers = response.getAllHeaders();
					setResponseHeader(outMsg, headers);

					logger.debug("Response status message {}", response.getStatusLine().toString());
					outMsg.setProperty("RESPONSE.HTTP.HEADER.STATUSCODE",
							String.valueOf(response.getStatusLine().getStatusCode()));
					outMsg.setProperty("RESPONSE.HTTP.HEADER.STATUS", response.getStatusLine().toString());
				}

				break;
			}

		} catch (IOException | BSCMessageException e) {
			logger.error("Failed to process the request " + e.toString());
			throw new BSCDestinationException(this, e, BSCErrorLevel.CRITICAL,
					"Failed to process the request " + e.toString());
		}

		return outMsg;

	}

	private void setResponseHeader(BSCMessage outMsg, Header[] headers) {
		logger.debug("HTTP response headers are");
		String cookie = "";
		for (int i = 0; i < headers.length; i++) {
			logger.debug("{}={}", headers[i].getName().toUpperCase(), headers[i].getValue());
			if(headers[i].getName().equalsIgnoreCase("set-cookie")){
				cookie+=headers[i].getValue()+";";
				outMsg.setProperty("RESPONSE.HTTP.HEADER." + headers[i].getName().toUpperCase(),
						cookie);
			}
			else{
			outMsg.setProperty("RESPONSE.HTTP.HEADER." + headers[i].getName().toUpperCase(),
					headers[i].getValue());
			}
		}
	}
	
	

	@Override
	public List<BSCMessage> onSend(List<BSCMessage> message) throws BSCDestinationException {
		// TODO Auto-generated method stub
		return null;
	}
	
}