package com.bsc.intg.svcs.core;

import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;

public abstract class BSCResourceBase extends BSCComponentBase implements BSCResource {
	

	public BSCResourceBase(BSCComponentType componentType, BSCComponent parent, String configName) {
		super(componentType, parent, configName);
	}
	

	@Override
	public void initialize(String prefix ) throws BSCComponentInitializationException {

	}
	
	@Override
	public void initialize() throws BSCComponentInitializationException {

	}


}
