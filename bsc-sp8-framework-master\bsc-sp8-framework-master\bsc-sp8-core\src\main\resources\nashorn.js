function SP(key,value){
	message.setProperty(key,value);
}

function uuid() {
	return java.util.UUID.randomUUID().toString();
}

function hexToString(h) {
    var s = ''
    for (var i = 0; i < h.length; i+=2) {
        s += String.fromCharCode(parseInt(h.substr(i, 2), 16))
    }
    return decodeURIComponent(escape(s))
}

function GMTTimestamp(){
	return java.time.Instant.now().toString();
}


function GP(key){
	return message.getProperty(key);
}
//Generates the event
function generateEvent(eventName){
	message.generateEvent(eventName);
}


