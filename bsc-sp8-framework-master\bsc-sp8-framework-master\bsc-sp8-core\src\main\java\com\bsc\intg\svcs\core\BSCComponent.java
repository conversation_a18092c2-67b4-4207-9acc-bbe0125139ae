package com.bsc.intg.svcs.core;

import java.util.Map;

import org.slf4j.Logger;
import org.springframework.context.ConfigurableApplicationContext;

import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;

public interface BSCComponent {

	public void start() throws BSCComponentInitializationException;
	
	public void stop();
	
	public void initialize() throws BSCComponentInitializationException;
	
	public String getInstanceName();
	
	public String getConfigName();
	
	public BSCComponentType getComponentType();
	
	public String getConfigPath();
	
	public Map<String,Object> getProperties();
	
	public Logger getLogger();
	
	public ConfigurableApplicationContext getConfigAppContext();
	
	public String getProperty(String key);

	public String getProperty(String key,String defaultValue);
	
	public int getIntProperty(String key) throws BSCComponentInitializationException;
	
	public int getIntProperty(String key,int defaultValue) throws BSCComponentInitializationException;
	
	public String getNonEmptyProperty(String key) throws BSCComponentInitializationException;
	
	public boolean getBooleanProperty(String key) throws BSCComponentInitializationException;
	
	public boolean getBooleanProperty(String key, boolean defaultValue) throws BSCComponentInitializationException;
	
	public String getNonEmptyGlobalProperty(String key) throws BSCComponentInitializationException;
	
	public int getIntGlobalProperty(String key) throws BSCComponentInitializationException ;
	
	public int getIntGlobalProperty(String key,int defaultValue) throws BSCComponentInitializationException;
	
	public boolean getBooleanGlobalProperty(String key) throws BSCComponentInitializationException ;
	
	public boolean getBooleanGlobalProperty(String key,boolean defaultValue) throws BSCComponentInitializationException;
	
	public BSCComponentSubType getComponentSubType() ;

	public void setComponentSubType(BSCComponentSubType componentSubType) ;
	
	public void setProperty(String key,String defaultValue) ;
}
