package com.bsc.intg.svcs.core.vfs.aws.s3;

import java.util.Objects;


class PlatformFeatures {
	private final boolean defaultAllowForOwner;
	private final boolean allowDenyForOwner;
	private final boolean supportsServerSideEncryption;
	private final boolean supportsAuthorizedGroup;
	private final boolean supportsAcl;

	public PlatformFeatures(boolean defaultAllowForOwner, boolean allowDenyForOwner,
			boolean supportsServerSideEncryption, boolean supportsAuthorizedGroup, boolean supportsAcl) {
		this.defaultAllowForOwner = defaultAllowForOwner;
		this.allowDenyForOwner = allowDenyForOwner;
		this.supportsServerSideEncryption = supportsServerSideEncryption;
		this.supportsAuthorizedGroup = supportsAuthorizedGroup;
		this.supportsAcl = supportsAcl;
	}

	public boolean defaultAllowForOwner() {
		return defaultAllowForOwner;
	}

	public boolean allowDenyForOwner() {
		return allowDenyForOwner;
	}

	public boolean supportsServerSideEncryption() {
		return supportsServerSideEncryption;
	}

	public boolean supportsAuthorizedGroup() {
		return supportsAuthorizedGroup;
	}

	public boolean supportsAcl() {
		return supportsAcl;
	}

	public final void process() {
		// Nothing to do
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (o == null || getClass() != o.getClass())
			return false;
		PlatformFeatures that = (PlatformFeatures) o;
		return defaultAllowForOwner == that.defaultAllowForOwner && allowDenyForOwner == that.allowDenyForOwner
				&& supportsServerSideEncryption == that.supportsServerSideEncryption;
	}

	@Override
	public int hashCode() {
		return Objects.hash(defaultAllowForOwner, allowDenyForOwner, supportsServerSideEncryption);
	}

	@Override
	public String toString() {
		return "PlatformFeaturesImpl{" + "defaultAllowForOwner=" + defaultAllowForOwner + ", allowDenyForOwner="
				+ allowDenyForOwner + ", supportsServerSideEncryption=" + supportsServerSideEncryption
				+ ", supportsAuthorizedGroup=" + supportsAuthorizedGroup + '}';
	}
}
