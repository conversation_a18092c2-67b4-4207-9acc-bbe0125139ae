<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<maven.compiler.source>1.8</maven.compiler.source>
		<maven.compiler.target>1.8</maven.compiler.target>
	</properties>

	<groupId>com.bsc.intg.svcs</groupId>
	<artifactId>bsc-sp8-framework</artifactId>
	<version>8.0.0</version>
	<packaging>pom</packaging>

	<name>bsc-sp8-framework</name>
	<description>Parent project POM to manage the dependencies and common configuration for all the services</description>

	<modules>
		<module>bsc-sp8-core</module>
		<module>bsc-sp8-parent</module>
	</modules>

	<organization>
		<name>Boston Scientific Corporation.</name>
		<url>http://www.bostonscientific.com</url>
	</organization>



	<developers>
		<developer>
			<id>sircilv</id>
			<name>Vinay Sircilla</name>
			<email><EMAIL></email>
			<organization>Boston Scientific Corporation.</organization>
			<organizationUrl>http://www.bostonscientific.com</organizationUrl>
			<roles>
				<role>Project lead</role>
			</roles>
		</developer>
	</developers>

	<scm>
		<url>https://gitlab.bsci.bossci.com/ei/app/services/java/bsc-sp8-framework.git</url>
	</scm>



	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>1.5.4.RELEASE</version>
	</parent>

	<dependencies>

		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-spring-boot-starter-jaxws</artifactId>
			<version>3.1.12</version>
		</dependency>

		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
			<version>20170516</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-jms</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-jersey</artifactId>
		</dependency>

		<dependency>
		    <groupId>org.springframework.boot</groupId>
		    <artifactId>spring-boot-starter-security</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-pool2</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-jta-atomikos</artifactId>
		</dependency>


		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-jdbc</artifactId>
		</dependency>

		<dependency>
			<groupId>com.oracle</groupId>
			<artifactId>ojdbc</artifactId>
			<version>12.1.0.1</version>
		</dependency>

		<dependency>
			<groupId>com.ibm.mq.jms</groupId>
			<artifactId>com.ibm.mq.allclient</artifactId>
			<version>9.0.0.3</version>
		</dependency>
		
		<dependency>
			<groupId>com.ibm.mq.jms</groupId>
			<artifactId>providerutil</artifactId>
			<version>9.0.0.3</version>
		</dependency>

		<dependency>
			<groupId>com.ibm.mq.jms</groupId>
			<artifactId>fscontext</artifactId>
			<version>9.0.0.3</version>
		</dependency>
		
		<dependency>
			<groupId>javax.jms</groupId>
			<artifactId>jms</artifactId>
			<version>2.0</version>
		</dependency>
		
		
		<dependency>
			<groupId>javax.mail</groupId>
			<artifactId>mail</artifactId>
			<version>1.4</version>
		</dependency>


		<dependency>
			<groupId>org.codehaus.janino</groupId>
			<artifactId>janino</artifactId>
		</dependency>

		<dependency>
			<groupId>commons-beanutils</groupId>
			<artifactId>commons-beanutils</artifactId>
			<version>1.9.3</version>
		</dependency>


		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-io</artifactId>
			<version>1.3.2</version>
		</dependency>

	
		<dependency>
			<groupId>com.h2database</groupId>
			<artifactId>h2</artifactId>
		</dependency>



		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-mail</artifactId>
		</dependency>



		<dependency>
		    <groupId>com.fasterxml.jackson.core</groupId>
		    <artifactId>jackson-core</artifactId>
		    <version>2.13.2</version>
		</dependency>
		<dependency>
		    <groupId>com.fasterxml.jackson.core</groupId>
		    <artifactId>jackson-databind</artifactId>
		    <version>2.13.2.2</version>
		</dependency>
		<dependency>
		    <groupId>com.fasterxml.jackson.core</groupId>
		    <artifactId>jackson-annotations</artifactId>
		    <version>2.13.2</version>
		</dependency>
		<dependency>
		    <groupId>com.fasterxml.jackson.dataformat</groupId>
		    <artifactId>jackson-dataformat-xml</artifactId>
		    <version>2.13.2</version>
		    
		    <exclusions>
            <exclusion>
                <groupId>org.codehaus.woodstox</groupId>
                <artifactId>stax2-api</artifactId><!-- 4.1 conflicts with 3.1.4 from apache cxf-spring-boot-starter-jaxws-->
            </exclusion>
   			</exclusions>
		</dependency>

		
		
		<dependency>
			<groupId>org.apache.olingo</groupId>
			<artifactId>odata-server-core</artifactId>
			<version>4.4.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.olingo</groupId>
			<artifactId>odata-commons-api</artifactId>
			<version>4.4.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.olingo</groupId>
			<artifactId>odata-commons-core</artifactId>
			<version>4.4.0</version>
		</dependency> 
		
		<dependency>
			<groupId>org.quartz-scheduler</groupId>
			<artifactId>quartz-jobs</artifactId>
			<version>2.2.1</version>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-vfs2</artifactId>
			<version>2.7.0</version>
		</dependency>

	
		<dependency>
			<groupId>com.jcraft</groupId>
			<artifactId>jsch</artifactId>
			<version>0.1.55</version>
		</dependency>

		
		<!-- https://mvnrepository.com/artifact/org.apache.curator/curator-framework -->
		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-framework</artifactId>
			<version>2.8.0</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.apache.curator/curator-recipes -->
		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-recipes</artifactId>
			<version>2.8.0</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.apache.curator/curator-client -->
		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-client</artifactId>
			<version>2.8.0</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.apache.zookeeper/zookeeper -->
		<dependency>
			<groupId>org.apache.zookeeper</groupId>
			<artifactId>zookeeper</artifactId>
			<version>3.4.6</version>
			<type>pom</type>
		</dependency>
		
		
		<dependency>
            <groupId>com.sap</groupId>
            <artifactId>sapjco3</artifactId>
            <version>3.16</version>
         </dependency>

         <dependency>
            <groupId>com.sap</groupId>
            <artifactId>sapidoc3</artifactId>
            <version>3.16</version>
         </dependency> 
         
        
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.0</version>
        </dependency>
        
  
 		 <dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>4.5.2</version>
		</dependency>

		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpmime</artifactId>
			<version>4.5</version>
		</dependency>
		
		<dependency>
		    <groupId>commons-lang</groupId>
		    <artifactId>commons-lang</artifactId>
		    <version>2.6</version>
		</dependency>		
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-s3</artifactId>
			<version>1.11.933</version>
		</dependency>		
		<dependency>
   			<groupId>joda-time</groupId>
    			<artifactId>joda-time</artifactId>
	    		<version>2.10.1</version>
		</dependency>		
		<dependency>
			<groupId>org.quartz-scheduler</groupId>
			<artifactId>quartz</artifactId>
			<version>2.1.5</version>
		</dependency>		
		 <dependency>
			<groupId>commons-net</groupId>
			<artifactId>commons-net</artifactId>
			<version>3.3</version>
		</dependency>		
		<dependency>
		    <groupId>com.fasterxml.jackson.datatype</groupId>
		    <artifactId>jackson-datatype-jsr310</artifactId>
		    <version>2.13.2</version>
		</dependency>				
		<dependency>
			<groupId>io.projectreactor</groupId>
  			<artifactId>reactor-core</artifactId>
  			<version>3.4.17</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.8.1</version>
		</dependency>
		<dependency>
			<groupId>com.azure</groupId>
			<artifactId>azure-storage-blob</artifactId>
			<version>12.17.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-compress</artifactId>
			<version>1.20</version>
		</dependency> 

		<dependency>
         	<groupId>io.jsonwebtoken</groupId>
        	<artifactId>jjwt-api</artifactId>
       		<version>0.12.6</version>
   		</dependency>

   		<dependency>
     		<groupId>io.jsonwebtoken</groupId>
        	<artifactId>jjwt-impl</artifactId>
        	<version>0.12.6</version>
   		</dependency>

    	<dependency>
     		<groupId>io.jsonwebtoken</groupId>
       		<artifactId>jjwt-jackson</artifactId>
        	<version>0.12.6</version>
    	</dependency>
	</dependencies>



</project>