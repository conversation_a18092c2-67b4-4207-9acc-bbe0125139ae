package com.bsc.intg.svcs.core.rest;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Base64;
import java.util.List;
import javax.net.ssl.HttpsURLConnection;
import org.json.JSONObject;
import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestinationBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCMessage;

public class BSCRESTDestination extends BSCDestinationBase {

	HttpURLConnection conn = null;

	public BSCRESTDestination(BSCComponentType componentType, BSCComponent parentComponent, String configName) {
		super(componentType, parentComponent, configName);
	}

	@Override
	public void initialize() throws BSCComponentInitializationException {

		super.initialize();

		logger.info("Initializing REST Destination ");
		
		try {
			login();
		} catch (BSCServiceException e) {
			logger.error("Unable to login - " + e.toString());
			throw new BSCComponentInitializationException(this, BSCErrorLevel.CRITICAL, "Unable to login - " + e.toString());			
		}
	}

	@Override
	public BSCMessage onSend(BSCMessage message) throws BSCDestinationException {

		logger.info("Executing REST Destination onSend method - ");

		BSCMessage outMsg = null;
		boolean resetSession = false;		

		try {

			
			outMsg = message.cloneMessage(false);
			outMsg.loadMessage(invokeRESTCall(message).getStringMessage());
			
			if (getProperty("session.reset.codes").contains(message.getProperty("responsecode"))) {
				logger.info("HTTP response code matched with session reset codes, resetting the session ");
				resetSession=true;
			}
			
			if (getProperty("session.reset.values").contains(message.getProperty("responseMsg"))) {
				logger.info("HTTP response is matched with session reset values, resetting the session ");
				resetSession=true;
			}
			
			if (resetSession) {
				
				logger.info("Session seems to have been reset, attempting to establish a new session ");
				logout();
				login();
				outMsg = message.cloneMessage(false);
				outMsg.loadMessage(invokeRESTCall(message).getStringMessage());
				
				resetSession = false;
				
				if (getProperty("session.reset.codes").contains(message.getProperty("responsecode"))) {
					logger.info("HTTP response code matched with session reset codes, resetting the session ");
					resetSession=true;
				}
				
				if (getProperty("session.reset.values").contains(message.getProperty("responseMsg"))) {
					logger.info("HTTP response is matched with session reset values, resetting the session ");
					resetSession=true;
				}
				
				if (resetSession) {
					logger.error("Not able to establish the session ");
					throw new BSCDestinationException(this, BSCErrorLevel.CRITICAL, "Not able to establish the session ");
				}
			}

		} catch (BSCServiceException | BSCMessageException e) {
			logger.error("Failed to process the request " + e.toString());
			throw new BSCDestinationException(this, e, BSCErrorLevel.CRITICAL, "Failed to process the request " + e.toString());
		}

		return outMsg;

	}

	public void login() throws BSCServiceException {

		int responseCode = 0;

		switch (getProperty("login.authtype")) {

		case "OAUTH":

			logger.info("Executing login using oauth - ");

			try {				

				URL urlObj = new URL(getProperty("login.url"));

				conn = (HttpsURLConnection) urlObj.openConnection();

				conn.setRequestProperty("Content-Type", getProperty("login.contenttype"));
				conn.setRequestProperty("connection", "Keep-Alive");
				conn.setRequestMethod(getProperty("login.requestmethod"));
				conn.setDoOutput(true);

				StringBuffer content = new StringBuffer();
				content.append("grant_type=" + getProperty("login.granttype"));
				content.append("&client_id=" + getProperty("login.clientid"));
				content.append("&client_secret=" + getProperty("login.clientsecret"));
				content.append("&username=" + getProperty("login.username"));
				if(getProperty("login.password").contains("B64://")){
					setProperty("login.password", new String(Base64.getDecoder().decode(getProperty("login.password").replace("B64://",""))));	
				}
				content.append("&password=" + getProperty("login.password") + getProperty("login.securitytoken"));
				content.append("&format=" + getProperty("login.format"));

				logger.info("Content data - " + content.toString());

				OutputStream os = conn.getOutputStream();
				os.write(content.toString().getBytes());
				os.flush();

				responseCode = conn.getResponseCode();

				if (responseCode == HttpsURLConnection.HTTP_OK) {
					logger.info("Login successful with response code - {} ", responseCode);
				} else {
					logger.info("Login failure with error code - {} ", responseCode);
				}

				BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
				String inputLine;
				StringBuffer responseData = new StringBuffer();

				while ((inputLine = in.readLine()) != null) {
					responseData.append(inputLine);
				}
				in.close();

				logger.info("Authorization response - " + responseData.toString());

				JSONObject json = new JSONObject(responseData.toString());
				this.setProperty("login.accesstoken", json.getString("access_token"));
				this.setProperty("login.intsanceurl", json.getString("instance_url"));
				this.setProperty("login.authorization", getProperty("login.tokentype") + getProperty("login.accesstoken"));

				logger.info("Access Token - " + getProperty("login.accesstoken"));
				logger.info("Instance URL - " + getProperty("login.intsanceurl"));
				logger.info("Authorization - " + getProperty("login.authorization"));

			} catch (IOException e) {
				logger.error("Failed to login using OAUTH ERROR CODE - {} ERROR REASON - {}", responseCode,
						e.toString());
				throw new BSCServiceException(this, e, BSCErrorLevel.CRITICAL,
						"Failed to login - " + e.toString());
			}
			break;
			
		case "BASIC":
			logger.info("Executing login using basic authentication - ");
			this.setProperty("login.authorization", "Basic ");
			break;
		default:
			logger.error("Unspecified destination action ");
			throw new BSCServiceException(this, BSCErrorLevel.CRITICAL, "Unspecified destination action ");
		}

	}

	public void logout() {
		
		logger.info("Logging out - ");
		
		this.setProperty("login.accesstoken", "");
		this.setProperty("login.intsanceurl", "");
		this.setProperty("login.authorization", "");
		
		logger.info("Logged out successfully - ");
		
	}

	public BSCMessage invokeRESTCall(BSCMessage message) throws BSCServiceException {

		BSCMessage outMsg = null;

		try {

			String requestType = getProperty("rest.requestmethod");
			String requestBody = message.getStringMessage();
			String url = getProperty("login.intsanceurl")
					+ message.getProperty("rest.url");

			logger.info("REST URL - {}", url);

			logger.info("Authorization - {}", getProperty("login.authorization"));

			URL urlObj = new URL(url);

			if (url.contains("https"))
				conn = (HttpsURLConnection) urlObj.openConnection();
			else
				conn = (HttpURLConnection) urlObj.openConnection();

			conn.setRequestProperty("connection", "Keep-Alive");
			conn.setRequestMethod(requestType);
			conn.setRequestProperty("Authorization", getProperty("login.authorization"));

			if (requestType.equals("POST") || requestType.equals("PUT")) {
				if (requestBody.length() > 0) {
					conn.setDoOutput(true);
					OutputStream os = conn.getOutputStream();
					os.write(requestBody.getBytes());
					os.flush();
				}
			}

			int responseCode = conn.getResponseCode();
			message.setProperty("responsecode", String.valueOf(responseCode));
			message.setProperty("responseMsg", conn.getResponseMessage());

			if (responseCode == HttpURLConnection.HTTP_OK) {
				logger.info("REST call is invoked succesfully with response code - {} ", responseCode);
			} else {
				logger.info("REST call failed with error code - {} ", responseCode);
			}

			BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
			String inputLine;
			StringBuffer responseData = new StringBuffer();

			while ((inputLine = in.readLine()) != null) {
				responseData.append(inputLine);
			}
			in.close();

			logger.info("Response data - {}", responseData.toString());

			outMsg = message.cloneMessage(false);
			outMsg.loadMessage(responseData.toString());

		} catch (BSCMessageException | IOException e) {
			logger.error("Failed to invoke REST call - " + e.toString());
			throw new BSCServiceException(this, e, BSCErrorLevel.CRITICAL, "Failed to invoke REST call - " + e.toString());
		}

		return outMsg;
	}

	@Override
	public List<BSCMessage> onSend(List<BSCMessage> message) throws BSCDestinationException {
		// TODO Auto-generated method stub
		return null;
	}
}
