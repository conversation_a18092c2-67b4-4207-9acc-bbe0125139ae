package com.bsc.intg.svcs.core.jdbc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import javax.sql.DataSource;

import org.springframework.jdbc.core.JdbcTemplate;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentSubType;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCListenerBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCPropertyHelper;

public class BSCJDBCListener extends BSCListenerBase {

	private BSCJDBCListenerContainer sqlc = null;
	private JdbcTemplate jdbcTemplate = null;
	private boolean lockRequired, lock,outMsgPerRow = false;
	private String lockPrefix, lockName, connection, cron, outMsgFormat, queryCommand, inProgressCommand, commitCommand, rollbackCommand,datacolumn, keycolumn, delimiter  = null;
	private int lockTimeOut = 0;
	private List<String> skipcolumns = Collections.emptyList();
	private List<String> pstValues = Collections.emptyList();

	public BSCJDBCListener(BSCComponentType componentType, BSCComponent parentComponent, String configName) {
		super(componentType, parentComponent, configName);
	}

	@Override
	public void initialize() throws BSCComponentInitializationException {
		logger.info("Initializing Sql Listener");
		super.initialize(BSCComponentSubType.JDBC_LISTENER);

		try {
			connection = getNonEmptyProperty("connection");
			jdbcTemplate = createJdbcTemplate(connection);
			cron = getNonEmptyProperty("schedule");
			outMsgPerRow = getBooleanProperty("outmsg.perrow", false);
			outMsgFormat = getProperty("outmsg.format", "json");
			queryCommand = getNonEmptyProperty("querycommand");
			inProgressCommand = getProperty("inprogresscommand");
			commitCommand = getProperty("commitcommand");
			rollbackCommand = getProperty("rollbackcommand");
			datacolumn = getProperty("datacolumn");
			keycolumn = getProperty("keycolumn");
			delimiter = getProperty("outmsg.delimiter");
			String skipColumnsString = getProperty("skipcolumns");
			if (skipColumnsString != null) {
				skipcolumns = Arrays.asList(skipColumnsString.split(","));
			}
			String pstValuesString = getProperty("pst.values");
			if (pstValuesString != null) {
				pstValues = Arrays.asList(pstValuesString.split(","));
			}

			lockRequired = getBooleanProperty("lock.required", false);
			lockPrefix = getProperty("lock.prefix", "/SP8/" + getGlobalProperty("spring.profiles.active").toUpperCase()
					+ "/" + getGlobalProperty("bsc.service.name"));
			lockName = getProperty("lock.name", this.getConfigName());
			lockTimeOut = getIntProperty("lock.timeout", 0);

			sqlc = new BSCJDBCListenerContainer(logger);
			sqlc.setJdbcSqlListener(this);
			sqlc.setJdbcTemplate(jdbcTemplate);
			sqlc.setCron(cron);
			sqlc.setOutMsgPerRow(outMsgPerRow);
			sqlc.setOutMsgFormat(outMsgFormat);
			sqlc.setQueryCommand(queryCommand);
			sqlc.setInProgressCommand(inProgressCommand);
			sqlc.setCommitCommand(commitCommand);
			sqlc.setDatacolumn(datacolumn);
			sqlc.setKeycolumn(keycolumn);
			sqlc.setDelimiter(delimiter);
			sqlc.setSkipcolumns(skipcolumns);
			sqlc.setRollbackCommand(rollbackCommand);
			sqlc.setLock(lockRequired);
			sqlc.setLockDir(lockPrefix + lockName);
			sqlc.setLockTimeOut(lockTimeOut);
			sqlc.setPstValues(pstValues);
			sqlc.initialize();
		} catch (Throwable e) {
			throw new BSCComponentInitializationException(this, e, BSCErrorLevel.CRITICAL,
					"Failed to initialize the JDBC listener " + getInstanceName());
		}
	}

	public JdbcTemplate createJdbcTemplate(String connection) {
		DataSource ds = ((BSCJDBCDataSourceResource) this.getServiceContainer().getApplicationService()
				.getResource(connection)).getDataSource();
		JdbcTemplate jdbcTemplate = new JdbcTemplate(ds);
		return jdbcTemplate;
	}

	public void onMessage(String sqlMessage) throws BSCServiceException {
		logger.debug("SQL Listener: On Message");

		BSCMessage message = null;
		try {

			if (!scriptServiceEnabled) {
				scriptServiceName = null;
			}

			logger.info("Creating the message for the SQL Message");
			message = new BSCMessage(this, eventServiceName, scriptServiceName);
			message.addProperties(this.getProperties(), true, this.getConfigName());
			message.write(sqlMessage.getBytes());
			BSCPropertyHelper.printMessageContext(message, logger);

			onMessage(message);

		} catch (BSCServiceException e) {

			if (e.getLevel() == BSCErrorLevel.ROLLBACK) {
				logger.error("Rolling back the message", e);
				message.setException(e);
				message.generateEvent(this.errorEventName);
				message.clearException();
				throw e;
			} else {
				logger.error("Error occurred while invoking the service, continuing with the next message", e);
				message.setException(e);
				message.generateEvent(this.errorEventName);
				message.clearException();
				throw e;
			}
		} catch (Throwable e) {
			logger.error("Error occurred while invoking the service, continuing with the next message", e);
			message.setException(e);
			message.generateEvent(this.errorEventName);
			message.clearException();
			throw new BSCServiceException(this, e, BSCErrorLevel.ROLLBACK, "Failed to execute the app service");

		} finally {

			if (message != null) {
				message.close();
				message = null;
			}

		}
	}
}
