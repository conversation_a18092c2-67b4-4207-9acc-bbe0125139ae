package com.bsc.intg.svcs.core.boot;

import java.io.IOException;
import java.security.cert.X509Certificate;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.web.authentication.preauth.x509.X509AuthenticationFilter;

public class BSCX509AuthenticationFilter extends X509AuthenticationFilter {
	
	private static final Logger log = LoggerFactory.getLogger(BSCX509AuthenticationFilter.class);

		@Override
	    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
			
		    X509Certificate[] certs = (X509Certificate[]) request.getAttribute("javax.servlet.request.X509Certificate");
	
		    if (certs != null && certs.length > 0) {
		        
		    	log.debug("X.509 client authentication certificate: {}" , certs[0]);
		    	log.info ( "Serial = {} Issuer DN = {} " ,  certs[0].getSerialNumber() , certs[0].getIssuerDN().toString());
		    	
		    	String clientCredentials=certs[0].getSerialNumber() + "#" +  certs[0].getIssuerDN().toString();
		    	
		    	if ( System.getProperty("bsc.ssl.client.credentials","").equals(clientCredentials) )
		        	 chain.doFilter(request, response);
		        else
		        	throw new BadCredentialsException("Invalid client credentials -" +  clientCredentials );
		        
		    } else {
		    	log.info("No client certificate found in request.");
		        throw new BadCredentialsException("Invalid Serial");
		    }
		}
		
	}
