
package com.bsc.intg.svcs.core.test;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.atomic.AtomicInteger;
import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestinationBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCPropertyHelper;


public class BSCKafkaDestinationStub extends BSCDestinationBase {

	AtomicInteger kafkaDstcount = new AtomicInteger(0); 
	ArrayList<String> tcList = new ArrayList<String>();


	public BSCKafkaDestinationStub(BSCComponentType componentType, BSCComponent parentComponent, String configName) {
		super(componentType, parentComponent, configName);
	}


	public void initialize() throws BSCComponentInitializationException {

		super.initialize();

		logger.info("Initializing Kafka Stub");
		logger.info("Initial kafka stub destination count - {}", kafkaDstcount.get());

	}

	@Override
	public BSCMessage onSend(BSCMessage message) throws BSCDestinationException {

		logger.info("Executing onSend method - Kafka Stub");

		String sep = File.separator;
		String workingDir = System.getProperty("user.dir");
		String outputDir = workingDir + sep + "test"+ sep + "output" + sep;
		boolean matchFound = false;

		String testCaseName = message.getProperty("JMS.USER.TestCase");
		String serviceStatus = message.getProperty("JMS_CORRELATION_ID");


		logger.info("Test case - {}", testCaseName);
		logger.info("Service status - {}", serviceStatus);
		logger.info("Destination - {}", configName);		
		logger.info("Output file location - {}", outputDir);		
		logger.info("Kafka destination count - {}", kafkaDstcount.get());


		for (int idx =0; idx<tcList.size();idx++) {
			if (tcList.get(idx).equals(testCaseName)) {
				matchFound = true;
			}	
		}

		if (!matchFound) {
			kafkaDstcount.set(0);	
			tcList.add(testCaseName);	
		}	

		String fileName = configName + "_" + testCaseName + "_" + String.format("%02d", kafkaDstcount.incrementAndGet())  + ".txt";
		logger.info("Output file - {} ", fileName);

		String deliveryCount=message.getProperty("JMS.USER.JMSXDeliveryCount");

		logger.info("deliveryCount  - {} ", deliveryCount);


		try{	
			if (!new File(outputDir).exists()) {
				new File(outputDir).mkdir();
			}
			
			String filePath = outputDir + fileName;

			if(serviceStatus.contains("Down")){
				if (Integer.parseInt(deliveryCount)<3){
					
					logger.error("KafkaDestinationStub is down");
					writeToFile(message.getProperties(), "KafkaDestinationStub is down", filePath);
					throw new BSCDestinationException(this, BSCErrorLevel.CRITICAL,  "KafkaDestinationStub is down");
				}
			}
			else{
				writeToFile(message.getProperties(), message.toString(), filePath);
			}

		}

		catch (Exception  e) {
			throw new BSCDestinationException(this, e, BSCErrorLevel.CRITICAL,"Output file creation failed." + e.toString());
		}

		return message;
	}
	
public void writeToFile(Map<String, Object> props, String data, String filePath) throws IOException {
		
		BufferedWriter writer = new BufferedWriter(new FileWriter(filePath));
		Map<String, Object> httpHeaders = (Map<String, Object>) BSCPropertyHelper.getPropertiesStartingWith(props,
				"HTTP", false);
		for (Entry<String, Object> p : httpHeaders.entrySet()) {
			writer.write(p + "\n");
		}
		Map<String, Object> jmsHeaders = (Map<String, Object>) BSCPropertyHelper
				.getPropertiesStartingWith(props, "JMS", false);
		for (Entry<String, Object> p : jmsHeaders.entrySet()) {
			writer.write(p + "\n");
		}
		
		writer.write(data + "\n");
		writer.close();

	}



	@Override
	public List<BSCMessage> onSend(List<BSCMessage> message) throws BSCDestinationException {
		// TODO Auto-generated method stub
		return null;
	}

}