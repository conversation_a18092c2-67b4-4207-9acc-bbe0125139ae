package com.bsc.intg.svcs.core.jms;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.net.Socket;
import java.security.KeyStore;
import java.security.Principal;
import java.security.PrivateKey;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.Map;

import javax.jms.ConnectionFactory;
import javax.jms.Destination;
import javax.jms.JMSException;
import javax.jms.XAConnectionFactory;
import javax.naming.NamingException;
import javax.net.ssl.KeyManager;
import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509KeyManager;

import org.slf4j.Logger;

import com.ibm.mq.jms.MQQueue;
import com.ibm.msg.client.jms.JmsConnectionFactory;
import com.ibm.msg.client.jms.JmsFactoryFactory;
import com.ibm.msg.client.wmq.WMQConstants;




public class JMSMQProvider implements JMSProvider {

	
	private Logger logger=null;
	
	private class FilteredKeyManager implements X509KeyManager {

	    private final X509KeyManager originatingKeyManager;
	    private final X509Certificate sslCertificate;
	    private final String SSLCertificateKeyStoreAlias;

	    public FilteredKeyManager(X509KeyManager originatingKeyManager, X509Certificate sslCertificate, String SSLCertificateKeyStoreAlias) {
	        this.originatingKeyManager = originatingKeyManager;
	        this.sslCertificate = sslCertificate;
	        this.SSLCertificateKeyStoreAlias = SSLCertificateKeyStoreAlias;
	    }

	    @Override
	    public String chooseClientAlias(String[] keyType, Principal[] issuers, Socket socket) {
	        return SSLCertificateKeyStoreAlias;
	    }

	    @Override
	    public String chooseServerAlias(String keyType, Principal[] issuers, Socket socket) {
	        return originatingKeyManager.chooseServerAlias(keyType, issuers, socket);
	    }

	    @Override
	    public X509Certificate[] getCertificateChain(String alias) {
	        return new X509Certificate[]{ sslCertificate };
	    }

	    @Override
	    public String[] getClientAliases(String keyType, Principal[] issuers) {
	        return originatingKeyManager.getClientAliases(keyType, issuers);
	    }

	    @Override
	    public String[] getServerAliases(String keyType, Principal[] issuers) {
	        return originatingKeyManager.getServerAliases(keyType, issuers);
	    }

	    @Override
	    public PrivateKey getPrivateKey(String alias) {
	        return originatingKeyManager.getPrivateKey(alias);
	    }
	}
	
	
	public JMSMQProvider(Logger logger ) {
		this.logger=logger;
	}

	/**
	 * @return ConnectionFactory object
	 * @throws NamingException
	 * @throws JMSException 
	 */
	public ConnectionFactory getConnectionFactory( ) throws NamingException, JMSException {
		
			JmsFactoryFactory factoryFactory = null;
			JmsConnectionFactory connectionFactory =null;

			
			factoryFactory = JmsFactoryFactory.getInstance(WMQConstants.WMQ_PROVIDER);
			
			connectionFactory = factoryFactory.createQueueConnectionFactory();

			if (System.getProperty("mq.ssl.enable","false").equals("true") ) {
				connectionFactory.setObjectProperty(
								  WMQConstants.WMQ_SSL_SOCKET_FACTORY, getSSLSocketFactory());
			}
			return connectionFactory;
			
	}
	
	private SSLSocketFactory getSSLSocketFactory(){
		
		SSLSocketFactory sslSocketFactory = null;
		try{
			
		String keystorePassword=System.getProperty("mq.ssl.keystore.password", System.getProperty("javax.net.ssl.keyStorePassword"));
		String keyStoreFile=System.getProperty("mq.ssl.keystore", System.getProperty("javax.net.ssl.keyStore"));
		
		String trustStoreFile=System.getProperty("mq.ssl.truststore",System.getProperty("javax.net.ssl.trustStore"));
		String trustStorePassword=System.getProperty("mq.ssl.truststore.password", System.getProperty("javax.net.ssl.trustStorePassword"));
		
		String clientAlias=System.getProperty("mq.ssl.client.alias","client");
		
		KeyStore keyStore = KeyStore.getInstance(System.getProperty("mq.ssl.keystore.type","JKS"));
		java.io.FileInputStream keyStoreInputStream = new java.io.FileInputStream(keyStoreFile);
		keyStore.load (keyStoreInputStream, keystorePassword.toCharArray());

		KeyStore trustStore= KeyStore.getInstance (System.getProperty("mq.ssl.truststore.type","JKS"));
		java.io.FileInputStream trustStoreInputStream = new java.io.FileInputStream(trustStoreFile);
		trustStore.load (trustStoreInputStream, trustStorePassword.toCharArray());

		keyStoreInputStream.close();
		trustStoreInputStream.close();

		KeyManagerFactory keyManagerFactory = 
		  KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
		TrustManagerFactory trustManagerFactory = 
		  TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
		
		keyManagerFactory.init(keyStore,keystorePassword.toCharArray());
		trustManagerFactory.init(trustStore);

		SSLContext sslContext = SSLContext.getInstance(System.getProperty("mq.ssl.protocol","TLSv1.2")); 
		
		//sslContext.init(keyManagerFactory.getKeyManagers(),  trustManagerFactory.getTrustManagers(),   null);
		sslContext.init(new KeyManager[] { new FilteredKeyManager((X509KeyManager)keyManagerFactory.getKeyManagers()[0], (X509Certificate)keyStore.getCertificateChain(clientAlias)[0], clientAlias) },    trustManagerFactory.getTrustManagers(), new SecureRandom());
		
		sslSocketFactory = sslContext.getSocketFactory(); 
		} catch (Exception e) {
			e.printStackTrace();
			
		}
		return sslSocketFactory;

		
	}
	
	public XAConnectionFactory getXAConnectionFactory(String  xaConnectionFactoryClass)throws Exception {
		
		XAConnectionFactory xaqcf = null;
		try {
		Class<?> xa = Class.forName(xaConnectionFactoryClass);
		Constructor<?> ctor = xa.getDeclaredConstructor();
		xaqcf = (XAConnectionFactory) ctor.newInstance();
		} catch (ClassNotFoundException | NoSuchMethodException | SecurityException | InstantiationException | IllegalAccessException | IllegalArgumentException | InvocationTargetException e) {
			throw e;
		}

		if (System.getProperty("mq.ssl.enable","false").equals("true") ) {
			
			try {
					((JmsConnectionFactory)xaqcf).setObjectProperty(WMQConstants.WMQ_SSL_SOCKET_FACTORY, getSSLSocketFactory());
				} catch (JMSException e) {
							throw e;
				}
			
			
		}
		return xaqcf;
		
}
	
	


	/**
	 * @param destination
	 *           Destination Name
	 * @param destinationProperties
	 * 			set of properties to be applied on the destination object          
	 */
	public void setDestinationProperties(Destination destination, Map<String,String> destinationProperties) {
		
		for (Map.Entry<String, String> entry : destinationProperties.entrySet()) {
            
			
			logger.info("Checking destination property name : {}  value : {}" , entry.getKey() , entry.getValue());
			
			propertyloop:
			for (Field f : WMQConstants.class.getFields()) {
				
	           if (f.getName().equals(entry.getKey())) {
	        	   
	        	   switch (f.getType().getSimpleName()) {
	        	   
		        	   case "String":
							
		        		   logger.info("Setting destination property name (string) : {}  value : {}" , entry.getKey() , entry.getValue());
		        		   try {
								((MQQueue)destination).setStringProperty((String)f.get(null), entry.getValue());
								return;
								
		        		   } catch (JMSException | IllegalArgumentException | IllegalAccessException e) {
								e.printStackTrace();
							}
							
							break propertyloop;
		        	   
		        	   case "int":
		        		   logger.info("Setting destination property name (int) : {}  value : {}" , entry.getKey() , entry.getValue());
		        		   try {
		        			   ((MQQueue)destination).setIntProperty((String)f.get(null), Integer.valueOf(entry.getValue()));
								return;
							} catch (JMSException | IllegalArgumentException | IllegalAccessException e) {
								e.printStackTrace();
							}
		        		   
		        		   
		        		   break propertyloop;
		        	   
		        	   case "boolean":
		        		   logger.info("Setting destination property name (boolean) : {}  value : {}" , entry.getKey() , entry.getValue());
		        		   try {
		        			   ((MQQueue)destination).setBooleanProperty((String)f.get(null), Boolean.valueOf(entry.getValue()));
								return;
							} catch (JMSException | IllegalArgumentException | IllegalAccessException e) {
								e.printStackTrace();
							}
		        		   
		        		   break propertyloop;
		        		   
	        	   }
	           }
	        }
		}	

	}
	

	/**
	 * @param connectionFactory
	 *           JMS Connection factory
	 * @param connectionProperties
	 * 			set of properties to be applied on the connection factory object          
	 */
	public void setConnectionProperties(ConnectionFactory connectionFactory, Map<String,String> connectionProperties) {
		
		
		for (Map.Entry<String, String> entry : connectionProperties.entrySet()) {
            
			logger.debug("Checking connection property name : {}  value : {}" , entry.getKey() , entry.getValue());
			
				propertyloop:
				for (Field f : WMQConstants.class.getFields()) {
					
		           if (f.getName().equals(entry.getKey())) {
		        	   
		        	   switch (f.getType().getSimpleName()) {
		        	   
			        	   case "String":
							
			        		   logger.info("Setting connection property name (string) : {}  value : {}" , entry.getKey() , entry.getValue());
			        		  
			        		   try {
									((JmsConnectionFactory)connectionFactory).setStringProperty((String)f.get(null), entry.getValue());
									
			        		    } catch (JMSException | IllegalArgumentException | IllegalAccessException e) {
									e.printStackTrace();
								}
								break propertyloop;
			        	   
			        	   case "int":
			        		   
			        		   logger.info("Setting connection property name (int) : {}  value : {}" , entry.getKey() , entry.getValue());
			        		   try {
			        			   ((JmsConnectionFactory)connectionFactory).setIntProperty((String)f.get(null), Integer.valueOf(entry.getValue()));
									return;
								} catch (JMSException | IllegalArgumentException | IllegalAccessException e) {
									e.printStackTrace();
								}
			        		   
			        		   
			        		   break propertyloop;
			        	   
			        	   case "boolean":
			        		   
			        		   logger.debug("Setting connection property name (boolean) : {}  value : {}" , entry.getKey() , entry.getValue());
			        		   try {
			        			   ((JmsConnectionFactory)connectionFactory).setBooleanProperty((String)f.get(null), Boolean.valueOf(entry.getValue()));
									return;
								} catch (JMSException | IllegalArgumentException | IllegalAccessException e) {
									e.printStackTrace();
								}
			        		   
			        		   break propertyloop;
			        		   
		        	   }
		           }
		        }
			
			}	
		}

	@Override
	public Destination getDestination(String destinationName, String destinationType) throws NamingException {
		
		MQQueue queue =null;
		
		if ( destinationType.equals("queue")) {
			
			try {
			
				queue=new MQQueue(destinationName);
				queue.setIntProperty(WMQConstants.WMQ_MESSAGE_BODY,WMQConstants.WMQ_MESSAGE_BODY_JMS); 
			
			} catch (JMSException e) {
				e.printStackTrace();
			}
		}
		return queue;
	}
}
