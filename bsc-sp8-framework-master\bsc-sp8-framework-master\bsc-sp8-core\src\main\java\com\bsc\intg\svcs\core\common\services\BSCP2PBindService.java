package com.bsc.intg.svcs.core.common.services;

import java.util.ArrayList;
import java.util.List;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestination;
import com.bsc.intg.svcs.core.BSCIntegrationServiceBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCStringUtils;

public class BSCP2PBindService extends BSCIntegrationServiceBase{

	public BSCP2PBindService(BSCComponentType componentType, BSCComponent parentComponent, String configName,
			String instance) {
		super(componentType, parentComponent, configName, instance);
		
	}
	
	List<BSCDestination> dests = new ArrayList<BSCDestination>();
	

	@Override
	public BSCMessage service(BSCMessage message) throws BSCServiceException {
		logger.info("Message received in P2PBindService");	
		BSCMessage outMsg = null;
		try {		
			outMsg = message.cloneMessage(false);			
					
			for(BSCDestination dest: dests){
				outMsg = dest.send(message);
			}					
		} catch ( BSCDestinationException | BSCMessageException  e) {
			message.setException(e);
			logger.error("Failed to process the request: "+e.getMessage());
			throw new BSCServiceException(this,e, e.getLevel(), "Failed to process the request: "+e.getMessage() );
		} catch(Exception e){
			message.setException(e);
			logger.error("An unexpected error occurred: "+e);
			throw new BSCServiceException(this,e, BSCErrorLevel.CRITICAL, "An unexpected error occurred: "+e.getMessage() );
		}
		
		return outMsg;
	}
	
	
	@Override
	public void initialize() throws BSCComponentInitializationException {

		super.initialize();
		
		logger.info("Initializing destination");
		
		BSCStringUtils.convertCommaSeperated(getProperty("destinations"))
		.stream()
		.forEach(destination -> dests.add(getDestination(destination)));
		
		valid=true;
	}

}
