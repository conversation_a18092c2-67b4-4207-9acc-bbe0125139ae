package com.bsc.intg.svcs.core.common.services;

import java.util.Base64;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCIntegrationServiceBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCCryptoHelper;




public class BSCBasicAuthService extends BSCIntegrationServiceBase {

	
	protected String user = null;
	protected String password = null;
	
	
	public BSCBasicAuthService(BSCComponentType componentType, BSCComponent parentComponent, String configName,
			String instance) {
		super(componentType, parentComponent, configName, instance);
	}
	

@SuppressWarnings("rawtypes")
@Override
	public BSCMessage service(BSCMessage inMessage) throws BSCServiceException {
		logger.debug("Executing BSCBasicAuthService service");
		try {
			inMessage = login(inMessage);
			
		} catch (BSCDestinationException e) {
			throw new BSCServiceException(this,BSCErrorLevel.CRITICAL, "Failed to authenticate the message in BSCOAuthService");
		}
		
		return inMessage;
	
	}



/**
 * Initializes all the resources of the service
 * 
 * @throws BSCComponentInitializationException
 * 			if any exception occurs during initialization of the components or resources of the service.
 */
	@Override
	public void initialize() throws BSCComponentInitializationException {

		super.initialize();
		valid = true;
		password = getProperty("basic.password");
		user = getProperty("basic.user");

//		
		
	}



public BSCMessage login(BSCMessage message) throws BSCDestinationException {

	try{
	if (password.contains("ss://"))
		password = BSCCryptoHelper.getSecret(password);
	
	String Auth = user + ":" + password;
	String pwd = Base64.getEncoder().encodeToString(Auth.getBytes());
	Auth = "Basic " + pwd;
	message.setProperty("REQUEST.HTTP.HEADER.Authorization", Auth);
	} catch (Exception e) {
		this.getLogger().error("Error decrypting the password");
		throw new BSCDestinationException(this, e, BSCErrorLevel.FATAL,
				"Authentication Failed " + e.toString());
	}
	return message;
	
}


	
}
