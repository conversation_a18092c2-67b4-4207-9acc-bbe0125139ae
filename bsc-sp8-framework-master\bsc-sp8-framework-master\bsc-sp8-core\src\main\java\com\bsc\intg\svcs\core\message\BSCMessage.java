package com.bsc.intg.svcs.core.message;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.StringReader;
import java.io.StringWriter;
import java.io.UnsupportedEncodingException;
import java.io.Writer;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Enumeration;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.TimeZone;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.jms.BytesMessage;
import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.TextMessage;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.UriInfo;
import javax.xml.transform.Result;
import javax.xml.transform.Source;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.stream.StreamSource;
import javax.xml.ws.handler.MessageContext;

import org.apache.commons.lang.StringUtils;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCControllerBase;
import com.bsc.intg.svcs.core.BSCListenerBase;
import com.bsc.intg.svcs.core.BSCService;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.util.BSCPropertyHelper;
import com.bsc.intg.svcs.core.vfs.BSCVFSMessage;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.ibm.msg.client.wmq.WMQConstants;

public class BSCMessage extends ByteArrayOutputStream {

	public enum MessageType { BINARY, MODEL };

	protected SimpleDateFormat				formatter										= new SimpleDateFormat("yyyyMMddHHmmssSSSzzz");

	protected Map<String, Object> 			properties										= new LinkedHashMap<String,Object>();
	
	protected BSCComponent 					messageOwner									= null;
	
	protected Throwable						exception 										= null;
	
	protected String						scriptServiceName								= null;
	
	protected String						eventServiceName								= null;

	
	protected String						encoding										= "UTF-8";
	
	protected String						script											= null;

	protected 	JsonNode 					jsonNode										= null;
	
	private 	boolean 					eventDisabled       							=  false;
	
	protected BSCMessageModel				messageModel									= null;
	
	protected MessageType					messageType										= MessageType.BINARY;
	
	protected BSCDestinationAction			action											=  null;   
	
	protected String						scriptFnBody									= null;
	
	protected String						scriptFnName									= null;
	
	protected Object						scriptFnResult									= null;
	
	public BSCDestinationAction getAction() {
		return action;
	}
	
	public void setAction( BSCDestinationAction action) {
		this.action=action;
	}
	
	public void executeScript(String script) throws BSCServiceException {
		
		BSCService scriptService = null;
		
		if (scriptServiceName != null ) {
				
			try {
				
				if (messageOwner.getComponentType() == BSCComponentType.LISTENER ) {
					scriptService=	((BSCListenerBase)messageOwner).getServiceContainer().getService(scriptServiceName);
				} else if (messageOwner.getComponentType() == BSCComponentType.CONTROLLER  ) {
					scriptService=	((BSCControllerBase)messageOwner).getServiceContainer().getService(scriptServiceName);
				}
				
				if (script != null && scriptService != null ) { 
					this.script=script;  
					scriptService.execute(this);
					script=null;
				}
				
			} catch (BSCServiceException se) {
				messageOwner.getLogger().error("Failed to execute the script {}", se);
			} finally {
					
				if(scriptService != null) {
	
					try {
						
						if (messageOwner.getComponentType() == BSCComponentType.LISTENER ) {
							((BSCListenerBase)messageOwner).getServiceContainer().releaseService(scriptServiceName, scriptService);
						} else if (messageOwner.getComponentType() == BSCComponentType.CONTROLLER  ) {
							((BSCControllerBase)messageOwner).getServiceContainer().releaseService(scriptServiceName, scriptService);
						}
						
					} catch (  BSCServiceException e) {
						messageOwner.getLogger().error("Failed to release the service {} to the pool. {}", scriptServiceName, e);
					}
				}
			}
		}
	}
	
	
	public void disableEvent() {
		this.eventDisabled=true;
	}
	
	public void enableEvent() {
		this.eventDisabled=false;
	}
	
	public String getScript() {
		return this.script;
	}
	
	public BSCMessage(BSCComponent messageOwner,String eventServiceName, String scriptServiceName) {
		this.messageOwner=messageOwner;
		this.scriptServiceName=scriptServiceName;
		this.eventServiceName=eventServiceName;
	}
	
	
	
	public BSCMessage loadMessage(String message, String encoding) throws BSCMessageException {
		
		this.encoding = encoding;
		
		try {
			this.write(message.getBytes(encoding));
		} catch (IOException e) {
			throw (new BSCMessageException(messageOwner, e, BSCErrorLevel.CRITICAL,"Failed to create the BSCMessage from the String", this));
		}
		
		return this;
	}
	
	public BSCMessage parseIntoJSON() throws BSCMessageException {
		
		XmlMapper xmlMapper = new XmlMapper();
		try {
			jsonNode = xmlMapper.readTree(this.toByteArray());
		} catch (IOException e) {
		}

		return this;
	}
	
	public JsonNode getJSON() {
		
		return this.jsonNode;
	}
	
	
	public BSCMessage loadMessage(String message) throws BSCMessageException {
		
		try {
			this.write(message.getBytes(encoding));
		} catch (IOException e) {
			throw (new BSCMessageException(messageOwner, e, BSCErrorLevel.CRITICAL,"Failed to create the BSCMessage from the String", this));
		}
		
		return this;
	}

	public String getStringMessage() throws BSCMessageException   {
		
		String strMessage  = null;
		
		try {
			strMessage = new String(this.toByteArray(),encoding);
		} catch (UnsupportedEncodingException e) {
			throw (new BSCMessageException(messageOwner, e, BSCErrorLevel.CRITICAL,"Failed to convert bytes to String with specified encoding", this));
		}
		
		return strMessage;
	}
	
	public BSCMessage cloneMessage(boolean clonePayload) throws BSCMessageException   {
		
		BSCMessage clonedMessage = new BSCMessage(messageOwner, eventServiceName, scriptServiceName);
		
		clonedMessage.addProperties(this.properties, true);
		
		if(clonePayload) {
		
			clonedMessage.loadMessage(this.toByteArray());
		}
		return clonedMessage;
	}
	
	
	public BSCMessage loadMessage( byte [] message) throws BSCMessageException {
		try {
			this.reset();
			this.write(message);
			
		} catch (IOException e) {
			throw (new BSCMessageException(messageOwner, e, BSCErrorLevel.CRITICAL,"Failed to create the BSCMessage from the byte array", this));
		}
		return this;
	}
		
	
	public BSCMessage loadMessage(BSCMessageModel messageModel) throws BSCMessageException {
		this.messageModel=messageModel;
		this.messageType=MessageType.MODEL;
		return this;
	}
	
	
	public BSCMessageModel getMessageModel() throws BSCMessageException {
		return messageModel;
	}
	
	
	public Throwable getException() {
		return exception;
	}

	public void setException(Throwable e) {
		this.exception = e;
	}
	
	public BSCComponent getMessageOwner() {
		return messageOwner;
	}
	

	public void setProperty( String key, String value) {
		properties.put(key, value);
	}
	
	public void setObjectProperty( String key, Object value) {
		properties.put(key, value);
	}
	
	
	public void removeProperty( String key) {
		properties.remove(key);
	}
	
	
	
	public String getProperty(String key, String defaultValue) {
		
		Object value = properties.get(key);
		if ( value == null ) {
			return defaultValue;
		} else {
			return (String) value;
		}
	}
	
	
	public Object getObjectProperty(String key, Object  defaultValue) {
		
		Object value = properties.get(key);
		if ( value == null ) {
			return defaultValue;
		} else {
			return  value;
		}
	}
	
	
	public String getProperty(String key) {
		return (String) properties.get(key);
	}
	
	public Object getObjectProperty(String key) {
		return  properties.get(key);
	}
	
	
	public void addProperties(Map<String,Object> toBeAdded, boolean overwrite) {
		
		for (Entry<String, Object> entry : toBeAdded.entrySet()){
			
			if ( properties.containsKey( entry.getKey() )  && !overwrite)	{
				continue;
			}
			properties.put( entry.getKey(), entry.getValue() );
		}
	}
	
	public void addProperties(Map<String,Object> toBeAdded, boolean overwrite, String prefix) {
		
		if (prefix != null && !prefix.equals("")) {
			prefix=prefix + ".";
		} else {
			prefix="";
		}
		
		for (Entry<String, Object> entry : toBeAdded.entrySet()){
			
			if ( properties.containsKey( entry.getKey() )  && !overwrite)	{
				continue;
			}
			properties.put( prefix + entry.getKey(), entry.getValue() );
		}
	}

	public BSCMessage loadMessage(javax.jms.Message inputMessage) throws BSCMessageException {
		
		try {

			Date	messageTime	= new Date();
			
			messageTime.setTime(inputMessage.getJMSTimestamp());
	
			setProperty("READ_TIME", formatter.format(new Date()));
			messageTime = new Date(inputMessage.getJMSTimestamp());
			setProperty("MESSAGE_TIME", formatter.format(messageTime));
			setProperty("HOST", java.net.InetAddress.getLocalHost().getHostName());
			
			if (inputMessage instanceof TextMessage) {
				TextMessage textMessage = (TextMessage) inputMessage;
				String messageText = textMessage.getText();
				this.write(messageText.getBytes());
			}
			else if (inputMessage instanceof BytesMessage) {
				BytesMessage bytesMessage = (BytesMessage) inputMessage;
				byte[] bytes = new byte[(int) bytesMessage.getBodyLength()];
				bytesMessage.readBytes(bytes);
				this.write(bytes);
			}
			else {
				throw (new BSCMessageException(messageOwner, BSCErrorLevel.CRITICAL,"Failed to create the BSCMessage from the JMS Message", this));
			}
	
		} catch(Exception ex) {
			throw (new BSCMessageException(messageOwner, ex, BSCErrorLevel.CRITICAL, this));
		}
		 return this;
		
	}
	
	
	public String getProperty(BSCMessageProperties key) {
		
		return (String) properties.get(key.name());
		
	}
	
	public void setProperty(BSCMessageProperties key, String value) {
		properties.put(key.name(), value);
	}
	


	
	public void copyJMSPropertiesTo(Message message,  Object ... properties) throws JMSException {

				
			if ( properties != null ) {
				
				for (Object p: properties) {
					if (p instanceof BSCMessageProperties) {
						copyJMSPropertyTo(message,(BSCMessageProperties)p);
					} else if (p instanceof Map<?,?>) {
						Map<String,Object> jmsUserProperties = BSCPropertyHelper.getPropertiesStartingWith((Map<String,Object>)p, "JMS.USER.", true);
						for(Entry<String,Object> pp: jmsUserProperties.entrySet()) {
							message.setStringProperty(pp.getKey(), (String)pp.getValue());
						}
					} else {
						message.setStringProperty((String)p, this.getProperty((String)p) );
					}
				}
				
			} else {
				
				for (BSCMessageProperties p: BSCMessageProperties.values()) {
					copyJMSPropertyTo(message,p);
				}
				Map<String,Object> jmsUserProperties = BSCPropertyHelper.getPropertiesStartingWith(this.properties, "JMS.USER.", true);
				for(Entry<String,Object> p: jmsUserProperties.entrySet()) {
					if (!p.getKey().contains("_IBM_") && p.getValue() != null )
						message.setStringProperty(p.getKey().trim(), ((String) p.getValue()).trim());
				}
			}
			
	}
	
	public void copyJMSPropertyTo(Message message, BSCMessageProperties p ) throws JMSException {
		
		switch (p) {

		case JMS_CORRELATION_ID:
			if (this.getProperty(p) != null) {
			
				String correlId = this.getProperty(p);
				message.setJMSCorrelationID(StringUtils.rightPad(correlId,24, " "));
			}

			break;
		case JMS_MESSAGE_ID:
			if (this.getProperty(p) != null) {
				message.setJMSMessageID(this.getProperty(p));
				String msgId = this.getProperty(p);
				message.setObjectProperty(WMQConstants.JMS_IBM_MQMD_MSGID,stringToId(StringUtils.rightPad(msgId, 24, " "), 24));
			}
			
			break;
		}
	}

	public static byte[] stringToId(String id, int length) {
		String EMPTY_ID = "                        ";

		if (id != null)
			return id.substring(0, length).getBytes(StandardCharsets.ISO_8859_1);
		else
			return EMPTY_ID.getBytes(StandardCharsets.ISO_8859_1);
	}


	
	public void copyJMSPropertiesFrom(Message message,  Object ... properties) throws JMSException {
		
				
			if ( properties != null ) {
				
				for (Object p: properties) {
					if (p instanceof BSCMessageProperties)
						copyJMSPropertyFrom(message, (BSCMessageProperties)p);
					else
						this.setProperty((String)p, message.getStringProperty((String) p));
				}
				
			} else {
				
				for (BSCMessageProperties p: BSCMessageProperties.values()) {
					if (p.name().startsWith("JMS_")) copyJMSPropertyFrom(message,p);
				}
				
				@SuppressWarnings("unchecked")
				Enumeration<String> propertyNames = message.getPropertyNames();
				while (propertyNames.hasMoreElements()) {
					String name = (String) propertyNames.nextElement();
					this.setProperty( "JMS.USER." + name, message.getStringProperty(name) );
				}
			}
	
	}
	

	public void copyJMSPropertyFrom(Message message, BSCMessageProperties p ) throws JMSException {
	
	
		switch (p) {

		case JMS_CORRELATION_ID:
			
				if (message.getJMSCorrelationID() != null) {
					
					try {
						if (message.getJMSCorrelationID().trim().length() < 1) {
							setProperty(BSCMessageProperties.JMS_CORRELATION_ID, "NoCorrID"  );
						}
						else {
							setProperty( BSCMessageProperties.JMS_CORRELATION_ID, message.getJMSCorrelationID().trim());
						}
						
					} catch (Exception jmsExc) {
						setProperty( BSCMessageProperties.JMS_CORRELATION_ID, "CorrIDExc");
					}
					
				} else {
					setProperty(BSCMessageProperties.JMS_CORRELATION_ID, "CorrIDExc");
				}
				break;
	
		
		case JMS_MESSAGE_ID:
				if (message.getJMSMessageID() !=null ) {
				
					try {
						if (message.getJMSMessageID().trim().length() < 1) {
							setProperty( BSCMessageProperties.JMS_MESSAGE_ID, "NoMsgID");
						}
						else {
			
							String JMSMsgId = message.getJMSMessageID().trim();
							setProperty( BSCMessageProperties.JMS_MESSAGE_ID, JMSMsgId);
						}
					}
					catch (Exception jmsExc) {
						setProperty( BSCMessageProperties.JMS_MESSAGE_ID, "NoMsgID");
					}
					
			} else {
				setProperty( BSCMessageProperties.JMS_MESSAGE_ID, "NoMsgID" );
			}
				
			break;
				
		case JMS_REPLY_TO:
			if (message.getJMSReplyTo() != null) {
				setProperty( BSCMessageProperties.JMS_REPLY_TO, message.getJMSReplyTo().toString());
			}
			break;
	
		}
	
	}
	
	public void copyHTTPPropertiesFrom( UriInfo uriInfo, HttpHeaders httpHeaders, Object ... properties) {
		
		
		if ( properties != null ) {
			
			/*for (Object p: properties) {
				if (p instanceof BSCMessageProperties)
					copyHTTPPropertyFrom(message, (BSCMessageProperties)p);
				else
					this.setProperty((String)p, message.getStringProperty((String) p));
			}*/
			
		} else {
			
			for (BSCMessageProperties p: BSCMessageProperties.values()) {
				if (p.name().startsWith("HTTP_")) copyHTTPPropertyFrom(httpHeaders,uriInfo,p);
			}
			
			Set<String> headerKeys = httpHeaders.getRequestHeaders().keySet();
	        for(String header:headerKeys){
	        	this.setProperty( "HTTP." + header, httpHeaders.getRequestHeader(header).get(0) );
	        }
	        
	        MultivaluedMap<String, String> queryparams = uriInfo.getQueryParameters();
	       
	        Iterator<String> it = queryparams.keySet().iterator();


	              while(it.hasNext()){
	                String theKey = (String)it.next();
	                this.setProperty( "HTTP." + theKey, queryparams.getFirst(theKey));
	            }
		}

}
	
	public void copyHTTPPropertyFrom(HttpHeaders httpHeaders, UriInfo uriInfo, BSCMessageProperties p ) {
		
		
		switch (p) {
				
		case HTTP_CONTENT_TYPE:
			if (httpHeaders.getRequestHeader("Content-Type") != null) {
				setProperty( BSCMessageProperties.HTTP_CONTENT_TYPE, httpHeaders.getRequestHeader("Content-Type").toString());
			}
			break;
			
		case HTTP_REQUEST_URI:
			if (uriInfo.getRequestUri() != null) {
				setProperty( BSCMessageProperties.HTTP_REQUEST_URI, uriInfo.getRequestUri().toString());
			}
			break;
	
		}
	
	}
	
	@SuppressWarnings("rawtypes")
	public void copySOAPHTTPPropertiesFrom( MessageContext mctx, HttpServletRequest webreq, Object ... properties) {
			
			
			if ( properties != null ) {
				
				/*for (Object p: properties) {
					if (p instanceof BSCMessageProperties)
						copyHTTPPropertyFrom(message, (BSCMessageProperties)p);
					else
						this.setProperty((String)p, message.getStringProperty((String) p));
				}*/
				
			} else {
				
				for (BSCMessageProperties p: BSCMessageProperties.values()) {
					if (p.name().startsWith("HTTP_")) copySOAPHTTPPropertyFrom(webreq,p);
				}
							
				Map <String, ArrayList<String>> map = (Map<String, ArrayList<String>>)mctx.get(MessageContext.HTTP_REQUEST_HEADERS);
				 
				Iterator<Entry<String, ArrayList<String>>> iterator = map.entrySet().iterator();
				while (iterator.hasNext())
				{
				Map.Entry<String, ArrayList<String>>  entry = (Entry<String, ArrayList<String>>)iterator.next();
				ArrayList<String> value = entry.getValue();
				this.setProperty("HTTP."+ entry.getKey(), value.get(0));
				}
				
				Map <String, String[]> quesryStringMap = webreq.getParameterMap();
			        Iterator<Entry<String, String[]>> mapIterator = quesryStringMap.entrySet().iterator();
			        while (mapIterator.hasNext()) {
			            Map.Entry<String, String[]> pair = (Entry<String, String[]>)mapIterator.next();
			            this.setProperty("HTTP."+pair.getKey(),pair.getValue()[0].toString());

			        //    mapIterator.remove();
			        }
		              
			}

	}

	
public void copySOAPHTTPPropertyFrom(HttpServletRequest webreq, BSCMessageProperties p) {
		
		
		switch (p) {
		
		case HTTP_CONTENT_TYPE:
			if (webreq.getHeader("Content-Type") != null) {
				setProperty( BSCMessageProperties.HTTP_CONTENT_TYPE, webreq.getHeader("Content-Type").toString());
			}
			break;
					
		case HTTP_REQUEST_URI:
			if (webreq.getRequestURI() != null) {
				setProperty( BSCMessageProperties.HTTP_REQUEST_URI, webreq.getRequestURI().toString());
			}
			break;

		}

	}

	
	public void clearException() {
		this.exception=null;
	}
	
	public void generateEvent(String eventName ) {
		
		

		BSCService	eventService	= null;

		
		if (!eventDisabled) {
				
			try {
				
				if (messageOwner.getComponentType() == BSCComponentType.LISTENER ) {
					eventService=	((BSCListenerBase)messageOwner).getServiceContainer().getService(eventServiceName);
				} else if (messageOwner.getComponentType() == BSCComponentType.CONTROLLER  ) {
					eventService=	((BSCControllerBase)messageOwner).getServiceContainer().getService(eventServiceName);
				}
				
				this.setProperty(BSCMessageProperties.EVENT_NAME, eventName);
				
				try{
				
					if (eventService!=null) eventService.execute(this);
				
				} catch (BSCServiceException e) {
					
					if(messageOwner != null ){
						messageOwner.getLogger().error("Failed to generate an event {} ", eventName);
					}	
				}
				
				
			} catch (BSCServiceException se) {
				messageOwner.getLogger().error("Failed to execute the script {}", se);
				
			} finally {
					
				if(eventService != null) {
	
					try {
						
						if (messageOwner.getComponentType() == BSCComponentType.LISTENER ) {
							((BSCListenerBase)messageOwner).getServiceContainer().releaseService(eventServiceName, eventService);
						} else if (messageOwner.getComponentType() == BSCComponentType.CONTROLLER  ) {
							((BSCControllerBase)messageOwner).getServiceContainer().releaseService(eventServiceName, eventService);
						}
						
					} catch (  BSCServiceException e) {
						messageOwner.getLogger().error("Failed to release the service {} to the pool. {}", eventServiceName, e);
					}
				}
				
				eventDisabled=false;
			}
			
			
		}
		
		
	}
	
	public Map<String, Object> getProperties() {
		return this.properties;
	}
	public void close() {
		this.exception=null;
	}

	public BSCMessage loadMessage(BSCVFSMessage vfsMessage) throws BSCMessageException{
		
		try {
			this.reset();	
			Map<String, Object> vfsMessageProperties = BSCPropertyHelper.getPropertiesStartingWith(vfsMessage.getProperties(),
					"VFS.", false);
			this.addProperties(vfsMessageProperties, true);		
			
			this.write(vfsMessage.toByteArray());
		}catch (IOException e) {
			throw (new BSCMessageException(messageOwner, e, BSCErrorLevel.CRITICAL,"Failed to create the BSCMessage from the byte array", this));
		}
		return this;		
	}

	public String getTagValue(String regex, int occurence, String defaultValue) throws BSCMessageException {
		
		final Pattern pattern = Pattern.compile(regex, Pattern.DOTALL);
		String matchedValue = null;
		Matcher matcher=null;
		
		matcher = pattern.matcher(getStringMessage());
		
		for (int i=1; i<=occurence; i++) {
			matcher.find();
		}
		try {
			matchedValue =matcher.group(1); 
		} catch (IllegalStateException e) {
			messageOwner.getLogger().error("No match found - regex {} occurence {}", regex, occurence);
		}
		if (matchedValue==null ) {
			matchedValue=defaultValue;
		}
		
		return matchedValue;
	}
	
	
	public String generateCorrelationId(){
		
		DateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        formatter.setTimeZone(TimeZone.getTimeZone("UTC"));
        
        String dateString = formatter.format(new Date());
        return dateString + String.format("%02d", (int)Math.floor(Math.random() * 99 ) );
    }


	
	public  String getTimestamp(String timeZone, String format) {
		
		if(timeZone==null || timeZone.equals(""))
			timeZone="UTC";
		if(format==null || format.equals(""))	
			format="yyyy-MM-dd HH:mm:ss.SSS";
		
		DateFormat formatter = new SimpleDateFormat(format);
        formatter.setTimeZone(TimeZone.getTimeZone(timeZone));
        
        String dateString = formatter.format(new Date());
        return dateString;
	}
	
	public void logDebug(String logMessage) {
		if(messageOwner.getLogger().isDebugEnabled())
			messageOwner.getLogger().debug(logMessage);
	}
	
	public void logInfo(String logMessage) {
		messageOwner.getLogger().info(logMessage);
	}
	
	public void logWarn(String logMessage) {
		messageOwner.getLogger().warn(logMessage);
	}
	
	public void logError(String logMessage) {
		messageOwner.getLogger().error(logMessage);
	}

	public String getScriptFnBody() {
		return scriptFnBody;
	}

	public void setScriptFnBody(String scriptFnBody) {
		this.scriptFnBody = scriptFnBody;
	}

	public String getScriptFnName() {
		return scriptFnName;
	}

	public void setScriptFnName(String scriptFnName) {
		this.scriptFnName = scriptFnName;
	}

	public Object getScriptFnResult() {
		return scriptFnResult;
	}

	public void setScriptFnResult(Object scriptFnResult) {
		this.scriptFnResult = scriptFnResult;
	}
		
	public String xsltTransformation(String xml, String xsltFile) throws TransformerException {
		Source inputSource = new StreamSource(new StringReader(xml));
	    
	    TransformerFactory factory = TransformerFactory.newInstance();
	    Source stylesheetSource = new StreamSource(new File(xsltFile).getAbsoluteFile());
	    Transformer transformer = factory.newTransformer(stylesheetSource);
	    
	    Writer outputWriter = new StringWriter();
	    Result outputResult = new StreamResult(outputWriter);
	    transformer.transform(inputSource, outputResult);
	    String outputString = outputWriter.toString();
		return outputString;		
	}

}
