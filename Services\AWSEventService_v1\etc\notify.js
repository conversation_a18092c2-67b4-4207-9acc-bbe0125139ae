function initializeNotificationProps(env) {

	//MES notification config
	if (message.getProperty("JMS.USER.APP_Application") != null
			&& message.getProperty("JMS.USER.EVENT_DestinationApp") != null) {
		if (message.getProperty("JMS.USER.APP_Application").equals("MES")
				|| message.getProperty("JMS.USER.EVENT_DestinationApp").equals(
						"MES")) {

			if (message.getProperty("JMS.USER.EVENT_SourceApp").equals("M590")
					|| (message.getProperty("JMS.USER.EVENT_IndexTag") != null && message
							.getProperty("JMS.USER.EVENT_IndexTag").equals(
									"M590"))) {
				if (env.equals("PRD")) {
					SP("error.notification.email.to", "<EMAIL>");
				} else {

					SP("error.notification.email.to",
							"<EMAIL>");
				}
				if (message.getProperty("JMS.USER.EVENT_Object").equals(
						"MESReplyListener")
						|| message.getProperty("JMS.USER.EVENT_StackTrace")
								.contains("ErrorCode: 400") || message.getProperty("JMS.USER.EVENT_StackTrace")
								.contains("ErrorCode: 500")) {
					SP("error.notification.email.cc", "");
				}
			}
		}
	}

	//RapidResponse notification config
	if (message.getProperty("JMS.USER.EVENT_DestinationApp") != null) {
		if (message.getProperty("JMS.USER.EVENT_DestinationApp").toLowerCase()
				.equals("rapidresponse")
				|| message.getProperty("JMS.USER.EVENT_SourceApp")
						.toLowerCase().equals("rapidresponse")) {
			SP("error.notification.email.cc", "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>");
		}
	}

	//TracII notification config
	if (message.getProperty("JMS.USER.EVENT_DestinationApp") != null) {
		if (message.getProperty("JMS.USER.EVENT_DestinationApp").toLowerCase()
				.equals("tracii")) {
			SP("error.notification.email.to", "<EMAIL>");
		}
	}

	//MapleGrove notification config
	if (message.getProperty("JMS.USER.EVENT_MessageType") != null) {
		if (message.getProperty("JMS.USER.EVENT_MessageType").toUpperCase()
				.equals("MAPLEGROVEACTIVE")
				|| message.getProperty("JMS.USER.EVENT_MessageType")
						.toUpperCase().equals("MAPLEGROVETERM")) {
			SP("error.notification.email.to", "<EMAIL>");
		}
	}

	//Manulife notification config
	if (message.getProperty("JMS.USER.EVENT_MessageType") != null
			&& message.getProperty("JMS.USER.EVENT_SourceApp") != null) {
		if (message.getProperty("JMS.USER.EVENT_MessageType").toUpperCase()
				.equals("Manulife")
				|| message.getProperty("JMS.USER.EVENT_SourceApp")
						.toUpperCase().equals("Manulife")) {
			if (!env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.cc", "<EMAIL>");
				SP("error.notification.email.to", "<EMAIL>");
			} else {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			}
		}
	}

	//Exactus notification config
	if (message.getProperty("JMS.USER.EVENT_MessageType") != null) {
		if (message.getProperty("JMS.USER.EVENT_MessageType").toUpperCase()
				.equals("EXACTUS")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc",
						"<EMAIL>");
			} else {
				SP("error.notification.email.cc", "<EMAIL>");
			}
		}
	}

	//Anaqua notification config
	if (message.getProperty("JMS.USER.EVENT_MessageType") != null) {
		if (message.getProperty("JMS.USER.EVENT_MessageType").toUpperCase()
				.equals("ANAQUA")
				|| message.getProperty("JMS.USER.EVENT_MessageType")
						.toUpperCase().equals("AQ")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc",
						"<EMAIL>");
			} else {
				SP("error.notification.email.cc", "<EMAIL>");
			}
		}
	}

	//Sunlife notification config
	if (message.getProperty("JMS.USER.EVENT_MessageType") != null) {
		if (message.getProperty("JMS.USER.EVENT_MessageType").toUpperCase()
				.equals("SUNLIFE")
				|| message.getProperty("JMS.USER.EVENT_MessageType")
						.toUpperCase().equals("SL")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc",
						"<EMAIL>");
			} else {
				SP("error.notification.email.cc", "<EMAIL>");
			}
		}
	}

	//sfdc notification config
	if (message.getProperty("JMS.USER.EVENT_DestinationApp") != null) {
		if (message.getProperty("JMS.USER.EVENT_DestinationApp").toLowerCase()
				.equals("sfdc")
				|| message.getProperty("JMS.USER.EVENT_SourceApp")
						.toLowerCase().equals("sfdc")) {
			if (env.equals("PRD")) {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "");
			}
		}
	}

	//GMDT notification config
	if (message.getProperty("JMS.USER.APP_Application") != null
			&& message.getProperty("JMS.USER.EVENT_SourceApp") != null
			&& message.getProperty("JMS.USER.EVENT_DestinationApp") != null) {
		if (message.getProperty("JMS.USER.APP_Application").toUpperCase()
				.equals("GMDT")
				|| message.getProperty("JMS.USER.EVENT_SourceApp")
						.toUpperCase().equals("GMDT")
				|| message.getProperty("JMS.USER.EVENT_DestinationApp")
						.toUpperCase().equals("GMDT")
				|| message.getProperty("JMS.USER.EVENT_MessageType")
						.toUpperCase().equals("TRAVELTAG")) {
			if (!env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.cc", "<EMAIL>");
				SP("error.notification.email.to",
						"<EMAIL>");
			} else {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			}
		}
	}

	//BYOD OneTrust notification config
	if (message.getProperty("JMS.USER.EVENT_MessageType") != null) {
		if (message.getProperty("JMS.USER.EVENT_MessageType").toUpperCase()
				.equals("DEVICEACTIVATION")
				|| message.getProperty("JMS.USER.EVENT_MessageType")
						.toUpperCase().equals("ICMPATIENTSTATUS")
				|| message.getProperty("JMS.USER.EVENT_MessageType")
						.toUpperCase().equals("CONSENT_CHECK")) {
			if (!env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			} else {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			}
		}
	}

	//Kronos notification config
	if (message.getProperty("JMS.USER.EVENT_MessageType") != null) {
		if (message.getProperty("JMS.USER.EVENT_MessageType").toUpperCase()
				.equals("PAYROLLFILE")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to", "<EMAIL>");
			} else {
				SP("error.notification.email.to", "<EMAIL>");
			}
		}
	}
	//BTS notification config
	if (message.getProperty("JMS.USER.EVENT_MessageType") != null) {
		if (message.getProperty("JMS.USER.EVENT_MessageType").toUpperCase()
				.equals("Z3PL_RESDLVRY_DELVRY07_KR_BTS")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			} else {
				SP("error.notification.email.to", "<EMAIL>");
			}
		}
	}
	//Consent V3 notification config
	if (message.getProperty("JMS.USER.EVENT_MessageType") != null) {
		if (message.getProperty("JMS.USER.EVENT_MessageType").toUpperCase()
				.equals("Consent_v3")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			} else {
				SP("error.notification.email.to", "<EMAIL>");
			}
		}
	}
	//Russia Track and Trace notification config
	if (message.getProperty("JMS.USER.EVENT_MessageType") != null) {
		if (message.getProperty("JMS.USER.EVENT_MessageType").toUpperCase()
				.equals("ZTNTDLVY_ZDELVRY07_RU_TNT")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			} else {
				SP("error.notification.email.to", "<EMAIL>");
			}
		}
	}

	if (message.getProperty("JMS.USER.EVENT_MessageType") != null) {
		if (message.getProperty("JMS.USER.EVENT_MessageType").toUpperCase()
				.equals("ZTNTSHPLIST_ZSHPMNT06_RU_TNT")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			} else {
				SP("error.notification.email.to", "<EMAIL>");
			}
		}
	}

	//ChinaDMS notification config
	if (message.getProperty("JMS.USER.EVENT_MessageType") != null) {
		if (message.getProperty("JMS.USER.EVENT_MessageType").toUpperCase()
				.equals("ZSD_ORD_CREATE_REPLY")) {
			if (env.toUpperCase().equals("PRD")) {
				SP(
						"error.notification.email.to",
						"<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			} else {
				SP(
						"error.notification.email.to",
						"<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>");
			}
		}
	}

	//Global Guardian error notification config
	if (message.getProperty("JMS.USER.EVENT_MessageType") != null) {
		if (message.getProperty("JMS.USER.EVENT_MessageType").toUpperCase()
				.equals("GLOBALGUARDIAN")
				|| message.getProperty("JMS.USER.EVENT_MessageType")
						.toUpperCase().equals("GG")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc",
						"<EMAIL>");
			} else {
				SP("error.notification.email.cc", "<EMAIL>");
			}
		}
	}

	//Falcon Soft LATAM error notification config
	if (message.getProperty("JMS.USER.EVENT_MessageType") != null) {
		if (message.getProperty("JMS.USER.EVENT_MessageType").toUpperCase()
				.equals("FALCONSOFT")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			} else {
				SP("error.notification.email.cc", "<EMAIL>");
			}
		}
	}

	//Concur BSCFilePubSub error notification config
	if (message.getProperty("JMS.USER.EVENT_MessageType") != null) {
		if (message.getProperty("JMS.USER.EVENT_MessageType").toUpperCase()
				.equals("ATTENDEE_DETAIL")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			} else {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			}
		}
	}

	//IQVIA error notification config
	if (message.getProperty("JMS.USER.EVENT_DestinationApp") != null) {
		if (message.getProperty("JMS.USER.EVENT_DestinationApp").toUpperCase()
				.equals("IQVIA")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			} else {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			}
		}
	}

	//Kronos Ph2 notification config
	if (message.getProperty("JMS.USER.EVENT_SourceApp") != null) {
		if (message.getProperty("JMS.USER.EVENT_SourceApp").toUpperCase()
				.equals("KRONOS")) {
			if (message.getProperty("JMS.USER.EVENT_MessageType") != null) {
				if (message.getProperty("JMS.USER.EVENT_MessageType").equals(
						"Dorado_HR")
						|| message.getProperty("JMS.USER.EVENT_MessageType")
								.equals("Dorado")
						|| message.getProperty("JMS.USER.EVENT_MessageType")
								.equals("SanJuan")
						|| message.getProperty("JMS.USER.EVENT_MessageType")
								.equals("Misc")
						|| message.getProperty("JMS.USER.EVENT_MessageType")
								.equals("Excelity")
						|| message.getProperty("JMS.USER.EVENT_MessageType")
								.equals("MapleGrove")
						|| message.getProperty("JMS.USER.EVENT_MessageType")
								.equals("Quincy")
						|| message.getProperty("JMS.USER.EVENT_MessageType")
								.equals("Finance")
						|| message.getProperty("JMS.USER.EVENT_MessageType")
								.equals("US")
						|| message.getProperty("JMS.USER.EVENT_MessageType")
								.equals("ADP")
						|| message.getProperty("JMS.USER.EVENT_MessageType")
								.equals("Canada")
						|| message.getProperty("JMS.USER.EVENT_MessageType")
								.equals("Spencer")
						|| message.getProperty("JMS.USER.EVENT_MessageType")
								.equals("EDC")
						|| message.getProperty("JMS.USER.EVENT_MessageType")
								.equals("CostaRica")
						|| message.getProperty("JMS.USER.EVENT_MessageType")
								.equals("Penang")
						|| message.getProperty("JMS.USER.EVENT_MessageType")
								.equals("TIM")
						|| message.getProperty("JMS.USER.EVENT_MessageType")
								.equals("Punch_Export")
						|| message.getProperty("JMS.USER.EVENT_MessageType")
								.equals("Galway")) {
					if (env.toUpperCase().equals("PRD")) {
						SP("error.notification.email.to",
								"<EMAIL>");
						SP("error.notification.email.cc", "<EMAIL>");
					} else {
						SP("error.notification.email.to",
								"<EMAIL>");
						SP("error.notification.email.cc", "<EMAIL>");
					}
				}
			}
		}
	}

	//Malaysia_eInvoice(EDICOM)
	if (message.getProperty("JMS.USER.EVENT_SourceApp") != null) {
		if (message.getProperty("JMS.USER.EVENT_SourceApp").toUpperCase()
				.equals("SAP")) {
			if (message.getProperty("JMS.USER.EVENT_MessageType") != null) {
				if (message.getProperty("JMS.USER.EVENT_MessageType").equals(
						"Malaysia_eInvoice")) {
					if (env.toUpperCase().equals("PRD")) {
						SP("error.notification.email.to",
								"<EMAIL>");
						SP("error.notification.email.cc", "<EMAIL>");
					} else {
						SP("error.notification.email.to",
								"<EMAIL>");
						SP("error.notification.email.cc", "<EMAIL>");
					}
				}
			}
		}
	}

	//APACDatahub notification config
	if (message.getProperty("JMS.USER.EVENT_SourceApp") != null
			&& message.getProperty("JMS.USER.EVENT_DestinationApp") != null) {
		if (message.getProperty("JMS.USER.EVENT_SourceApp").toUpperCase()
				.equals("APACDATAHUB")
				|| message.getProperty("JMS.USER.EVENT_DestinationApp")
						.toUpperCase().equals("APACDATAHUB")) {
			if (!env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to", "<EMAIL>");
			} else {
				SP("error.notification.email.to", "<EMAIL>");
			}
		}
	}

	//LDS Integration
	if (message.getProperty("JMS.USER.EVENT_SourceApp") != null) {
		if (message.getProperty("JMS.USER.EVENT_SourceApp").toUpperCase()
				.equals("LDS")) {
			if (message.getProperty("JMS.USER.EVENT_MessageType") != null) {
				if (message.getProperty("JMS.USER.EVENT_MessageType").equals(
						"LBLData")) {
					if (env.toUpperCase().equals("PRD")) {
						SP("error.notification.email.to", "<EMAIL>");
					} else {
						SP("error.notification.email.to", "<EMAIL>");
					}
				}
			}
			//KAIROS error notification config
			if (message.getProperty("JMS.USER.EVENT_DestinationApp") != null) {
				if (message.getProperty("JMS.USER.EVENT_DestinationApp")
						.toUpperCase().equals("KAIROS")) {
					if (env.toUpperCase().equals("PRD")) {
						SP("error.notification.email.to", "<EMAIL>");
					} else {
						SP("error.notification.email.to", "<EMAIL>");
					}
				}
			}
		}
	}

	//ChinaDMS error notification config

	if (message.getProperty("JMS.USER.EVENT_DestinationApp") != null) {
		if (message.getProperty("JMS.USER.EVENT_SourceApp").toUpperCase()
				.equals("ChinaDMS")
				&& (message.getProperty("JMS.USER.EVENT_DestinationApp")
						.toUpperCase().equals("SAP"))
				|| (message.getProperty("JMS.USER.EVENT_DestinationApp")
						.toUpperCase().equals("ChinaDMS"))) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to", "<EMAIL>");
				SP(
						"error.notification.email.to",
						"<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>");
			} else {
				SP("error.notification.email.to", "<EMAIL>");
				SP(
						"error.notification.email.to",
						"<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>");
			}
		}
	}

	//BTS Material MAster notification config
	if (message.getProperty("JMS.USER.EVENT_DestinationApp") != null
			&& message.getProperty("JMS.USER.EVENT_MessageType") != null) {
		if (message.getProperty("JMS.USER.EVENT_DestinationApp").toUpperCase()
				.equals("BTS")
				|| message.getProperty("JMS.USER.EVENT_MessageType")
						.toUpperCase().equals("Material_Master")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			} else {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.to", "<EMAIL>");

			}
		}
	}

	//GSMS notification config
	if (message.getProperty("JMS.USER.EVENT_SourceApp") != null
			&& message.getProperty("JMS.USER.EVENT_MessageType") != null) {
		if (message.getProperty("JMS.USER.EVENT_SourceApp").toUpperCase()
				.equals("PowerAutomate")
				|| message.getProperty("JMS.USER.EVENT_MessageType")
						.toUpperCase().equals("FSE_Item")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.cc", "<EMAIL>");
			} else {
				//			SP("error.notification.email.to","<EMAIL>");
				SP("error.notification.email.to", "<EMAIL>");
			}
		}
	}

	// Marketing Cloud notification config
	if (message.getProperty("JMS.USER.EVENT_DestinationApp") != null
			&& message.getProperty("JMS.USER.EVENT_MessageType") != null) {

		var destinationApp = message.getProperty(
				"JMS.USER.EVENT_DestinationApp").toUpperCase();
		var messageType = message.getProperty("JMS.USER.EVENT_MessageType")
				.toUpperCase();

		if (destinationApp.equals("EI")
				&& messageType.equals("MARKETINGCLOUD_CAMPAIGN")
				|| messageType.equals("MARKETINGCLOUD_CONGRESS")) {

			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.cc", "<EMAIL>");
			} else {
				//          SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.to", "<EMAIL>");

			}
		}
	}

	//Natdatatrans notification config
	if (message.getProperty("JMS.USER.EVENT_DestinationApp") != null
			&& message.getProperty("JMS.USER.EVENT_MessageType") != null) {
		if (message.getProperty("JMS.USER.EVENT_DestinationApp").toUpperCase()
				.equals("SALESFORCE")
				|| message.getProperty("JMS.USER.EVENT_MessageType")
						.toUpperCase().equals("EndoUserAlignment")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.cc", "<EMAIL>");
			} else {
				//			SP("error.notification.email.to","<EMAIL>");
				SP("error.notification.email.to", "<EMAIL>");

			}
		}
	}

	//WWSDB notification config
	if (message.getProperty("JMS.USER.EVENT_DestinationApp") != null
			&& message.getProperty("JMS.USER.EVENT_MessageType") != null) {
		if (message.getProperty("JMS.USER.EVENT_DestinationApp").toUpperCase()
				.equals("EI")
				&& message.getProperty("JMS.USER.EVENT_MessageType")
						.toUpperCase().equals("Daily_Sales")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.cc", "<EMAIL>");
			} else {
				SP("error.notification.email.to", "<EMAIL>");

				//			SP("error.notification.email.to","<EMAIL>");

			}
		}
	}

	//Hawkeye notification config
	if (message.getProperty("JMS.USER.EVENT_DestinationApp") != null
			&& message.getProperty("JMS.USER.EVENT_MessageType") != null) {
		if (message.getProperty("JMS.USER.EVENT_DestinationApp").toUpperCase()
				.equals("HawkEye")
				|| message.getProperty("JMS.USER.EVENT_MessageType")
						.toUpperCase().equals("Patient_Device_Case")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.cc", "<EMAIL>");
			} else {
				//		SP("error.notification.email.to","<EMAIL>");
				SP("error.notification.email.to", "<EMAIL>");

			}
		}
	}

	//UDP notification config
	if (message.getProperty("JMS.USER.EVENT_SourceApp") != null
			&& message.getProperty("JMS.USER.EVENT_MessageType") != null) {
		if (message.getProperty("JMS.USER.EVENT_SourceApp").toUpperCase()
				.equals("UDP")
				|| message.getProperty("JMS.USER.EVENT_MessageType").equals(
						"DeviceTracking")) {

			if (env.toUpperCase().equals("PRD")) {
				SP(
						"error.notification.email.to",
						"<EMAIL>,<EMAIL>,<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			} else {
				SP(
						"error.notification.email.to",
						"<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>");
			}

		}
	}

	//Facele
	if (message.getProperty("JMS.USER.EVENT_SourceApp") != null) {
		if (message.getProperty("JMS.USER.EVENT_SourceApp").equals("Facele")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			} else {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			}
		}
	}

	//CaptivateIQ Integration -GSR  notification config
	if (message.getProperty("JMS.USER.EVENT_SourceApp") != null
			&& message.getProperty("JMS.USER.EVENT_MessageType") != null) {
		if (message.getProperty("JMS.USER.EVENT_SourceApp").toUpperCase()
				.equals("GSR")
				&& message.getProperty("JMS.USER.EVENT_MessageType")
						.toUpperCase().equals("EUSALES")
				|| message.getProperty("JMS.USER.EVENT_MessageType")
						.toUpperCase().equals("ANZSALES")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			} else {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			}
		}
	}

	//CaptivateIQ Integration -Hyperion  notification config
	if (message.getProperty("JMS.USER.EVENT_SourceApp") != null
			&& message.getProperty("JMS.USER.EVENT_MessageType") != null) {
		if (message.getProperty("JMS.USER.EVENT_SourceApp").toUpperCase()
				.equals("HYPERION")
				&& message.getProperty("JMS.USER.EVENT_MessageType")
						.toUpperCase().equals("EUHYPERION")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			} else {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			}
		}
	}

	//CaptivateIQ Integration -SF  notification config
	if (message.getProperty("JMS.USER.EVENT_SourceApp") != null
			&& message.getProperty("JMS.USER.EVENT_MessageType") != null) {
		if (message.getProperty("JMS.USER.EVENT_SourceApp").toUpperCase()
				.equals("SF")
				&& message.getProperty("JMS.USER.EVENT_MessageType")
						.toUpperCase().equals("ANZSF")
				|| message.getProperty("JMS.USER.EVENT_MessageType")
						.toUpperCase().equals("EUSF")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			} else {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			}
		}
	}

	//ReportGCC Integration  notification config
	if (message.getProperty("JMS.USER.EVENT_SourceApp") != null
			&& message.getProperty("JMS.USER.EVENT_MessageType") != null) {
		if (message.getProperty("JMS.USER.EVENT_SourceApp").toUpperCase()
				.equals("ESKER")
				&& message.getProperty("JMS.USER.EVENT_MessageType")
						.toUpperCase().equals("REPORTGCC")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to",
						"<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			} else {
				SP("error.notification.email.to",
						"<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			}
		}
	}

	//Voya Integration  notification config
	if (message.getProperty("JMS.USER.EVENT_SourceApp") != null
			&& message.getProperty("JMS.USER.EVENT_MessageType") != null) {
		if (message.getProperty("JMS.USER.EVENT_SourceApp").toUpperCase()
				.equals("ECP")
				&& message.getProperty("JMS.USER.EVENT_MessageType").equals(
						"ECPPayrollBenefits")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			} else {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			}
		}
	}

	//SalesRep notification config
	if (message.getProperty("JMS.USER.EVENT_MessageType") != null) {
		if (message.getProperty("JMS.USER.EVENT_MessageType").toUpperCase()
				.equals("GBSIA")
				|| message.getProperty("JMS.USER.EVENT_MessageType")
						.toUpperCase().equals("GBS")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			} else {
				SP("error.notification.email.to", "<EMAIL>");
			}
		}
	}

	// LegalEase and Norton Integration Notification Config 
	if (message.getProperty("JMS.USER.EVENT_SourceApp") != null
			&& message.getProperty("JMS.USER.EVENT_MessageType") != null) {
		if (message.getProperty("JMS.USER.EVENT_SourceApp").toUpperCase()
				.equals("ECP")) {
			if (message.getProperty("JMS.USER.EVENT_MessageType").equals(
					"RECONCILIATION")
					|| message.getProperty("JMS.USER.EVENT_MessageType")
							.equals("REMITTANCE")) {
				if (env.toUpperCase().equals("PRD")) {
					SP("error.notification.email.to", "<EMAIL>");
					SP("error.notification.email.cc", "<EMAIL>");
				} else {
					SP("error.notification.email.to", "<EMAIL>");
					SP("error.notification.email.cc", "<EMAIL>");
				}
			}
		}
	}
	
	//Degreed notification config
	if(message.getProperty("JMS.USER.EVENT_MessageType")!=null){
		if (message.getProperty("JMS.USER.EVENT_MessageType").toUpperCase().equals("DEGREED")
				|| message.getProperty("JMS.USER.EVENT_MessageType").toUpperCase().equals("DG")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to","<EMAIL>");
			} else {
				SP("error.notification.email.to","<EMAIL>");
			}
		}
	}

	//IQVIA_HCP error notification config
	if(message.getProperty("JMS.USER.EVENT_DestinationApp")!=null){
		if (message.getProperty("JMS.USER.EVENT_DestinationApp").toUpperCase().equals("IQVIA_HCP")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to","<EMAIL>");
				SP("error.notification.email.cc","<EMAIL>");
			} else {	
				SP("error.notification.email.cc","<EMAIL>");
			}
		}
	}
	
	//CidUdpCustomerCheck notification config
	if(message.getProperty("JMS.USER.EVENT_MessageType")!=null){
		if (message.getProperty("JMS.USER.EVENT_MessageType").toUpperCase().equals("CUSTOMERVERIFICATION")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to","<EMAIL>");
			} else {
				SP("error.notification.email.to","<EMAIL>");
			}
		}
	}
	
	//Cumulocity - Avvigo Prime notification config
	if (message.getProperty("JMS.USER.EVENT_DestinationApp") != null
			&& message.getProperty("JMS.USER.EVENT_MessageType") != null) {
		if (message.getProperty("JMS.USER.EVENT_DestinationApp").toUpperCase()
				.equals("CUMULOCITY")
				&& message.getProperty("JMS.USER.EVENT_MessageType")
						.toUpperCase().equals("ICDEVICE")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			} else {
				SP("error.notification.email.to", "<EMAIL>");
			}
		}
	}
	
	//Motivosity notification config
	if(message.getProperty("JMS.USER.EVENT_MessageType")!=null){
		if (message.getProperty("JMS.USER.EVENT_MessageType").toUpperCase().equals("MOTIVOSITY")
				|| message.getProperty("JMS.USER.EVENT_MessageType").toUpperCase().equals("MV")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to","<EMAIL>");
			} else {
				SP("error.notification.email.to","<EMAIL>");
			}
		}
	}
	
	//BMRAM, Ariba notification config
    if(message.getProperty("JMS.USER.EVENT_MessageType")!=null){
        if (message.getProperty("JMS.USER.APP_Application").equals("BMRAM")||message.getProperty("JMS.USER.EVENT_MessageType").equals("ORDERS_ORDERS05_BMRAM")) {
            if (env.toUpperCase().equals("PRD")) {
                   SP("error.notification.email.to","<EMAIL>");
                   SP("error.notification.email.cc","<EMAIL>,<EMAIL>");
            } else {
                   SP("error.notification.email.to","<EMAIL>");
                   SP("error.notification.email.cc","<EMAIL>,<EMAIL>");
            }
         }
    }
    
  //Concur error notification config
	if (message.getProperty("JMS.USER.EVENT_MessageType") != null) {
		if (message.getProperty("JMS.USER.EVENT_MessageType").toUpperCase()
				.equals("ATTENDEEDETAIL") || message.getProperty("JMS.USER.EVENT_MessageType").toUpperCase()
				.equals("SAEREG") ) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			} else {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			}
		}
	}
	
	//Windchill notification config
	if(message.getProperty("JMS.USER.EVENT_DestinationApp")!=null && message.getProperty("JMS.USER.EVENT_MessageType")!=null ){
		if (message.getProperty("JMS.USER.EVENT_DestinationApp").toUpperCase().equals("AIFARM")
			|| message.getProperty("JMS.USER.EVENT_MessageType").toUpperCase().equals("DOCUMENT")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to","<EMAIL>");
				SP("error.notification.email.cc","<EMAIL>");
			} else {	    
				SP("error.notification.email.to","<EMAIL>");
				SP("error.notification.email.cc","<EMAIL>");
			}
		}
	}
	
	//CID notification config
	if(message.getProperty("JMS.USER.EVENT_DestinationApp")!=null && message.getProperty("JMS.USER.EVENT_MessageType")!=null ){
		if (message.getProperty("JMS.USER.EVENT_DestinationApp").toUpperCase().equals("CDL")
			|| message.getProperty("JMS.USER.EVENT_MessageType").toUpperCase().equals("User")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to","<EMAIL>");
				SP("error.notification.email.cc","<EMAIL>");
			} else {	    
				SP("error.notification.email.cc","<EMAIL>");
			}
		}
	}
	
	
	//Benefex error notification config
	if (message.getProperty("JMS.USER.EVENT_MessageType") != null) {
		if (message.getProperty("JMS.USER.EVENT_MessageType").toUpperCase()
				.equals("BENEFEX") || message.getProperty("JMS.USER.EVENT_MessageType").toUpperCase()
				.equals("HRISBENEFEX") ) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			} else {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			}
		}
	}
	
	// GSD-Axonics notification config
	if (message.getProperty("JMS.USER.EVENT_DestinationApp") != null
			&& message.getProperty("JMS.USER.EVENT_MessageType") != null
			&& message.getProperty("JMS.USER.EVENT_SourceApp") != null) {
		if (message.getProperty("JMS.USER.EVENT_DestinationApp").toUpperCase()
				.equals("AXONICS")
				&& message.getProperty("JMS.USER.EVENT_MessageType").equals(
						"Customers")
				||	message.getProperty("JMS.USER.EVENT_MessageType").equals(
				"Invoices")
				&& message.getProperty("JMS.USER.EVENT_SourceApp")
						.equals("GSD")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to",
						"<EMAIL>,<EMAIL>,<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			} else {
				SP("error.notification.email.to", "<EMAIL>");
			}
		}
	}
	
	//Concur-UDP notification config for Tableau messageType
	if (message.getProperty("JMS.USER.EVENT_MessageType") != null) {
		if (message.getProperty("JMS.USER.EVENT_MessageType").toUpperCase()
				.equals("TABLEAU")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>,<EMAIL>");
			} else {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "<EMAIL>");
			}
		}
	}

	//Autostore notification config
	if (message.getProperty("JMS.USER.EVENT_DestinationApp") != null) {
		if (message.getProperty("JMS.USER.EVENT_DestinationApp").toLowerCase()
				.equals("autostore")) {
			if (env.equals("PRD")) {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "");
			}
		}
	}
	
	//Vanderlande notification config
	if (message.getProperty("JMS.USER.EVENT_DestinationApp") != null) {
		if (message.getProperty("JMS.USER.EVENT_DestinationApp").toLowerCase()
				.equals("vanderlande")) {
			if (env.equals("PRD")) {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "");
			}
		}
	}
	
	//PackSize notification config
	if (message.getProperty("JMS.USER.EVENT_DestinationApp") != null) {
		if (message.getProperty("JMS.USER.EVENT_DestinationApp").toLowerCase()
				.equals("packsize")) {
			if (env.equals("PRD")) {
				SP("error.notification.email.to", "<EMAIL>");
				SP("error.notification.email.cc", "");
			}
		}
	}
	
	//Windchill AIFArm notification config
	if(message.getProperty("JMS.USER.EVENT_DestinationApp")!=null && message.getProperty("JMS.USER.EVENT_MessageType")!=null ){
		if (message.getProperty("JMS.USER.EVENT_DestinationApp").toUpperCase().equals("AIFARM")
			&& message.getProperty("JMS.USER.EVENT_MessageType").toUpperCase().equals("DOCUMENT")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to","<EMAIL>");
				SP("error.notification.email.cc","<EMAIL>");
			} else {	    
				SP("error.notification.email.to","<EMAIL>");
				SP("error.notification.email.cc","<EMAIL>");
			}
		}
	}
	
	//GMDT-PHIRepo notification config
	if(message.getProperty("JMS.USER.EVENT_DestinationApp")!=null && message.getProperty("JMS.USER.EVENT_MessageType")!=null ){
		if (message.getProperty("JMS.USER.EVENT_DestinationApp").toUpperCase().equals("PHIREPOSITORY")
			&& message.getProperty("JMS.USER.EVENT_MessageType").equals("DragonCustomerDemographic")) {
			if (env.toUpperCase().equals("PRD")) {
				SP("error.notification.email.to","<EMAIL>");
				SP("error.notification.email.cc","<EMAIL>");
			} else {	    
				SP("error.notification.email.to","<EMAIL>");
			}
		}
	}
	
	//TrackerID 1772_ECP Costa Rica Payroll Integration
	if (message.getProperty("JMS.USER.EVENT_SourceApp") != null) {
	    if (message.getProperty("JMS.USER.Event_DestinationApp").equals("SanJoseBank") && 
	        message.getProperty("JMS.USER.EVENT_MessageType").equals("PAIN")) {
	        if (!env.toUpperCase().equals("PRD")) {
	            SP("error.notification.email.to", "<EMAIL>");
	        } else {
	            SP("error.notification.email.to", "<EMAIL>");
	            SP("error.notification.email.cc", "<EMAIL>,<EMAIL>,<EMAIL>");
	        }
	    }
	}  
	 
	//TrackerID 1772_ECP Costa Rica Payroll Integration  
	if (message.getProperty("JMS.USER.EVENT_SourceApp") != null) {
		if (message.getProperty("JMS.USER.EVENT_SourceApp").toUpperCase().equals("ECP") && 
	             message.getProperty("JMS.USER.EVENT_MessageType").equals("CR_PAIN")) {
	        if (!env.toUpperCase().equals("PRD")) {
	            SP("error.notification.email.to", "<EMAIL>");
	        } else {
	            SP("error.notification.email.to", "<EMAIL>");
	            SP("error.notification.email.cc", "<EMAIL>,<EMAIL>,<EMAIL>");
	        }
	    }
	}
}