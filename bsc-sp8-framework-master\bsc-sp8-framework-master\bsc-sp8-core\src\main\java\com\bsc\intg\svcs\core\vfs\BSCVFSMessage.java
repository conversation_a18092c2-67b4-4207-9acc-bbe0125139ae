package com.bsc.intg.svcs.core.vfs;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.TimeZone;

public class BSCVFSMessage extends ByteArrayOutputStream {

	Map<String, Object> properties = new LinkedHashMap<String, Object>();
	BSCVFSObjectInfo file = null;
	String fileName = null;

	public BSCVFSObjectInfo getFile() {
		return file;

	}

	public void addFile(BSCVFSObjectInfo file) {
		
		if (file != null) {
			
			String parentPath = file.getParentPath();
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
			simpleDateFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
			Calendar cal = Calendar.getInstance();
		    cal.setTimeInMillis(file.getLastModified());
			String lastModifiedTstmp = simpleDateFormat.format(cal.getTime());
			this.file=file;
			setProperty("VFS.FILE.NAME", file.getFileName());
			setProperty("VFS.FILE.URI", parentPath.substring(0,parentPath.lastIndexOf("/")+1));
			setProperty("VFS.FILE.URL", file.getURL());
			setProperty("VFS.FILE.CREATION.TIME", String.valueOf(lastModifiedTstmp));
			setProperty("VFS.FILE.MODIFICATION.TIME", String.valueOf(lastModifiedTstmp));
			setProperty("VFS.FILE.SIZE", String.valueOf(file.getSize()));
		}
	}


	public void loadFileContent(byte[] data) {
		try {
			this.write(data);
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	public void setProperty(String key, String value) {
		properties.put(key, value);
	}

	public String getProperty(String key) {
		return (String) properties.get(key);
	}


	public Map<String, Object> getProperties() {
		return this.properties;
	}

	public void setObjectProperty(String key, Object value) {
		properties.put(key, value);
	}

}
