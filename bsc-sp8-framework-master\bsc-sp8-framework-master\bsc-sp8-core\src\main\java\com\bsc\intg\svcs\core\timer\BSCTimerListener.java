package com.bsc.intg.svcs.core.timer;

import java.util.Map;

import org.quartz.Scheduler;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentSubType;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCListenerBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCPropertyHelper;

public class BSCTimerListener extends BSCListenerBase {

	BSCTimerListenerContainer 	tlc 			= null;
	Scheduler 					sched		 	= null;
	private String 				cron 			= null;
	private String 				message 		= null;
	private boolean				lockRequired				= false;
	private String				lockPrefix					= null;
	private String				lockName					= null;
	private int					lockTimeOut					= 0;

	public BSCTimerListener(BSCComponentType componentType, BSCComponent parentComponent, String configName) {
		super(componentType, parentComponent, configName);
	}

	public void initialize() throws BSCComponentInitializationException {

		logger.debug("Initializing file listener");
		super.initialize(BSCComponentSubType.VFS_LISTENER);

		cron 					= 	getNonEmptyProperty("schedule");
		message 				=	getProperty("message","");
		lockRequired			= getBooleanProperty("lock.required", false);
		lockPrefix				= getProperty("lock.prefix","/SP8/"+getGlobalProperty("bsc.service.name")+"/"+getGlobalProperty("spring.profiles.active")+"/");
		lockName				= getProperty("lock.name",this.getConfigName());
		lockTimeOut   			= getIntProperty("lock.timeout",0);

		try {

			tlc = new BSCTimerListenerContainer(logger);
			tlc.setTimerListener(this);			
			tlc.setCron(cron);
			tlc.setMessage(message);
			tlc.setLock(lockRequired);
			tlc.setLockDir(lockPrefix+lockName);
			tlc.setLockTimeOut(lockTimeOut);
			tlc.initialize();

		} catch (Throwable e) {
			throw new BSCComponentInitializationException(this, e, BSCErrorLevel.CRITICAL,
					"Failed to initialize the listener " + getInstanceName());
		}

	}

	public void start() throws BSCComponentInitializationException {

		logger.debug("Starting file listener container");
		try {
			tlc.start();
		} catch (Throwable e) {
			logger.error("Error occured to start VFS file listener container", e);
			throw new BSCComponentInitializationException(this, e, BSCErrorLevel.CRITICAL,
					"Failed to start BSCVFSListenerContainer " + getInstanceName());
		}

	}

	public void onMessage(BSCTimerMessage timerMessage) throws BSCServiceException {

		logger.debug("Timer Listener: On Message");

		BSCMessage message = null;
		
		try {

			if (!scriptServiceEnabled) {
				scriptServiceName = null;
			}

			logger.info("Creating the message for the services MessageId - ({}) Timestamp -({})",timerMessage.getProperty("TIMER.MESSAGEID"), timerMessage.getProperty("TIMER.MESSAGETIMESTAMP"));
			message = new BSCMessage(this, eventServiceName, scriptServiceName);
			message.addProperties(this.getProperties(), true, this.getConfigName());
			Map<String, Object> timerMessageProperties = BSCPropertyHelper.getPropertiesStartingWith(timerMessage.getProperties(),"TIMER.", false);
			message.addProperties(timerMessageProperties, true);		
			
			Map<String, Object> timerListenerProperties = BSCPropertyHelper.getPropertiesStartingWith(this.getProperties(),"property.", true);
			message.addProperties(timerListenerProperties, true);
			
			message.write(timerMessage.toByteArray());
			
			message.setProperty("CONTEXT.MESSAGE.ID", timerMessage.getProperty("TIMER.MESSAGEID") );
			message.setProperty("CONTEXT.MESSAGE.TIME",timerMessage.getProperty("TIMER.MESSAGETIMESTAMP") );
			message.setProperty("CONTEXT.MESSAGE.OWNER.NAME", this.configName);
			message.setProperty("CONTEXT.MESSAGE.OWNER.INSTANCE", this.instanceName);
			
			BSCPropertyHelper.printMessageContext(message, logger);
			
			onMessage(message);

		} catch (BSCServiceException e) {

			if (e.getLevel() == BSCErrorLevel.ROLLBACK) {
				logger.error("Rolling back the message", e);
				message.setException(e);
				message.generateEvent(this.errorEventName);
				message.clearException();
				throw e;
			} else {
				logger.error("Error occurred while invoking the service, continuing with the next message", e);
				message.setException(e);
				message.generateEvent(this.errorEventName);
				message.clearException();
				throw e;
			}

		} catch (Throwable e) {
			logger.error("Error occurred while invoking the service, continuing with the next message", e);
			message.setException(e);
			message.generateEvent(this.errorEventName);
			message.clearException();
			throw new BSCServiceException(this, e, BSCErrorLevel.ROLLBACK, "Failed to execute the app service");

		} finally {

			if (message != null) {
				message.close();
				message = null;
			}

		}
		

	}
	
	@Override
	public String getLockDir() {
        return lockPrefix + lockName;
    }

}
