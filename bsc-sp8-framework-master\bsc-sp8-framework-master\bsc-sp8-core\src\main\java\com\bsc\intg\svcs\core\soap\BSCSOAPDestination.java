package com.bsc.intg.svcs.core.soap;

import java.io.StringReader;
import java.io.StringWriter;
import java.net.Socket;
import java.security.KeyStore;
import java.security.Principal;
import java.security.PrivateKey;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.TreeMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.net.ssl.KeyManager;
import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509KeyManager;
import javax.xml.namespace.QName;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Source;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.stream.StreamSource;
import javax.xml.ws.BindingProvider;
import javax.xml.ws.Dispatch;
import javax.xml.ws.Service;
import javax.xml.ws.WebServiceException;
import javax.xml.ws.handler.MessageContext;
import javax.xml.ws.soap.SOAPBinding;
import javax.xml.ws.soap.SOAPFaultException;

import org.apache.http.HttpStatus;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestinationBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCCryptoHelper;
import com.bsc.intg.svcs.core.util.BSCPropertyHelper;


public class BSCSOAPDestination extends BSCDestinationBase {
	
	private String  url				= 	"";
	private String  wsdl			= 	"";
	private String  namespace		= 	"";
	private String  port			= 	"";
	private String  operation		= 	"";
	private String  responseMatcher	= 	"";
	private String 	unescapeResponse=   "";
	private String  bindingType		= 	"";
	private String  messageMode		= 	"";
	
	private String 		sslProtocol				= null;
	private String 		sslClientAlias				= null;
	private String 		sslKeystore					= null;
	private String 		sslKeystoreType				= null;
	private String 		sslKeystorePwd				= null;
	private String 		sslTruststore				= null;
	private String 		sslTruststoreType			= null;
	private String 		sslTruststorePwd			= null;
	
	protected SSLConnectionSocketFactory sslSocketFactory =null;
	
	
	public BSCSOAPDestination(BSCComponentType componentType, BSCComponent parentComponent, String configName) {
		super(componentType, parentComponent, configName);
	}
	
	@Override
	public void initialize() throws BSCComponentInitializationException {
		super.initialize();
		
		sslProtocol					= getProperty("ssl.protocol","TLSv1.2"); 
		sslClientAlias				= getProperty("ssl.client.alias","client"); 
		sslKeystore					= getProperty("ssl.keystore", getGlobalProperty("javax.net.ssl.keyStore"));
		sslKeystoreType				= getProperty("ssl.keystore.type","JKS");
		sslKeystorePwd				= getProperty("ssl.keystore.password", getGlobalProperty("javax.net.ssl.keyStorePassword")); 
		sslTruststore				= getProperty("ssl.truststore",getGlobalProperty("javax.net.ssl.trustStore"));
		sslTruststoreType			= getProperty("ssl.truststore.type","JKS");
		sslTruststorePwd			= getProperty("ssl.truststore.password", getGlobalProperty("javax.net.ssl.trustStorePassword"));
		
		
		
		try {
			
			class FilteredKeyManager implements X509KeyManager {

			    private final X509KeyManager originatingKeyManager;
			    private final X509Certificate sslCertificate;
			    private final String SSLCertificateKeyStoreAlias;

			    public FilteredKeyManager(X509KeyManager originatingKeyManager, X509Certificate sslCertificate, String SSLCertificateKeyStoreAlias) {
			        this.originatingKeyManager = originatingKeyManager;
			        this.sslCertificate = sslCertificate;
			        this.SSLCertificateKeyStoreAlias = SSLCertificateKeyStoreAlias;
			    }

			    @Override
			    public String chooseClientAlias(String[] keyType, Principal[] issuers, Socket socket) {
			        return SSLCertificateKeyStoreAlias;
			    }

			    @Override
			    public String chooseServerAlias(String keyType, Principal[] issuers, Socket socket) {
			        return originatingKeyManager.chooseServerAlias(keyType, issuers, socket);
			    }

			    @Override
			    public X509Certificate[] getCertificateChain(String alias) {
			        return new X509Certificate[]{ sslCertificate };
			    }

			    @Override
			    public String[] getClientAliases(String keyType, Principal[] issuers) {
			        return originatingKeyManager.getClientAliases(keyType, issuers);
			    }

			    @Override
			    public String[] getServerAliases(String keyType, Principal[] issuers) {
			        return originatingKeyManager.getServerAliases(keyType, issuers);
			    }

			    @Override
			    public PrivateKey getPrivateKey(String alias) {
			        return originatingKeyManager.getPrivateKey(alias);
			    }
			}
			
			if (getProperty("ssl.enable","true").equals("true") ) {
				
				if (sslKeystorePwd.contains("ss://")) 
					sslKeystorePwd = BSCCryptoHelper.getSecret(sslKeystorePwd);
				if (sslTruststorePwd.contains("ss://")) 
					sslTruststorePwd = BSCCryptoHelper.getSecret(sslTruststorePwd);
			
				
				KeyStore keyStore = KeyStore.getInstance(sslKeystoreType);
				java.io.FileInputStream keyStoreInputStream = new java.io.FileInputStream(sslKeystore);
				keyStore.load (keyStoreInputStream, sslKeystorePwd.toCharArray());
		
				KeyStore trustStore= KeyStore.getInstance (sslTruststoreType);
				java.io.FileInputStream trustStoreInputStream = new java.io.FileInputStream(sslTruststore);
				trustStore.load (trustStoreInputStream, sslTruststorePwd.toCharArray());
		
				keyStoreInputStream.close();
				trustStoreInputStream.close();
		
				KeyManagerFactory keyManagerFactory = 
				KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
				TrustManagerFactory trustManagerFactory = 
				TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
					
				keyManagerFactory.init(keyStore,sslKeystorePwd.toCharArray());
				trustManagerFactory.init(trustStore);
					
				String sslType=sslProtocol;
		
				SSLContext sslContext = SSLContext.getInstance(sslType); 
					
				sslContext.init(new KeyManager[] { new FilteredKeyManager((X509KeyManager)keyManagerFactory.getKeyManagers()[0], (X509Certificate)keyStore.getCertificateChain(sslClientAlias)[0], sslClientAlias) },    trustManagerFactory.getTrustManagers(), new SecureRandom());
					
				sslSocketFactory = new SSLConnectionSocketFactory(	sslContext,
				            new String[]{sslType}, 
				            null,
				            SSLConnectionSocketFactory.getDefaultHostnameVerifier());
						
			}
			
		} catch (Exception e) {
			throw new BSCComponentInitializationException(this, BSCErrorLevel.CRITICAL, "Failed to initialize SSL");		
		}
		
	}
	
	
	@Override
	public BSCMessage onSend(BSCMessage message) throws BSCDestinationException {
		
		logger.info("Executing SOAP Destination onSend method - ");
		
		BSCMessage soapResponseMessage = null;
		
		try {
			
			
			/* Retrieve the parameters from the message if needed */
			url = message.getProperty("REQUEST.SOAP.URL",getProperty("soap.endpoint.address"));
			wsdl = message.getProperty("REQUEST.SOAP.WSDL",getProperty("soap.wsdl"));
			namespace = message.getProperty("REQUEST.SOAP.NAMESPACE",getProperty("soap.target.namespace"));
			port = message.getProperty("REQUEST.SOAP.PORT",getProperty("soap.port"));
			operation = message.getProperty("REQUEST.SOAP.OPERATION",getProperty("soap.operation"));
			responseMatcher = message.getProperty("REQUEST.SOAP.RESPONSE.MATCHER",getProperty("soap.response.matcher",""));
			bindingType = message.getProperty("REQUEST.SOAP.BINDING.TYPE",getProperty("soap.binding.type"));
			messageMode = message.getProperty("REQUEST.SOAP.MESSAGE.MODE",getProperty("soap.message.mode",""));
			unescapeResponse = message.getProperty("REQUEST.SOAP.UNESCAPE.RESPONSE",getProperty("soap.unescape.response",""));
			
			soapResponseMessage =  invokeSOAPCall(url, wsdl, namespace, port, operation, message, responseMatcher);				

			
		} catch (BSCServiceException | BSCMessageException e) {
			logger.error("Failed to process the request " + e.toString());
			throw new BSCDestinationException(this, e, BSCErrorLevel.CRITICAL, "Failed to process the request " + e.toString());
		} catch (BSCDestinationException e) {
			logger.error("Failed to process the request " + e.toString());
			throw e;
		}
		
		return soapResponseMessage;
	}
		
	
	protected BSCMessage invokeSOAPCall (String endPointAddress,String wsdlURL, String tnSpace, String port, String operation, BSCMessage message, String responseRegEx) throws BSCServiceException, BSCDestinationException, BSCMessageException {
	
		String responseStr = null;
		Map<String, List<String>>	headers	= new HashMap<String, List<String>>();
		BSCMessage soapResponseMessage = null;
		
		try {	
			logger.info("\n EndpointAddress ({}) \n WSDLURL ({}) \n TargetNameSpace ({}) \n Port ({}) \n Operation ({}) \n ", endPointAddress, wsdlURL, tnSpace, port,  operation);
			logger.debug("\n Payload: ({})",message.getStringMessage());
			
			soapResponseMessage = message.cloneMessage(false);
			Date callStart = new Date();
			QName operationQName = new QName(tnSpace,operation );
			QName portQName = new QName(tnSpace,port);
			Service service = Service.create(operationQName );
			 
			if (bindingType.equals("1.1"))
				 service.addPort(portQName,SOAPBinding.SOAP11HTTP_BINDING,wsdlURL);
			else
				 service.addPort(portQName,SOAPBinding.SOAP12HTTP_BINDING,wsdlURL);
			
			Dispatch<Source> dispatch=null;
			
			if (messageMode.equals("PAYLOAD")) 
				dispatch = service.createDispatch(portQName,Source.class,Service.Mode.PAYLOAD);
			else
				dispatch = service.createDispatch(portQName,Source.class,Service.Mode.MESSAGE);
	
			if (endPointAddress!=null && !endPointAddress.isEmpty())   
				 dispatch.getRequestContext().put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY,endPointAddress );
			
			
			// Add customer header if needed 
			headers = loadCustomHeaders(message);		
			dispatch.getRequestContext().put(MessageContext.HTTP_REQUEST_HEADERS, headers);
			
			//Invoke web service operation
			Source response = null;
			
			//try {
				response = dispatch.invoke(new StreamSource(new StringReader(message.getStringMessage())));
			
		
			Date callEnd = new Date();
			logger.info (" SOAP call completed in ({})ms", (callEnd.getTime() - callStart.getTime()));
			logger.debug("Transforming the response to string"); 
			
			//Transform the response
			try {
				TransformerFactory factory = TransformerFactory.newInstance();
				Transformer transformer = factory.newTransformer();
				transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "yes");
				transformer.setOutputProperty(OutputKeys.METHOD, "xml");
				transformer.setOutputProperty(OutputKeys.ENCODING, "UTF-8");		
				StringWriter writer = new StringWriter();
				transformer.transform(response, new StreamResult(writer));
				responseStr= writer.toString();
			
				// Remove CDATA from the payload
				responseStr = responseStr.replaceAll( "<!\\[CDATA\\[", "");
				responseStr = responseStr.replaceAll("]]>", "");
				logger.debug("\n Response Message for SOAP CALL: \n ({})",responseStr);	
				
				
				
				// Load Response Headers to the BSCMessage
				@SuppressWarnings("unchecked")
				TreeMap<String, String> soapResponseHeaders = (TreeMap<String, String>) dispatch.getResponseContext().get(MessageContext.HTTP_RESPONSE_HEADERS);
				Set<Entry<String, String>> set = soapResponseHeaders.entrySet();
				Iterator<Entry<String, String>> iterator = set.iterator();
				while(iterator.hasNext()) {
					@SuppressWarnings("rawtypes")
					Map.Entry entry = (Map.Entry)iterator.next();
					soapResponseMessage.setProperty("RESPONSE.SOAP.HEADER." + String.valueOf(entry.getKey()).toUpperCase(),String.valueOf(entry.getValue()));
				}
				soapResponseMessage.setProperty("RESPONSE.SOAP.HEADER.STATUS", String.valueOf(dispatch.getResponseContext().get(MessageContext.HTTP_RESPONSE_CODE)));
				//soapResponseMessage.setProperty("RESPONSE.SOAP.HEADER.STATUS", String.valueOf(dispatch.getResponseContext().get(MessageContext.HTTP_RESPONSE_CODE)));
			}
			catch (TransformerException e){
				logger.error("Unable to transform the response to string");
				responseStr = e.getMessage();
				soapResponseMessage.setProperty("RESPONSE.SOAP.HEADER.STATUS", HttpStatus.SC_INTERNAL_SERVER_ERROR+"");
				//soapResponseMessage.setProperty("RESPONSE.SOAP.HEADER.STATUS", String.valueOf(dispatch.getResponseContext().get(MessageContext.HTTP_RESPONSE_CODE)));
				
			} 
		
			if (!responseRegEx.isEmpty()) {
				Matcher m = Pattern.compile(responseRegEx,Pattern.DOTALL).matcher(responseStr);
				if( m.find() ) {
					responseStr=m.group(1);
				}
			}
			
			if(unescapeResponse.toUpperCase().equals("TRUE")) {
				responseStr= org.apache.commons.lang3.StringEscapeUtils.unescapeXml(responseStr);
			}
		}
		catch (SOAPFaultException e) {
			responseStr = e.getFault().getFaultString();
			soapResponseMessage.setProperty("RESPONSE.SOAP.HEADER.STATUS", HttpStatus.SC_INTERNAL_SERVER_ERROR+"");
		} catch (WebServiceException we) {
			logger.error("Failed to process the request " + we.toString(), we);
			throw new BSCDestinationException(this, we, BSCErrorLevel.CRITICAL, "Failed to process the request " + we.toString());
		}
		
		logger.debug("\n\nResponse\n{}\n\n",responseStr);
		
		soapResponseMessage.loadMessage(responseStr);
		
		return soapResponseMessage;
	}
	
	public Map<String, List<String>> loadCustomHeaders (BSCMessage message) {
		
		Map<String, List<String>>	headers	= new HashMap<String, List<String>>();
		
		/* Load Custom Headers from the properties */
		Map<String,Object> soapHeaders = (Map<String,Object>) BSCPropertyHelper.getPropertiesStartingWith(this.getProperties(), "soap.header.",true);
	
		/* Load Custom Headers from the message if available */
		Map<String,Object> soapHeadersFromMessage = (Map<String,Object>) BSCPropertyHelper.getPropertiesStartingWith(message.getProperties(), "REQUEST.SOAP.HEADER.",true);
		
	//	if(logger.isDebugEnabled()){
			
			logger.debug("\n\nDestination Headers: \n\n");
			for(Entry<String,Object> p: soapHeaders.entrySet()) {
				logger.debug("\nHeader: {} key-value {} \n", p.getKey(), (String)p.getValue());
				headers.put((String)p.getKey(), Arrays.asList(new String[] {(String) p.getValue()}));
			}		
			
			logger.debug("\n\nMessage Headers: \n\n");
			for(Entry<String,Object> p: soapHeadersFromMessage.entrySet()) {
				logger.debug("\nHeader: {} key-value {} \n", p.getKey(), (String)p.getValue());
				headers.put((String)p.getKey(), Arrays.asList(new String[] {(String) p.getValue()}));
			}
			
			logger.debug("\n\nEffective SOAP Headers: \n\n");
			for(Entry<String, List<String>> p: headers.entrySet()) {
				logger.debug("\nHeader: {} key-value {} \n", p.getKey(), p.getValue());
			}
//		}
		return headers;
	}
	
	@Override
	public List<BSCMessage> onSend(List<BSCMessage> message) throws BSCDestinationException {
		// TODO Auto-generated method stub
		return null;
	}

}
