package com.bsc.intg.svcs.core.common.services;

import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestination;
import com.bsc.intg.svcs.core.BSCIntegrationServiceBase;
import com.bsc.intg.svcs.core.BSCService;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCDestinationAction;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCPropertyHelper;
import com.bsc.intg.svcs.core.util.BSCStringUtils;

/**
 * 
 * This is a common service that performs Queue to REST transport. The
 * properties of this service can be configured to support any integration that
 * requires to read message from the queue and execute call to rest APIs.
 * 
 * <br>
 * <br>
 * **** BSCQueue2RESTService properties ****<br>
 * destination.route = The destination route key,that will determine the
 * destination from destination.route.map <br>
 * destination.route.map = Key value pair of route key:REST destination.Specify
 * the default destination in the map in case of default destination required.
 * <br>
 * response.destination = The queue that will get the response received from
 * REST call. <br>
 * retry.connection = Number of retries in case of connection failure <br>
 * retry.delivery = Number of retries in case of delivery failure <br>
 * retry.wait = Wait duration between each retry(in seconds)<br>
 * <br>
 * 
 * @see <a href=
 *      "https://gitlab.bsci.bossci.com/ei/app/services/java/MPGatewayService_v1.git">https://gitlab.bsci.bossci.com/ei/app/services/java/MPGatewayService_v1.git</a>
 *      For the properties to be defined
 * 
 *      <p>
 *      Property of Boston Scientific Corporation
 *      <p>
 */

public class BSCQueue2RESTService extends BSCIntegrationServiceBase {

	protected BSCDestination respDestination = null;
	protected BSCDestination reqDestination = null;
	protected int connectionRetry = 0;
	protected int deliveryRetry = 0;
	protected String deliveryRetryCodes = "500,503,403,401";
	protected int retryWait = 0;
	protected static Map<String, String> restDestinationMap = new HashMap<String, String>();
	BSCMessage reqMessage = null;

	/**
	 * The BSCQueue2RESTService constructor instantiates the service
	 * configuration and loads the config properties. This instantiation happens
	 * on the start of service.
	 *
	 * @param componentType
	 *            BSCComponent type value for the service : SERVICE
	 * @see BSCComponentType
	 * 
	 * @param parentComponent
	 *            The container of the service
	 * 
	 * @param configName
	 *            The name of the service as mentioned in config properties
	 * 
	 * @param instance
	 *            The number of instance of the service
	 * 
	 */
	public BSCQueue2RESTService(BSCComponentType componentType, BSCComponent parentComponent, String configName,
			String instance) {
		super(componentType, parentComponent, configName, instance);
	}

	/**
	 * The service method gets executed when the listener is triggered(message
	 * is received in the listening queue). It performs REST call to the rest
	 * destination based on the destination route key, and sends the response
	 * back to the response queue configured.In case of a connection or delivery
	 * failure it will try to reconnect to the destination API for the number of
	 * retries configured with an interval between each retry.
	 *
	 * @param inMessage
	 *            BSCMessage is the universal message created by a Listener.
	 *            This message is used to pass information between
	 *            ServiceObjects. The message can carry text or binary data.
	 * @see BSCMessage
	 * 
	 * @return BSCMessage Sends the response message back to the caller
	 * 
	 * @throws BSCServiceException
	 *             if any exception occurs in the service.
	 * 
	 */
	@SuppressWarnings("rawtypes")
	@Override
	public BSCMessage service(BSCMessage inMessage) throws BSCServiceException {
		logger.debug("Executing Queue2RESTService service");

		List<BSCDestination> destList = new ArrayList<BSCDestination>();

		// Filter condition to check if the message can be processed
		if (inMessage.getProperty("invoke.api", "true").equalsIgnoreCase("true")) {
			try {

				BSCMessage outMessage = inMessage.cloneMessage(false);
				// Setting the destination properties based on the destination
				// route key
				String path = inMessage.getProperty("destination.route", "");
				String destNames = restDestinationMap.get(path);

				if (destNames != null && !destNames.isEmpty()) {
					ArrayList<String> destArrayList = BSCStringUtils.convertCommaSeperated(destNames);
					for (String destObjName : destArrayList) {
						destList.add(this.getDestination(destObjName));
					}
				}

				for (BSCDestination dest : destList) {
					outMessage = performHTTPCall(dest, inMessage);
				}

				if (destList.isEmpty() && reqDestination != null)
					outMessage = performHTTPCall(reqDestination, inMessage);

				return outMessage;

			} catch (BSCDestinationException | BSCMessageException | Exception e) {
				inMessage.setException(e);
				throw new BSCServiceException(this, e, BSCErrorLevel.ROLLBACK,
						"Failed to execute the app service: " + e.getMessage());
			}
		} else {
			logger.warn("Message got filtered out as it did not pass the filter condition");
			inMessage.reset();
			try {
				inMessage.loadMessage("Message got filtered out as it did not pass the filter condition");
			} catch (BSCMessageException e) {
				throw new BSCServiceException(this, e, BSCErrorLevel.ROLLBACK,
						"Failed to execute the app service: " + e.getMessage());
			}
			return inMessage;
		}

	}

	private BSCMessage performHTTPCall(BSCDestination dest, BSCMessage inMessage)
			throws BSCServiceException, BSCDestinationException, BSCMessageException {

		int retryCycle = 0;
		int retryMax = 0;
		BSCMessage respMsg = inMessage.cloneMessage(false);
		boolean isConnectionRetry = true;
		// Sending the message to REST destination and retrying in case of
		// failure
		do {
			Throwable connectionExcp = null;
			try {
				if (retryCycle > 0) {
					logger.warn("Executing retry cycle {} and snoozing for {} second before retrying", retryCycle,
							retryWait);
					if (retryMax - retryCycle < 0) {
						if (isConnectionRetry) {
							throw new BSCServiceException(this, connectionExcp, BSCErrorLevel.ROLLBACK,
									"Retry exhausted so rolling back the message");
						} else {
							if (respMsg.getProperty("failure.propagate.response", "false").equalsIgnoreCase("true"))
								propogateResponse(inMessage, respMsg);
							if (respMsg.getProperty("failure.generate.event", "true").equalsIgnoreCase("false"))
								inMessage.disableEvent();
							throw new BSCServiceException(this, BSCErrorLevel.ROLLBACK,
									"Delivery retry exhausted so rolling back the message");
						}
					}

					try {
						TimeUnit.SECONDS.sleep(retryWait);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}

				}

				if (dest.getProperty("auth.service", "").equals("")) {
					respMsg = dest.send(inMessage);
				} else {
					BSCMessage reqMessage = invokeAuthenticator(inMessage, dest.getProperty("auth.service"), "false");
					respMsg = dest.send(reqMessage);
					if (isResetSession(respMsg, dest)) {
						reqMessage = invokeAuthenticator(inMessage, dest.getProperty("auth.service"), "true");
						respMsg = dest.send(reqMessage);
						if (isResetSession(respMsg, dest)) {
							logger.error("Not able to establish the session ");
							throw new BSCDestinationException(this, BSCErrorLevel.CRITICAL,
									"Not able to establish the session. " + respMsg.getStringMessage());

						}

					}
				}
				logger.info("executed HTTP Destination");

			} catch (BSCDestinationException e) {
				logger.info("Destination invocation failed for CONNECTION error with description [{}] ",
						e.getMessage());
				retryMax = connectionRetry;
				retryCycle++;
				connectionExcp = e;
				isConnectionRetry = true;
				continue;
			}

			if (respMsg != null && !respMsg.getProperty("RESPONSE.HTTP.HEADER.STATUSCODE").startsWith("2")) {
				if (deliveryRetryCodes.contains(respMsg.getProperty("RESPONSE.HTTP.HEADER.STATUSCODE"))) {
					logger.error("Destination invocation failed for DELIVERY error with response status code [{}] ",
							respMsg.getProperty("RESPONSE.HTTP.HEADER.STATUSCODE"));
					retryMax = deliveryRetry;
					retryCycle++;

					isConnectionRetry = false;
					continue;
				} else {
					if (respMsg.getProperty("failure.propagate.response", "false").equalsIgnoreCase("true"))
						propogateResponse(inMessage, respMsg);
					if (respMsg.getProperty("failure.generate.event", "true").equalsIgnoreCase("false"))
						inMessage.disableEvent();

					throw new BSCServiceException(this, BSCErrorLevel.CRITICAL,
							"Failed to process the message with status code: "
									+ respMsg.getProperty("RESPONSE.HTTP.HEADER.STATUSCODE"));
				}

			}

																				   
																											 
												   
			if (respMsg.getProperty("success.propagate.response", "false").equalsIgnoreCase("true"))
				propogateResponse(inMessage, respMsg);
			break;

		} while (retryMax - retryCycle >= -1);

		return respMsg;

	}

	private boolean isResetSession(BSCMessage outMsg, BSCDestination dest) throws BSCMessageException {

		String resetCode = dest.getProperty("session.reset.codes", "");
		String resetValue = dest.getProperty("session.reset.values", "");

		if (!resetCode.isEmpty() && resetCode.contains(outMsg.getProperty("RESPONSE.HTTP.HEADER.STATUSCODE"))) {
			logger.info("HTTP response code matched with session reset codes, resetting the session ");
			return true;
		}

		if (!resetValue.isEmpty() && !outMsg.getStringMessage().isEmpty() && resetValue.contains(outMsg.getStringMessage())) {
			logger.info("HTTP response is matched with session reset values, resetting the session ");
			return true;
		}

		return false;

	}

	private BSCMessage invokeAuthenticator(BSCMessage inMessage, String authService, String resetFlag)
			throws BSCServiceException {

		BSCService service = null;
		BSCMessage responseMessage = null;
		try {
			service = this.getServiceContainer().getService(authService);
			service.setProperty("reset.session", resetFlag);
			responseMessage = service.execute(inMessage);
			return responseMessage;

		} catch (BSCServiceException e) {
			throw new BSCServiceException(this, e, e.getLevel(),
					"Failed to execute backend service: " + e.getMessage());

		} finally {
			if (service != null) {
				try {
					this.getServiceContainer().releaseService(authService, service);
				} catch (Exception | BSCServiceException e) {
					throw new BSCServiceException(this, e, BSCErrorLevel.ROLLBACK,
							"Failed to execute backend service" + e.getMessage());
				}
			}
		}

	}

	private void propogateResponse(BSCMessage inMessage, BSCMessage respMsg)
			throws BSCMessageException, BSCDestinationException {
		logger.info("Response from HTTP Destination is :" + respMsg.getStringMessage());
		respMsg.setProperty("include.properties.prefix", inMessage.getMessageOwner().getConfigName() + ".Meta./");
		respMsg.setAction(BSCDestinationAction.JMS_PUT);
		this.respDestination = this
				.getDestination(respMsg.getProperty("response.destination", this.getProperty("response.destination")));
		respDestination.send(respMsg);
	}

	/**
	 * Initializes all the resources of the service
	 * 
	 * @throws BSCComponentInitializationException
	 *             if any exception occurs during initialization of the
	 *             components or resources of the service.
	 */
	@Override
	public void initialize() throws BSCComponentInitializationException {

		super.initialize();
		valid = true;

		// String respDest = this.getProperty("response.destination");
		// if(respDest !=null)
		// this.respDestination = this.getDestination(respDest);
		String reqDest = this.getProperty("request.destination");
		if (reqDest != null)
			this.reqDestination = this.getDestination(reqDest);

		this.connectionRetry = Integer.parseInt(this.getProperty("retry.connection", "0"));
		this.deliveryRetry = Integer.parseInt(this.getProperty("retry.delivery", "0"));
		this.deliveryRetryCodes = this.getProperty("retry.delivery.codes", deliveryRetryCodes);
		this.retryWait = Integer.parseInt(this.getProperty("retry.wait", "30"));
		String destRouteMapProp = this.getProperty("destination.route.map", "");

		// Load the route key to destination mapping

		if (restDestinationMap.isEmpty()) {
			if (destRouteMapProp != null && !destRouteMapProp.isEmpty()) {
				String[] destAr = destRouteMapProp.split(";");
				for (String destObject : destAr) {
					String destKey = destObject.split("\\:")[0];
					String destValue = destObject.split("\\:")[1];
					restDestinationMap.put(destKey, destValue);
				}
			}
		}

	}

}
