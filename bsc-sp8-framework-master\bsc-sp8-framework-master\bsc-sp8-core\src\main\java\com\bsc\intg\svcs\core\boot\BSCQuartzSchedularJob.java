package com.bsc.intg.svcs.core.boot;

import java.time.Instant;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;


@DisallowConcurrentExecution
public class BSCQuartzSchedularJob implements Job {
	
	private Logger logger 	= null;
	
	public BSCQuartzSchedularJob(){
	}


	public void execute(JobExecutionContext context) throws JobExecutionException {
		
		BSCSchedulerTask task=null;
				
		try {
		
			JobDataMap jobMap =  context.getJobDetail().getJobDataMap();
			task=(BSCSchedulerTask ) jobMap.get("task");
			logger= (Logger)context.getJobDetail().getJobDataMap().get("logger");			
			
			logger.debug(Thread.currentThread().getName() + " - " + Instant.now() + " Started the file task {} for lsitener {}" , this.hashCode());
			
			if (!task.isRunning()) {
				logger.debug("Started file processing");
				task.setRunning(true);
				task.process();
			} else {
				logger.debug("Skipped file processing as the previous task is still running");
			}
			
		} catch (Throwable e) {
			logger.error("Error executing Job ", e);
			throw new JobExecutionException();
		} finally  {
			task.setRunning(false);
		}
		
	}
	
}
