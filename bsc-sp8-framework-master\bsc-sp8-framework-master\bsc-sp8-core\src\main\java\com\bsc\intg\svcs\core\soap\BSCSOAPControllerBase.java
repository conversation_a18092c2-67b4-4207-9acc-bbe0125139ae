package com.bsc.intg.svcs.core.soap;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.UriInfo;
import javax.xml.ws.WebServiceContext;
import javax.xml.ws.handler.MessageContext;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentSubType;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCControllerBase;
import com.bsc.intg.svcs.core.BSCService;
import com.bsc.intg.svcs.core.boot.BSCSOAPRuntime;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.message.BSCMessageModel;
import com.bsc.intg.svcs.core.message.BSCModel;
import com.bsc.intg.svcs.core.message.BSCObjectMessageModel;


public abstract class BSCSOAPControllerBase extends BSCControllerBase {

	@Context
	UriInfo uriInfo;
	
	@Context 
	HttpHeaders httpHeaders;
	
	@Resource
    WebServiceContext wsctx;
	
	protected boolean preScriptEnabled 		= false; 
	protected boolean postScriptEnabled		= false;
	protected boolean errorScriptEnabled	= false;

	protected String 	preScript				= null;
	protected String 	postScript				= null;
	protected String 	errorScript				= null;

	
	boolean scriptServiceEnabled 	= false;
	

	protected String 	scriptServiceName		= null;
	protected String	eventServiceName		= null;
	
	
	protected boolean 	isStopped  				= true;
	
	protected String	controllerRuntime		= null;
	
	protected String 	errorEventName			= null;
	
	protected String   endpointURI 				= null; 
	
	public BSCSOAPControllerBase(BSCComponentType componentType ,BSCComponent parentComponent, String configName) {
		super(componentType, parentComponent, configName);
	}
	
	@Override
	public void initialize() throws BSCComponentInitializationException {
		
		super.initialize(BSCComponentSubType.SOAP_CONTROLLER);
		
		preScript				=   getProperty("pre.script","");
		postScript				=   getProperty("post.script","");
		errorScript				=   getProperty("error.script","");
		
		scriptServiceName		=  this.getServiceContainer().getProperty("script.service.name","");
		eventServiceName		=  this.getServiceContainer().getNonEmptyProperty("event.service.name");
	
		if(!scriptServiceName.isEmpty()) 	scriptServiceEnabled=true;
		if(!preScript.isEmpty()) 			preScriptEnabled=true;
		if(!postScript.isEmpty()) 			postScriptEnabled=true;
		if(!errorScript.isEmpty()) 			errorScriptEnabled=true;
		
		controllerRuntime	=	getProperty("run.time","cxf");
		
		endpointURI 		=   getNonEmptyProperty("endpoint.uri");
		
		errorEventName 		=   this.getConfigName() + ".Event.Error";
		
		BSCSOAPRuntime runtime	 = ((BSCSOAPRuntime)this.getServiceContainer().getApplicationService().getApplicationServiceConfig().getRuntimeService(this.controllerRuntime));
		
		if(runtime !=null ) {
			
			logger.info("Registering the SOAP controller {}", this.getConfigName());
			try {
				runtime.registerSOAPEndpoint(endpointURI, this);
			} catch (Exception e) {
				throw new BSCComponentInitializationException(this, e, BSCErrorLevel.CRITICAL, "Failed to register endpoint for " + this.getConfigName());
			}
		}	
		

	}
	
	@SuppressWarnings("rawtypes")
	protected BSCModel dispatchRequest(String serviceName, BSCModel request, BSCModel response) throws Exception {
		
		MessageContext mctx = wsctx.getMessageContext();
		HttpServletRequest webreq = ((HttpServletRequest) mctx.get(MessageContext.SERVLET_REQUEST));
		
		if (!isStopped) {
			
		
			BSCService scriptService = null;
			BSCService eventService = null;
			BSCMessage message=null;
			
			try {
	
				if(!scriptServiceEnabled) {
					scriptServiceName=null;
				}
				
				
				message = new BSCMessage(this, eventServiceName, scriptServiceName);
				
				message.addProperties(this.getProperties(), true, this.getConfigName());
				
				logger.info("Creating the message for the service call");
				
				BSCMessageModel mm = new BSCObjectMessageModel();
				mm.addModel("soap.request",request.getClass(), request, null);
				mm.addModel("soap.response",response.getClass(), response, null);
				message.loadMessage(mm);
				
				if(request instanceof BSCSOAPGatewayRequest){
					BSCSOAPGatewayRequest requestObject = (BSCSOAPGatewayRequest)request;
					message.loadMessage(requestObject.getRequest());
				}
				
				message.copySOAPHTTPPropertiesFrom(mctx,webreq, (Object[]) null);
				
				logger.info("Executing the pre script");
				if (preScriptEnabled) message.executeScript(preScript);
				
	
				BSCService service = null;
				BSCMessage responseMessage = null;
				try {
					
					service = this.getServiceContainer().getService(serviceName);
					
					responseMessage = service.execute(message);
					
					return (BSCModel) responseMessage.getMessageModel().getModel("soap.response").getObject();
				
				} catch (BSCServiceException e) {
					
					try {
						if( errorScriptEnabled && message != null ) {
							logger.info("Executing the error script");
							message.executeScript(errorScript);
						}	
					} catch (BSCServiceException ee) {
						logger.error("Failed to execute error script",ee);
					}
					
					message.generateEvent(this.getConfigName() + ".Event.Error");
					
					
					throw new Exception("Failed to execute backend service " + e.getErrorString());
					
				} finally {
					
					if(service != null) {
						try {
							 this.getServiceContainer().releaseService(serviceName, service);
						} catch (Exception e) {
							throw new Exception("Failed to execute backend service " + e.toString());
						}
					}
					
					
					if (postScriptEnabled && responseMessage != null){
						logger.info("Executing the post script");
						responseMessage.executeScript(postScript);
					}
					
					if (responseMessage != null ) {
						responseMessage.close();
					}
				}
					
			} catch (BSCServiceException | BSCMessageException e ) {
					
					try {
						if( errorScriptEnabled && message != null ) {
							logger.info("Executing the error script");
							message.executeScript(errorScript);
						}	
					} catch (BSCServiceException ee) {
						logger.error("Failed to execute error script",ee);
					}
					
					message.generateEvent(this.errorEventName);
					
					throw new Exception("Failed to execute backend service " + e.getErrorString());
					
			} finally {
				
				
				if(message != null) {
					message.close();
				}
			}
	
		} else {
			throw new Exception("Internal server error ");
		}
		
	}
	
	@Override
	public void stop() {
		if (!isStopped)
			this.isStopped=true;
	}

	@Override
	public void start() {
		
		if ( isStopped ) {
				isStopped=false;
		}	
	}
	

}
