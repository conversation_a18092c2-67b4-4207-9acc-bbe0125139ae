package com.bsc.intg.svcs.core.test;

import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.TestName;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Unit test for simple App.
 */


public class CoreTest
{

	
	@Rule
	public TestName name = new TestName();
	
	final static String TEST_CASE_NAME = "CoreTest";
	public static Logger logger = LoggerFactory.getLogger(TEST_CASE_NAME);

	public static TestHelper testHelper = new TestHelper(TEST_CASE_NAME);
	public static JMSTestHelper jmsHelper = new JMSTestHelper(System.getProperty("primary.prefix"), TEST_CASE_NAME);
	public static String serviceProperties=" --bsc.service.name=bsc-sp8-core  --bsc.instance.name=bsc-sp8-core --bsc.config.name=bsc-sp8-core" ;
	

	@BeforeClass
	public static void setUpBeforeClass() {
		logger	=	(Logger) LoggerFactory.getLogger(CoreTest.class);
   }
	

	@Test
	public void runTests() throws Exception    {
		
		String argString = System.getenv("TEST_APP_ENV") + serviceProperties;
		String[] args = argString.split(" ");
		
		testHelper.invokeSpringBoot("com.bsc.intg.svcs.core.test.CoreTestBoot", "main", args);
		
		System.out.println("Tests are running");
    }

}
