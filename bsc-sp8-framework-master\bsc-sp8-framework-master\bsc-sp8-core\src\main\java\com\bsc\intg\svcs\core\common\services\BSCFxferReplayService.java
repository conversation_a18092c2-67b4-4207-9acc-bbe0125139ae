package com.bsc.intg.svcs.core.common.services;

import java.util.List;
import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCIntegrationServiceBase;
import com.bsc.intg.svcs.core.BSCListenerBase;
import com.bsc.intg.svcs.core.BSCService;
import com.bsc.intg.svcs.core.BSCServiceContainer;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCMessage;



public class BSCFxferReplayService extends BSCIntegrationServiceBase {


	public BSCFxferReplayService(BSCComponentType componentType, BSCComponent parentComponent, String configName,
			String instance) {
		super(componentType, parentComponent, configName, instance);
	}


	@Override
	public BSCMessage service(BSCMessage inMessage) throws BSCServiceException {
		
		BSCMessage outMessage = null;
		
		try {
			//Replay filename convention <ListenereName>_<ActualFilename>
			String fileName = inMessage.getProperty("VFS.FILE.NAME");
			int _pos = fileName.indexOf("_");
			
			if(_pos > 0){				
			
				String listener = fileName.substring(0, _pos);
				inMessage.setProperty("VFS.FILE.NAME",fileName.substring(_pos+1));
				String container = listener+"Container";
				/*List<String> runtimeServices = new ArrayList<>();
				runtimeServices.add("JScript");
				runtimeServices.add("Event");*/
				
				BSCServiceContainer serviceContainer = ((BSCListenerBase)inMessage.getMessageOwner()).getServiceContainer().getApplicationService().getContainer(container);
				
				List<String> serviceList = ((BSCListenerBase)((BSCListenerBase)inMessage.getMessageOwner()).getServiceContainer().getApplicationService().getContainer(container).getListener(listener)).getServiceNames();
						
						
				
				//serviceList.removeAll(runtimeServices);
				
				BSCService service = serviceContainer.getService(serviceList.get(0));
				
				outMessage= service.execute(inMessage);
			
			}
		
		} catch ( Throwable e ) {
			throw new BSCServiceException(this,e,BSCErrorLevel.ROLLBACK,"Failed to execute the app service");
		}
		
		return outMessage;
	}

	


	
	@Override
	public void initialize() throws BSCComponentInitializationException {

		super.initialize();	
		valid=true;
		
	}

}
