package com.bsc.intg.svcs.core.boot;

import static org.quartz.JobBuilder.newJob;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;
import org.quartz.SchedulerException;
import org.quartz.impl.triggers.CronTriggerImpl;
import org.slf4j.Logger;


public class BSCQuartzScheduler  implements BSCScheduler {
	
	org.quartz.Scheduler scheduler ;
	//Trigger 			trigger 					= null;   //Commented by <PERSON><PERSON><PERSON> as a part of VFS Quartz Scheduler
	JobDetail 			job 						= null;
	
	public BSCQuartzScheduler(org.quartz.Scheduler scheduler) {
		this.scheduler=scheduler;
	}
	

	public void scheduleTask(String jobName, String group, String cron, BSCSchedulerTask task, Logger logger)  {
		
		JobDataMap jobMap = new JobDataMap();
		jobMap.put("task", task);
		jobMap.put("logger", logger);
		job = newJob(BSCQuartzSchedularJob.class).withIdentity(jobName).usingJobData(jobMap).build();	
			
		
		try {
		
			CronTriggerImpl trigger = new CronTriggerImpl();
			trigger.setName(jobName);
			trigger.setCronExpression(new BSCCronExpression(cron));
			SimpleDateFormat localDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		
			logger.info("Current Date and Time in local timezone: " + localDateFormat.format( new Date()));
			trigger.getTriggerBuilder().build();
		
			scheduler.scheduleJob(job, trigger);
			logger.info("Fire time " + trigger.getNextFireTime());  //Added by Kaveri as a part of VFS Quartz Schedule
		} catch (SchedulerException e) {
			e.printStackTrace();
		} catch (ParseException e) {
			e.printStackTrace();
		}
	}

	@Override
	public void start() throws Exception {
			scheduler.start();
	}

	@Override
	public void stop() throws Exception {
			scheduler.shutdown();
	}



}
