function initializeAppProps(env){
	
	SP("event.archive.file.location","/AWS/"+env+"/BSC.EVENT/"+message.getProperty("JMS.USER.EVENT_SourceApp")+"/"+message.getProperty("JMS.USER.EVENT_MessageType")+"/"+message.getTimestamp("CST",'yyyyMMdd')+"/");
	SP("event.external.payload.file.location","/AWS/"+env+"/BSC.FILE.STORE/"+message.getProperty("JMS.USER.EVENT_SourceApp")+"/"+message.getProperty("JMS.USER.EVENT_MessageType")+"/"+message.getTimestamp("CST",'yyyyMMdd')+"/");
	
	var listener = message.getMessageOwner().getConfigName();
	if(listener.contains("NotifyListener"))	initializeNotificationProps(env);
	initializeMESProps(env);
			
}