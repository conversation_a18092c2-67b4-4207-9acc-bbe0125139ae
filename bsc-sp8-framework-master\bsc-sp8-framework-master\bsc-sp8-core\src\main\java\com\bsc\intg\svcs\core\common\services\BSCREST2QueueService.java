package com.bsc.intg.svcs.core.common.services;

import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestination;
import com.bsc.intg.svcs.core.BSCIntegrationServiceBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.jms.BSCJMSDestination;
import com.bsc.intg.svcs.core.message.BSCDestinationAction;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCPropertyHelper;
import com.bsc.intg.svcs.core.util.BSCStringUtils;

/**
 * This is a common service that performs REST to Queue transport.
 * The properties of this service can be configured to support any integration that requires to expose a REST service
 * to the caller and send the message received to a queue for further processing through an IIB flow or service.
 * 
 * <br><br>**** BSCREST2QueueService properties ****<br>
 * destination.route = The destination route key,that will determine the destination from destination.route.map <br>
 * destination.route.map = Key value pair of route key:REST destination.Specify the default destination in the map in case of default destination required. <br>
 * Publish : The destination queue that will receive the message from the REST call. <br>
 * destination.map : Key value pair of uri destination:REST destination <br>
 * <br>
 * 
 * @see <a href="https://gitlab.bsci.bossci.com/ei/app/services/java/MPGatewayService_v1.git">https://gitlab.bsci.bossci.com/ei/app/services/java/MPGatewayService_v1.git</a> 
 * 		For the properties to be defined  
 * 
 * <p>
 * Property of Boston Scientific Corporation $Revision: 1.0 $
 * <p>
 */
public class BSCREST2QueueService extends BSCIntegrationServiceBase {

	protected BSCDestination publishToQ = null;
	protected static Map<String, String> destinationMap 	= new HashMap<String, String>();

	
	/**
	 * The BSCREST2QueueService constructor instantiates the service configuration and loads the config properties. This instantiation happens on the start of service.
	 *
	 * @param componentType
	 * 			BSCComponent type value for the service : SERVICE 
	 * @see BSCComponentType  
	 *        
	 * @param parentComponent
	 *           The container of the service 
	 *           
	 * @param configName
	 *           The name of the service as mentioned in config properties 
	 *                    
	 * @param instance
	 *           The number of instance of the service   
	 *  
	 */
	public BSCREST2QueueService(BSCComponentType componentType, BSCComponent parentComponent, String configName,
			String instance) {
		super(componentType, parentComponent, configName, instance);
	}

	
	/**
	 * The service method gets executed whenever a call is made to the service through the controller.
	 * The method reads all the custom headers and url parameters sent by the caller, loads them as message
	 * meta properties and publishes to jms queue.
	 *
	 * @param inMessage
	 * 			BSCMessage is the universal message created by the rest controller. This message is used to pass information between ServiceObjects. The message can carry text or binary data.
	 * @see BSCMessage 
	 * 
	 * @return BSCMessage
	 * 			Sends the response message back to the caller
	 * 
	 * @throws BSCServiceException
	 * 			if any exception occurs in the service.	
	 */	
	@Override
	public BSCMessage service(BSCMessage inMessage) throws BSCServiceException {

		BSCMessage outMessage = null;
		List<BSCDestination> 	destList 			= new ArrayList<BSCDestination>();
		
		try {

			outMessage = inMessage.cloneMessage(true);

			String application = null;
			String messageType = null;
			String domain = null;
			String messageOwner = inMessage.getMessageOwner().getConfigName();
			
			String path = inMessage.getProperty("destination.route","");
			String destNames = destinationMap.get(path);
			String defaultResponse = "";
			
			 if(destNames!=null && !destNames.isEmpty()){
		        	ArrayList<String> destArrayList  = BSCStringUtils.convertCommaSeperated(destNames);
		        	for (String destObjName: destArrayList ) {
		        		destList.add(this.getDestination(destObjName));
		        	}
		        }

			inMessage.getProperties().forEach((k, v) -> logger.debug("Message properties are {}={}", k, (String) v));

			application = inMessage.getProperty("JS.SourceApp");
			messageType = inMessage.getProperty("JS.MessageType");
			domain = inMessage.getProperty("JS.Domain");

			if (application == null || messageType == null || domain == null) {
				
				throw new BSCServiceException(this, null, BSCErrorLevel.FATAL,
						"Mandatory Parameters { x-bsc-application, x-bsc-messagetype, x-bsc-domain} are missing in request");

			}

			//Read custom header values and URL parameters
			Map<String, Object> customHeaders = BSCPropertyHelper.getPropertiesStartingWith(inMessage.getProperties(),
					"HTTP.x-bsc-header-", true);
			Map<String, Object> customUrlParameters = BSCPropertyHelper
					.getPropertiesStartingWith(inMessage.getProperties(), "HTTP.Header-", true);

			if (customHeaders != null) {
				logger.debug("Rest request headers are");
				customHeaders.forEach((k, v) -> {
					inMessage.setProperty(messageOwner + ".Meta." + k, (String) v);
					logger.debug("{}={}", messageOwner + ".Meta." + k, (String) v);
				});
			}

			if (customUrlParameters != null) {
				logger.debug("Rest request headers are");
				customUrlParameters.forEach((k, v) -> {
					inMessage.setProperty(messageOwner + ".Meta." + k, (String) v);
					logger.debug("{}={}", messageOwner + ".Meta." + k, (String) v);
				});
			}
			
			inMessage.setProperty("include.properties.prefix", messageOwner + ".Meta./");
			inMessage.setAction(BSCDestinationAction.JMS_PUT);
			
			if(!destList.isEmpty()){
				for(BSCDestination dest:destList){
					dest.send(inMessage);
					outMessage = inMessage.cloneMessage(false);
					defaultResponse = inMessage.getProperty("PreferredResponse");
					logger.debug("Destination message"+defaultResponse);
					if(defaultResponse!=null)
						outMessage.loadMessage(defaultResponse);
					else
						outMessage.loadMessage("Message is delivered");
				}
			}else{
				defaultResponse = inMessage.getProperty("PreferredResponse");
				publishToQ.send(inMessage);
				outMessage = inMessage.cloneMessage(false);
				logger.debug("Destination message"+defaultResponse);
				if(defaultResponse!=null)
					outMessage.loadMessage(defaultResponse);
				else
					outMessage.loadMessage("Message is delivered");
			}

			
			

		} catch (BSCMessageException | BSCDestinationException | BSCServiceException| Exception e) {
			inMessage.setException(e);
			throw new BSCServiceException(this, e, BSCErrorLevel.ROLLBACK,
				"Failed to process message into queue due to " + e.getMessage());
		}
		return outMessage;
	}


/**
 * Initializes all the resources of the service
 * 
 * @throws BSCComponentInitializationException
 * 			if any exception occurs during initialization of the components or resources of the service.
 */
	@Override
	public void initialize() throws BSCComponentInitializationException {

		super.initialize();
		valid = true;
		publishToQ = this.getDestination("Publish");
		String destRouteMapProp 	= 	this.getProperty("destination.route.map","");
		
		if (destinationMap.isEmpty()){
			if(destRouteMapProp!=null && !destRouteMapProp.isEmpty()){
				 String[] destAr = destRouteMapProp.split(";");
		            for(String destObject: destAr){
		                String destKey = destObject.split("\\:")[0];
		                String destValue = destObject.split("\\:")[1];
		                destinationMap.put(destKey, destValue);
		            }
			}
			
		}

	}

}
