package com.bsc.intg.svcs.core.dfs;


import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;

import org.apache.zookeeper.CreateMode;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentBase;
import com.bsc.intg.svcs.core.BSCComponentType;

/**
 * 
 * ZooKeeperDFSService class wraps ZNode operations using Zookeeper CuratorFramework 
 */
public class NFSDFSService implements DFSService,DFSServiceProvider  {
	

	private static DFSService nfsDfs = null;
	private  String workpath = System.getProperty("nfs.work.path");
	private boolean isPath = false;
	
	
	public static DFSService getInstance() {
		if(nfsDfs!=null)
			return nfsDfs;
		else{
			nfsDfs = new NFSDFSService();
			return nfsDfs;
		}
		
	}
	
	/**
	 * Creates file at mentioned path with byte data and 
	 */

	@Override
	public void create(String path, byte[] data, String fileName) throws DFSServiceException {
		try {
			if(!isPath)path = workpath + path+".txt";
		     File file = new File(path);
		     /*If file gets created then the createNewFile() 
		      * method would return true or if the file is 
		      * already present it would return false
		      */
	             boolean fvar = file.createNewFile();
		     if (fvar){
		          System.out.println("File has been created successfully");
		          FileOutputStream fos = new FileOutputStream("pathname");
					   fos.write(data);
					   fos.close();
		     }
		     else{
		          System.out.println("File already present at the specified location");
		     }
	    	} catch (IOException e) {
	    		System.out.println("Exception Occurred:");
		        e.printStackTrace();
		  }

	}

	/**
	 * Fetch file data from mentioned path as byte data
	 * @return byte[]
	 *
	 */
	@Override
	public byte[] getData(String path) throws DFSServiceException {
		
		if(!isPath)path = workpath + path+".txt";
		byte[] stateFileBytes = null;
		try {
			stateFileBytes = Files.readAllBytes(Paths.get(path));

		} catch (Exception e) {
			
	        return null;
		}

		return stateFileBytes;

	}
	
	/**
	 * checks if file exists for mentioned path
	 * @return true if file exists 
	 *
	 */
	@Override
	public boolean pathExists(String path) throws DFSServiceException {
		if(!isPath)path = workpath + path+".txt";
		 File file = new File(path);
		 return file.exists();
	}

	/**
	 * Writes file data in bytes for mentioned path
	 */
	@Override
	public void setData(String path, byte[] data) throws DFSServiceException {
		if(!isPath)path = workpath + path+".txt";
			
			try (FileOutputStream fos = new FileOutputStream(path)) {
				   fos.write(data);
				   fos.close();

		} catch (Exception e) {
			isPath = true;
			if(!pathExists(path)){
       		 create(path, data, null);
       	 }
			else{
			DFSServiceException ale = new DFSServiceException(
					"File update failed,  exception occured while updating the file " + e.getMessage());
			ale.initCause(e);
			throw ale;
			}
		}

	}
	/**
	 * Deletes specified file 
	 * 
	 */

	@Override
	public void delete(String path)throws DFSServiceException{
		if(!isPath)path = workpath + path+".txt";
		try {
			 File myObj = new File(path); 
			    if (myObj.delete()) { 
			      System.out.println("Deleted the file: " + myObj.getName());
			    } else {
			      System.out.println("Failed to delete the file.");
			    } 
		} catch (Exception e) {
			DFSServiceException ale = new DFSServiceException(
					"File delete failed,  exception occured while deleting the file " + e.getMessage());
			ale.initCause(e);
			throw ale;
		}
		
	}

	
	@Override
	public List<String> getChildren(String path)throws DFSServiceException {
		List<String> child = null;
		return child;
		
	}
	
	
	@Override
	public void start() {
		
	}

	@Override
	public void shutdown() {
		
	}

	@Override
	public void setLogLevel(String level) {
		
	}

	/**
	 * Provides DFSServiceProvider instance
	 */
	@Override
	public DFSServiceProvider getDFSServiceProvider() {
		return (DFSServiceProvider)nfsDfs;
	}


}



