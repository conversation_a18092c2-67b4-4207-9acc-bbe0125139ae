	
package com.bsc.intg.svcs.core.jms;

import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.atomic.AtomicReference;

import javax.jms.BytesMessage;
import javax.jms.ConnectionFactory;
import javax.jms.Destination;
import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.QueueBrowser;
import javax.jms.Session;
import javax.jms.TextMessage;

import org.springframework.jms.core.BrowserCallback;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.jms.core.MessageCreator;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestinationBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.message.BSCMessageProperties;
import com.bsc.intg.svcs.core.util.BSCPropertyHelper;
import com.ibm.mq.jms.MQDestination;
import com.ibm.msg.client.wmq.WMQConstants;

/**
 * 
 *Creates JMS destination and performs JMS operations to write messages.See the individual properties for each parameter for a full explanation of their use.
 * 
 * <br><br>**** BSCJMSDestination properties ****<br>
 * destination.name = name of the destination<br>
 * destination.type = destination type.e.g. queue or topic.Default is set as queue.<br>
 * destination.target = define the destination target.Default value is "JMS".<br>
 * get.timeout = timeout to use for jms receive calls (in milliseconds)<br>
 * connection = connection resource name.<br>
 * transactional = the transaction mode that is used when creating a JMS Session. Default is "false".<br>
 * enableMQMDwrite = flag to enable MQMD header property overwrite.Default is set as "false".<br>
 * <br>
 * 
 * <p>
 * Property of Boston Scientific Corporation
 * <p>
 */

public class BSCJMSDestination extends BSCDestinationBase {

	public BSCJMSDestination(BSCComponentType componentType, BSCComponent parentComponent, String configName) {
		super(componentType, parentComponent, configName);
	}

	private 		JmsTemplate 	jmsTemplate;
	
	protected 		String 			destinationName		=  null;
	protected 		String 			destinationType		=  null;
	protected 		String 			destinationTarget	=  null;
	
	protected 		int	   			getTimeOut			=  0;
	
	protected       String 			connection 			= null;
	
	protected 		boolean transactional   =  true;
	
	protected 		boolean enableMQMDwrite   =  false;
	
	protected  String  defaultEncoding 					= "273";
	protected  String  defaultCCSID						= "1208";
	  
	public void initialize() throws BSCComponentInitializationException {
		
		super.initialize();
		
		logger.info("Initializing JMS Destination");
		
		this.destinationName = getNonEmptyProperty("destination.name");
		this.destinationType = getProperty("destination.type", "queue");
		this.destinationTarget = getProperty("destination.target", "JMS");
		
		this.getTimeOut=getIntProperty("get.timeout",5000);

		this.connection= getNonEmptyProperty("connection");
		
		this.transactional= getBooleanProperty("transactional", true);
		
		this.enableMQMDwrite= getBooleanProperty("enableMQMDwrite", false);
		
		try {
			
			this.jmsTemplate=createJMSTemplate();
			
			if (!transactional)
				this.jmsTemplate.setSessionTransacted(false);
			
		} catch (Exception e) {
			logger.error("Error occured JMS Destination", e);
			throw new BSCComponentInitializationException(this, e,BSCErrorLevel.CRITICAL, "Failed to initialize the destination " + getInstanceName() );
		}
		
		logger.info("Initialized JMS Destination with Queue: {} GetTimeout: {} ", this.destinationName,this.getTimeOut );
		
		
	}
	

	public  JmsTemplate createJMSTemplate() throws Exception  {
        
		JmsTemplate jmsTemplate = new JmsTemplate();
		BSCJMSConnectionResource jmsConnectionResource = 	((BSCJMSConnectionResource)this.getService().getServiceContainer().getApplicationService().getResource(  this.connection ));
		ConnectionFactory cf = jmsConnectionResource.getJMSConnectionFactory();	
		Destination destination = jmsConnectionResource.getJMSDestination(this.destinationName, this.destinationType);
		
		defaultEncoding = String.valueOf(((MQDestination) (destination)).getIntProperty (WMQConstants.WMQ_ENCODING));
		defaultCCSID = String.valueOf(((MQDestination) destination).getIntProperty(WMQConstants.WMQ_CCSID));
		
		MQDestination mqDest = (MQDestination) destination;
		mqDest.setBooleanProperty(WMQConstants.WMQ_MQMD_WRITE_ENABLED, enableMQMDwrite);
		
		jmsTemplate.setConnectionFactory( cf );
       	        
        jmsTemplate.setDefaultDestination(mqDest);
       
        return jmsTemplate;
       
    }
	
	@Override
	public BSCMessage onSend(BSCMessage message) throws BSCDestinationException {

		
		final AtomicReference<Message> arJMSMessage =  new AtomicReference<Message>();
		
		switch (message.getAction()) {
    	
			case JMS_PUT :
				
				logger.info("Putting the message on the queue {}", destinationName );
				
				BSCPropertyHelper.printMessageContext(message, logger);
				
				int encoding,ccsid;
				
				try {
					encoding= Integer.parseInt(message.getProperty("JMS.USER.ENCODING",defaultEncoding));
					ccsid= Integer.parseInt(message.getProperty("JMS.USER.CCSID",defaultCCSID));
					
				} catch(NumberFormatException e) {
					throw new BSCDestinationException(this,message.getException(),BSCErrorLevel.CRITICAL,"Failed to convert encoding/ccsid value to an integer");
				}
				
				
			   	try {
			   		
			   		switch (message.getProperty("JMS.USER.TYPE","TEXT") ) 		{
			   		
			   			case "TEXT":
			   			
				   			jmsTemplate.send(new MessageCreator() {
					    		
					            public Message createMessage(Session session) throws JMSException {
					            	
									logger.info("Creating JMS text message");
	
					                TextMessage jmsTextMessage;
					                message.clearException();
					                
					                
					                try {
					                	
					                	logger.info("Loading custom JMS properties");
					                	
					                	Map<String,String> jmsProperties = null;
					    				
					                	String includePropertiesPrefix = message.getProperty("include.properties.prefix", "");
					                	
					                	if (!includePropertiesPrefix.equals("")) {
					                		jmsProperties = new HashMap<String,String>();
					                		
					                		logger.info("Loading JMS properties with prefixes ({})", includePropertiesPrefix );
						                	
					                		for (String prefix : includePropertiesPrefix.split(",")) {
						    				
					                			logger.info("	Loading JMS properties with prefix ({})", prefix );
							                	
					                			String prefixToRemove = null, prefixToAdd = null ;
					                			
					                			String [] splitStrings = prefix.split("/");
					                			
					                			if (splitStrings.length > 1) {
					                				prefixToRemove = prefix.split("/")[0];
					                				prefixToAdd = prefix.split("/")[1];
					                			} else {
					                				prefixToRemove = prefix.split("/")[0];
					                				prefixToAdd = "";
					                			}
					                			
						    					Map<String,Object> customProperties = (Map<String,Object>) BSCPropertyHelper.getPropertiesStartingWith(message.getProperties(), prefixToRemove, true);
						    					
						    					for(Entry<String,Object> p: customProperties.entrySet()) {
						    						jmsProperties.put("JMS.USER."+ prefixToAdd + p.getKey(), (String)p.getValue());
						    					}
						    				}
						    				
					                	}
										
					                	jmsTextMessage = session.createTextMessage(message.getStringMessage());
										
										if (jmsProperties!=null) message.copyJMSPropertiesTo(jmsTextMessage, jmsProperties );
										
										message.copyJMSPropertiesTo(jmsTextMessage, (Object[]) null );
						                
										arJMSMessage.set(jmsTextMessage);
						                
						                
									} catch (BSCMessageException e) {
										message.setException(e);
									}
									
					                return arJMSMessage.get();
					            }
					        });
					    	
					    	
					    	break;
				    	
			   		
			   		case "BINARY":
			   		
				   			jmsTemplate.send(new MessageCreator() {
					    		
					            public Message createMessage(Session session) throws JMSException {
					            	
									logger.info("Creating JMS binary message");
	
					                BytesMessage jmsBytesMessage;
					                message.clearException();
					                
					                
					                try {
					                	
					                	logger.info("Loading custom JMS properties");
					                	
					                	Map<String,String> jmsProperties = null;
					    				
					                	String includePropertiesPrefix = message.getProperty("include.properties.prefix", "");
					                	
					                	if (!includePropertiesPrefix.equals("")) {
					                		jmsProperties = new HashMap<String,String>();
					                		
					                		logger.info("Loading JMS properties with prefixes ({})", includePropertiesPrefix );
						                	
					                		for (String prefix : includePropertiesPrefix.split(",")) {
						    				
					                			logger.info("	Loading JMS properties with prefix ({})", prefix );
							                	
					                			String prefixToRemove = null, prefixToAdd = null ;
					                			
					                			String [] splitStrings = prefix.split("/");
					                			
					                			if (splitStrings.length > 1) {
					                				prefixToRemove = prefix.split("/")[0];
					                				prefixToAdd = prefix.split("/")[1];
					                			} else {
					                				prefixToRemove = prefix.split("/")[0];
					                				prefixToAdd = "";
					                			}
					                			
						    					Map<String,Object> customProperties = (Map<String,Object>) BSCPropertyHelper.getPropertiesStartingWith(message.getProperties(), prefixToRemove, true);
						    					
						    					for(Entry<String,Object> p: customProperties.entrySet()) {
						    						jmsProperties.put("JMS.USER."+ prefixToAdd + p.getKey(), (String)p.getValue());
						    					}
						    				}
						    				
					                	}
										
					                	jmsBytesMessage = session.createBytesMessage();
										jmsBytesMessage.writeBytes(message.toByteArray());
										
										jmsBytesMessage.setIntProperty(WMQConstants.JMS_IBM_ENCODING, encoding);
										jmsBytesMessage.setIntProperty(WMQConstants.JMS_IBM_CHARACTER_SET, ccsid);
										  
										if (jmsProperties!=null) message.copyJMSPropertiesTo(jmsBytesMessage, jmsProperties );
										
										message.copyJMSPropertiesTo(jmsBytesMessage, (Object[]) null );
						                
										arJMSMessage.set(jmsBytesMessage);
						                
						                
									} catch (Exception e) {
										message.setException(e);
									}
									
					                return arJMSMessage.get();
					            }
					        });
					    	
	
				   
					    	break;
				    	
				    	
			   		}
		    		
			   		
			    	if (message.getException() != null ) {
			    		logger.error("Failed to create the JMS message ", message.getException());
			    		throw new BSCDestinationException(this,message.getException(),BSCErrorLevel.CRITICAL,"Failed to put the JMS message");
			    	} else {
			    		message.copyJMSPropertiesFrom(arJMSMessage.get(),  BSCMessageProperties.JMS_MESSAGE_ID);
			    		logger.info("JMS put with ({}) completed successfully ", message.getProperty(BSCMessageProperties.JMS_MESSAGE_ID) );
			    	}
			    	
			   	} catch (Exception  e) {
		    		throw new BSCDestinationException(this,e,BSCErrorLevel.CRITICAL);
		    	}
		    	
		    	return message;
	    	
			case JMS_GET:
				
				 logger.info("Finding the message on the queue {}", destinationName );
				 jmsTemplate.setReceiveTimeout(this.getTimeOut);
				 BSCMessage reply = null;
					 Message replyJMSMsg =  jmsTemplate.receiveSelected(message.getProperty(BSCMessageProperties.JMS_MESSAGE_SELECTOR));
					 if (replyJMSMsg != null  ) {
		       	 
					 try {
						 reply = message.cloneMessage(false).loadMessage(replyJMSMsg);
						 reply.copyJMSPropertiesFrom(replyJMSMsg, (Object[]) null);
					
					 } catch (BSCMessageException | JMSException e) {
						 throw new BSCDestinationException(this, e, BSCErrorLevel.CRITICAL,"Failed to load the jms mesage");
					 }
				 	}
				 
		        
		        return reply;
		        
			case JMS_BROWSESELECTED:
				
				BSCMessage msgReply = null; 
				Message browseJMSMsg =  jmsTemplate.browseSelected(message.getProperty(BSCMessageProperties.JMS_MESSAGE_SELECTOR), new BrowserCallback<Message>() {
				       
			            public Message doInJms(Session session,
			                    QueueBrowser browser) throws JMSException {
			                
			                @SuppressWarnings("unchecked")
			                Enumeration<Message> messageEnum = browser
			                        .getEnumeration();
			                if(messageEnum == null || !messageEnum.hasMoreElements())
			                	return null;
			                else
			                	return messageEnum.nextElement();
			            }
			        });
				if (browseJMSMsg != null  ) {
					 
					 try {
						 msgReply = message.cloneMessage(false).loadMessage(browseJMSMsg);
						 msgReply.copyJMSPropertiesFrom(browseJMSMsg, (Object[]) null);
					
					 } catch (BSCMessageException | JMSException e) {
						 throw new BSCDestinationException(this, e, BSCErrorLevel.CRITICAL,"Failed to load the jms mesage");
					 }
				 	}
			 return msgReply;
			 
			case JMS_BROWSE:
				
				BSCMessage browseReply = null; 
				Message browseMsg =  jmsTemplate.browse(new BrowserCallback<Message>() {
				       
			            public Message doInJms(Session session,
			                    QueueBrowser browser) throws JMSException {
			                
			                @SuppressWarnings("unchecked")
			                Enumeration<Message> messageEnum = browser
			                        .getEnumeration();

			                if(messageEnum == null || !messageEnum.hasMoreElements())
			                	return null;
			                else
			                	return messageEnum.nextElement();
			            }
			        });
				if (browseMsg != null  ) {
					 
					 try {
						 browseReply = message.cloneMessage(false).loadMessage(browseMsg);
						 browseReply.copyJMSPropertiesFrom(browseMsg, (Object[]) null);
					
					 } catch (BSCMessageException | JMSException e) {
						 throw new BSCDestinationException(this, e, BSCErrorLevel.CRITICAL,"Failed to load the jms mesage");
					 }
				 	}
			 return browseReply;
		
	        
			default:
				 throw new BSCDestinationException(this, BSCErrorLevel.CRITICAL,"Unspecified destination action ");
			
		}
		
        
	}


	@Override
	public List<BSCMessage> onSend(List<BSCMessage> message) throws BSCDestinationException {
		// TODO Auto-generated method stub
		return null;
	}
	


	
}
