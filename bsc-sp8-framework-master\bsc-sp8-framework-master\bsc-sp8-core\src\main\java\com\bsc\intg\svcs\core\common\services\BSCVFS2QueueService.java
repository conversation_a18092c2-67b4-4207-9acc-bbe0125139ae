package com.bsc.intg.svcs.core.common.services;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestination;
import com.bsc.intg.svcs.core.BSCIntegrationServiceBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCDestinationAction;
import com.bsc.intg.svcs.core.message.BSCMessage;

public class BSCVFS2QueueService extends BSCIntegrationServiceBase {

	protected BSCDestination publish = null;

	public BSCVFS2QueueService(BSCComponentType componentType, BSCComponent parentComponent, String configName,
			String instance) {
		super(componentType, parentComponent, configName, instance);
	}

	@Override
	public BSCMessage service(BSCMessage inMessage) throws BSCServiceException {

	try {
			logger.debug("Executing VFS2QueueService Service");
			inMessage.setAction(BSCDestinationAction.JMS_PUT);
			inMessage.setProperty("include.properties.prefix", inMessage.getMessageOwner().getConfigName() + ".Meta./");
			publish.send(inMessage);

		} catch (Exception | BSCDestinationException  e) {

			logger.error("Failed to publish the file", e);

			throw new BSCServiceException(this, e, BSCErrorLevel.ROLLBACK, "Failed to move message to Publish Queue");

		}
		return inMessage;
	}

	@Override
	public void initialize() throws BSCComponentInitializationException {

		super.initialize();
		valid = true;
		publish = this.getDestination("VFS2QPublish");
	}

}
