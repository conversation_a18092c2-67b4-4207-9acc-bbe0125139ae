
package com.bsc.intg.svcs.core.email;

import java.util.List;

import javax.mail.MessagingException;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;

import org.springframework.mail.MailSendException;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestinationBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.message.BSCMessage;

public class BSCEmailDestination extends BSCDestinationBase {

	public BSCEmailDestination(BSCComponentType componentType, BSCComponent parentComponent, String configName) {
		super(componentType, parentComponent, configName);
	}

	private JavaMailSenderImpl emailSender = new JavaMailSenderImpl();

	public void initialize() throws BSCComponentInitializationException {

		super.initialize();
		
		logger.info("Initializing Email Destination");
		
		emailSender.setHost(getProperty("host", getGlobalProperty("bsc.email.smtphost")));
		emailSender.setProtocol(getProperty("protocol"));
	}

	@Override
	public BSCMessage onSend(BSCMessage message) throws BSCDestinationException {
		
		logger.info("Executing Email Destination onSend method - ");
		
		logger.debug(
				"EmailFrom - {}, EmailTo - {}, EmailCC - {}, EmailBCC - {}, EmailSubject - {}, Application - {}, Priority - {} ",
				getProperty("email.from"), getProperty("email.to"), getProperty("email.cc"), getProperty("email.bcc"), getProperty("email.subject"), getProperty("email.application"), getProperty("email.priority"));

		MimeMessage msg = emailSender.createMimeMessage();	
		
		MimeMessageHelper helper = null;

		try {
			
			helper = new MimeMessageHelper(msg, true);
			helper.setFrom(getProperty("email.from"));
			
			InternetAddress[] toAdressArray = InternetAddress.parse(message.getProperty("email.to", getProperty("email.to")));
			helper.setTo(toAdressArray);

			if ((message.getProperty("email.cc", getProperty("email.cc"))) != null) {
				InternetAddress[] ccAdressArray = InternetAddress.parse(message.getProperty("email.cc", getProperty("email.cc")));
				helper.setCc(ccAdressArray);
			}
			if ((message.getProperty("email.bcc", getProperty("email.bcc"))) != null) {
				InternetAddress[] bccAdressArray = InternetAddress.parse(message.getProperty("email.bcc", getProperty("email.bcc")));
				helper.setBcc(bccAdressArray);
			}

			helper.setSubject(message.getProperty("email.subject", getProperty("email.subject")));
			helper.setText(message.getProperty("email.content", getProperty("email.content")), true);			

			if ((message.getProperty("email.priority", getProperty("email.priority"))) != null) {
				msg.addHeader("X-Priority", (message.getProperty("email.priority", getProperty("email.priority"))));
			}
			
			logger.info("Sending Email - ");
			
			emailSender.send(msg);
			
			//helper.addAttachment("attachment", new FileSystemResource(new File(getProperty("email.attachment"))));
		} catch (MessagingException | MailSendException e) {
			logger.error("Unable to construct/send email message- {}", e.toString());
			message.setException(e);
			throw new BSCDestinationException(this, e, BSCErrorLevel.CRITICAL, "Unable to construct/send  email message - " + e.toString());
		}
		return message;
	}

	@Override
	public List<BSCMessage> onSend(List<BSCMessage> message) throws BSCDestinationException {
		// TODO Auto-generated method stub
		return null;
	}

}