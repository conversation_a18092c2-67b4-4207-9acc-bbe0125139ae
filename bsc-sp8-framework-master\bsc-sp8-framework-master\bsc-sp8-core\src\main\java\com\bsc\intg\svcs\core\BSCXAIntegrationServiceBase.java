package com.bsc.intg.svcs.core;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.Map;

import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCExceptionBase;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCMessage;

public abstract class BSCXAIntegrationServiceBase extends BSCIntegrationServiceBase {
	

	ArrayList<String> destinations = null;
	
	Map<String, BSCDestination> destinationMap = new LinkedHashMap<String,BSCDestination >();
	
	public BSCXAIntegrationServiceBase(BSCComponentType componentType ,BSCComponent parentComponent, String configName, String instance ) {
		super(componentType, parentComponent, configName, instance);
		
	}
	

	public void initialize() throws BSCComponentInitializationException  {
		super.initialize();
	}		

	@Override
	public BSCMessage execute(BSCMessage message) throws BSCServiceException {
		
		logger.info("Executing the service call under transaction");
		
		TransactionTemplate tt = new TransactionTemplate(this.getServiceContainer().getApplicationService().getTransactionManager());
		
		BSCMessage response= tt.execute(new TransactionCallback<BSCMessage>() {
	
		@Override
		public BSCMessage doInTransaction(TransactionStatus ts) {
			try {
				logger.info("Invoking the service call");
				return service(message);

			} catch (RuntimeException | BSCServiceException e) {
				logger.error("Error occured while executing transaction under XA");
				
				message.setException(e);
				
				if (e instanceof BSCServiceException ) {
					if (  ((BSCServiceException)e).getLevel() == BSCErrorLevel.ROLLBACK ) {
						ts.setRollbackOnly();
					}	
				} else {
					ts.setRollbackOnly();
				}
			}
			logger.info("Completed executing the service call under transaction");
			
			return message;
		}
		});
		
		Throwable e = message.getException();
		
		if (e != null) {
			
			message.clearException();

			if (e instanceof BSCServiceException ) {
				logger.error("Service Exception occured while executing the service call under XA transaction");
				throw (BSCServiceException)e;	
			} else {
				logger.error("Runtime exception occured while invoking the service call, rolling back the XA transaction");
				throw new BSCServiceException(this,e,BSCErrorLevel.ROLLBACK);
			}
			
		} else {
			logger.info("Completed executing the service call under transaction");
			return response;
		}
		
	}
	

	
}
