package com.bsc.intg.svcs.core.container.services;

import java.util.UUID;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestination;
import com.bsc.intg.svcs.core.BSCIntegrationServiceBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCDestinationAction;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.message.BSCMessageProperties;
import com.bsc.intg.svcs.core.util.BSCErrorHelper;

public class BSCEventService extends BSCIntegrationServiceBase {


	BSCDestination eventDestination = null;
	String eventDestinationType = null;
	
	public BSCEventService(BSCComponentType componentType, BSCComponent parentComponent, String configName,
			String instance) {
		super(componentType, parentComponent, configName, instance);
	}

	@Override
	public BSCMessage service(BSCMessage message) throws BSCServiceException {
		
		String eventName=message.getProperty(BSCMessageProperties.EVENT_NAME);
		
		try {
			
			BSCMessage eventMessage=null;
			
			logger.info("Sending the event to the destination ( {} ) event ( {} )", eventDestination.getInstanceName(), eventName );
			
			if (message.getProperty(eventName + ".EVENT_IncludePayload","true").toUpperCase().equals("FALSE") ) {
				eventMessage=message.cloneMessage(false);
				eventMessage.loadMessage(" ");
			} else {
				eventMessage=message.cloneMessage(true);
			}
			
			eventMessage.setProperty(eventName + ".EVENT_Id", UUID.randomUUID().toString());

			eventMessage.setProperty(eventName + ".EVENT_Time", eventMessage.getTimestamp(null, null));
			
			if(eventName!= null && eventName.endsWith(".Error") ){				
				eventMessage.setProperty(eventName + ".EVENT_StackTrace", BSCErrorHelper.getExceptionTrace(message.getException(), 10));
				eventMessage.setProperty(eventName + ".EVENT_ErrorDescription",message.getProperty("ERROR.Description", message.getException().toString()));
				eventMessage.setProperty(eventName + ".EVENT_ErrorCode",message.getProperty("ERROR.Code","0"));							
			}
			
			if ( eventDestinationType.equals("JMS")) {
				eventMessage.setAction(BSCDestinationAction.JMS_PUT);
			}
			
			String includePropertiesPrefix = getGlobalProperty(message.getMessageOwner().getConfigName() + ".event.properties.prefix", "");
			
			if (!includePropertiesPrefix.equals(""))
				includePropertiesPrefix="," + includePropertiesPrefix;
			
			eventMessage.setProperty("include.properties.prefix", eventName +".EVENT_/EVENT_" + includePropertiesPrefix );
			eventDestination.send(eventMessage);
			

		} catch ( RuntimeException | BSCDestinationException | BSCMessageException  e) {
			logger.error(SMTP_TRIGGER, "Failed to send " + eventName + " event to the destination",e);
		}
		
		return message; 
	}

	@Override
	public void initialize() throws BSCComponentInitializationException {

		super.initialize();
		
		logger.info("Initializing event service ");
		
		eventDestination 		= getDestination("EventDestination");
		
		eventDestinationType 	= getProperty("destination.type", "JMS");
		
	}

	
}
