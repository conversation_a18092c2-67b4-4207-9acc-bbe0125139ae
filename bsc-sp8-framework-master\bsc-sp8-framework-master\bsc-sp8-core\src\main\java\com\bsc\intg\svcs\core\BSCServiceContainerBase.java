package com.bsc.intg.svcs.core;

import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;

public abstract class BSCServiceContainerBase extends BSCComponentBase implements BSCServiceContainer,BSCComponent {

	
	BSCServiceContainerBase(BSCComponentType componentType ,BSCComponent parentComponent, String name) {
		super(componentType,parentComponent,name);
	}

	public void initialize() throws  BSCComponentInitializationException {
		
	}
	

	public BSCApplicationService getApplicationService() {
		return (BSCApplicationService)parentComponent;
	}
}
