package com.bsc.intg.svcs.core.test;

import javax.inject.Singleton;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriInfo;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.rest.BSCRESTControllerBase;

@Singleton
@Path("/TestREST/v1")
public class TestRESTController extends BSCRESTControllerBase  {
	
	
	public TestRESTController(BSCComponentType componentType, BSCComponent parentComponent, String configName) {
		super(componentType, parentComponent, configName);
	}

	@POST
	@Path("/publish")
	@Consumes({MediaType.TEXT_PLAIN})
	@Produces({MediaType.TEXT_PLAIN})
	public Response processRequest(String request,@Context UriInfo uriInfo) {
		return dispatchRequest("IntService",request,MediaType.TEXT_PLAIN);
	}

}
