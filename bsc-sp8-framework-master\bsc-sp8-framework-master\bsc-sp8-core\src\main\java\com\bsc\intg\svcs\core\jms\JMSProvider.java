package com.bsc.intg.svcs.core.jms;

import java.util.Map;

import javax.jms.ConnectionFactory;
import javax.jms.Destination;
import javax.jms.JMSException;
import javax.jms.Queue;
import javax.jms.XAConnectionFactory;
import javax.naming.NamingException;



public interface JMSProvider {

	
	public XAConnectionFactory getXAConnectionFactory(String cfClassName)throws Exception;
	public ConnectionFactory getConnectionFactory( ) throws NamingException, JMSException ;
	public Destination getDestination(String destinationName, String destinationType) throws NamingException ;
	public void setDestinationProperties(Destination destination, Map<String,String> destinationProperties) ;
	public void setConnectionProperties(ConnectionFactory connectionFactory, Map<String,String> connectionProperties);
		 
}
