package com.bsc.intg.svcs.core.boot;

import org.apache.cxf.Bus;
import org.apache.cxf.bus.spring.SpringBus;
import org.apache.cxf.feature.LoggingFeature;
import org.apache.cxf.jaxws.EndpointImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class BSCSOAPRuntime {
	
	private static final Logger logger = LoggerFactory.getLogger(BSCServiceConfiguration.class);
	
	String runtime = null ;
	Object runtimeObject = null;
	
	public BSCSOAPRuntime(String runtime, Object cxfBus) {
		this.runtime=runtime;
		this.runtimeObject=cxfBus;
	}
	
	public void registerSOAPEndpoint(String endpointURL, Object seiImpl) throws Exception {
	
			if (runtime.equals("cxf")) {
				
				Bus bus	 = ((SpringBus)runtimeObject);
				
				if(bus !=null ) {
					
					EndpointImpl endpoint = new EndpointImpl(bus, seiImpl);
					endpoint.getFeatures().add(new LoggingFeature());
					endpoint.publish(endpointURL);

				} else {
					throw new Exception("BSC SOAPRuntime is not initialized");
				}
				
			}
	}
}
