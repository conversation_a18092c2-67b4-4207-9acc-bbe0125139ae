<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
	
	<artifactId>bsc-sp8-core</artifactId>
	<packaging>jar</packaging>
	<name>bsc-sp8-core</name>
  
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<maven.compiler.source>1.8</maven.compiler.source>
		<maven.compiler.target>1.8</maven.compiler.target>
	</properties>

	<parent>
		<groupId>com.bsc.intg.svcs</groupId>
		<artifactId>bsc-sp8-framework</artifactId>
		<version>8.0.0</version>
	</parent>
	<build>
	<plugins>
	<plugin>
	  <groupId>org.apache.maven.plugins</groupId>
	  <artifactId>maven-source-plugin</artifactId>
	  <executions>
	    <execution>
	      <id>attach-sources</id>
	      <goals>
	        <goal>jar</goal>
	      </goals>
	    </execution>
	  </executions>
	</plugin>
	
   	<plugin>
	
	    <groupId>pl.project13.maven</groupId>
	    <artifactId>git-commit-id-plugin</artifactId>
	    <version>2.2.4</version>
	    <executions>
	        <execution>
	            <id>get-the-git-infos</id>
	            <goals>
	                <goal>revision</goal>
	            </goals>
	        </execution>
	    </executions>
	    <configuration>
	        <dotGitDirectory>${project.basedir}/.git</dotGitDirectory>
	        <prefix>git</prefix>
	        <verbose>false</verbose>
	        <generateGitPropertiesFile>true</generateGitPropertiesFile>
	        <generateGitPropertiesFilename>src/main/resources/build.framework.info</generateGitPropertiesFilename>
	        <format>json</format>
	        <gitDescribe>
	            <skip>false</skip>
	            <always>false</always>
	            <dirty>-dirty</dirty>
	        </gitDescribe>
	    </configuration>

	</plugin>
	
	</plugins>

</build>

</project>
