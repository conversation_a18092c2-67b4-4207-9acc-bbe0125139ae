package com.bsc.intg.svcs.core.kafka;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCResourceBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;

public class BSCKafkaConnectionResource extends BSCResourceBase {
	
	public String cluster = null ;
	
	protected String connectionName = null;
	
	public BSCKafkaConnectionResource(BSCComponentType componentType, BSCComponent parent, String configName) {
		super(componentType, parent, configName);
	}
	
	public String getCluster() {
		return cluster;
	}
	
	@Override
	public void initialize(String prefix ) throws BSCComponentInitializationException  { 
			
			this.initialize();
		
			logger.info("Initializing the connection group ({})({}) ", prefix, configName);
		
			this.connectionName   =	getNonEmptyProperty("connection.name");
			this.cluster=getNonEmptyGlobalProperty( prefix + "." + connectionName + ".cluster");
			
	}

	@Override
	public void start() throws BSCComponentInitializationException {
		
	}

	@Override
	public void stop() {
		
	}

	@Override
	public void initialize() throws BSCComponentInitializationException {
		super.initialize();
	}


}
