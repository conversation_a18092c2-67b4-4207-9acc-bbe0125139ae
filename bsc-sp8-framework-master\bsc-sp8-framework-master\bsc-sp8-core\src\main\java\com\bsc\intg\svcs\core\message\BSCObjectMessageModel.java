package com.bsc.intg.svcs.core.message;

import java.util.HashMap;
import java.util.Map;

public class BSCObjectMessageModel implements BSCMessageModel {

	protected Map<String,BSCModelSpec> specMap = new HashMap<String,BSCModelSpec>();
	
	@Override
	public void addModel(String name, Class<?> modelClass, Object model,  String[] keys) {
		specMap.put(name, new BSCModelSpec(name,modelClass,model,keys));
	}

	@Override
	public BSCModelSpec getModel(String name) {
		return specMap.get(name);
	}

	@Override
	public Map<String, BSCModelSpec> getModels() {
		return specMap;
	}

}
