package com.bsc.intg.svcs.core.exception;

import org.slf4j.Logger;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.message.BSCMessage;

public class BSCExceptionBase extends Throwable {

	private static final long serialVersionUID = 1L;
	
	protected String 		message		=	null;
	protected BSCComponent  source		=	null;		
	protected BSCMessage 	messageObj	=	null;
	protected Logger 		logger 		= 	null;
	protected BSCErrorLevel level		=   null;
	
	public BSCExceptionBase(BSCComponent source, Throwable ex, BSCErrorLevel l) {
		super(ex);
		this.source		=	source;
		this.message	=	ex.getMessage();
		this.level		=	l;
		
	}
	
	public BSCExceptionBase(BSCComponent source, Throwable ex, BSCErrorLevel l, BSCMessage messageObj) {
		super(ex);
		this.source		=	source;
		this.message	=	ex.getMessage();
		this.messageObj =	messageObj;
		this.level		=	l;
	}
	
	
	public BSCExceptionBase(BSCComponent source,  BSCErrorLevel l,  String message, BSCMessage messageObj) {
		super(message);
		this.source		=	source;
		this.messageObj =	messageObj;
		this.level		=	l;
	}
	
	
	public BSCExceptionBase(BSCComponent source, BSCErrorLevel l, BSCMessage messageObj) {
		super("Exception occcured in the component " + source.getInstanceName());
		this.source		=	source;
		this.messageObj =	messageObj;
		this.level		=	l;
	}
	
	public BSCExceptionBase(BSCComponent source, BSCErrorLevel l, String message) {
		super(message);
		this.source		=   source;
		this.level		=	l;
	}
	
	public BSCExceptionBase(BSCComponent source, Throwable ex, BSCErrorLevel l,  String message) {
		super(message,ex);
		this.source		=   source;
		this.level		=	l;
	}
	
	public BSCExceptionBase(BSCComponent source, Throwable ex, BSCErrorLevel l, String message, BSCMessage messageObj) {
		super(message,ex);
		this.source		=   source;
		this.messageObj =   messageObj;
		this.level		=	l;
	}
	

	public BSCErrorLevel getLevel() {
		return level;
	}

	public String getErrorString() {
		
		String sourceName=null;
		
		if (source != null) {
			sourceName=source.getInstanceName();
		}
		
		return	"Source:" + sourceName +  " Message:" + getLocalizedMessage();	
	}
	
	public BSCErrorModel getErrorObject() {
		String sourceName=null;
		
		if (source!=null) {
			sourceName=source.getInstanceName();
		}
		return new BSCErrorModel(sourceName,  getLocalizedMessage());
	}
	
}
