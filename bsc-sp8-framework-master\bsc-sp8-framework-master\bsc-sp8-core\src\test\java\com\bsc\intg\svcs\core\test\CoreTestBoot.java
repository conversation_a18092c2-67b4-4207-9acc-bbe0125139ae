package com.bsc.intg.svcs.core.test;

import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

import com.bsc.intg.svcs.core.boot.BSCServiceApplication;

@EnableAutoConfiguration(exclude = { DataSourceAutoConfiguration.class,  DataSourceTransactionManagerAutoConfiguration.class })
@Configuration
@PropertySource( name = "bscServiceProperties", value = { "classpath:base.properties", "classpath:services.properties" })
public class CoreTestBoot{
	public static void main(String ...args) {
		BSCServiceApplication.start(args);
	}
}
