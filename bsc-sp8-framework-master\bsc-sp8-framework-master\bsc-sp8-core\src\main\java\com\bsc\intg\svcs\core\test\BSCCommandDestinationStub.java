package com.bsc.intg.svcs.core.test;

import java.io.FileWriter;
import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.Map.Entry;
import com.bsc.intg.svcs.core.util.BSCPropertyHelper;
import com.bsc.intg.svcs.core.BSCComponentSubType;
import java.io.BufferedWriter;
import java.util.concurrent.atomic.AtomicInteger;
import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestinationBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.message.BSCMessage;

public class BSCCommandDestinationStub extends BSCDestinationBase {


	protected String command = null;
	private int goodExit	= 0;

	
	public BSCCommandDestinationStub(BSCComponentType componentType, BSCComponent parentComponent, String configName) {
		super(componentType, parentComponent, configName);
	}
	
	AtomicInteger CmdDstcount = new AtomicInteger(0);
	ArrayList<String> tcList = new ArrayList<String>();

	@Override
	public void initialize() throws BSCComponentInitializationException {
		
		super.initialize();

		logger.info("Initializing Cmd Stub");

		logger.info("Initial Cmd stub destination count - {}", CmdDstcount.get());

		this.command = getProperty("command","");
		this.goodExit = getIntProperty("good.exit",0);
	
	}


	@Override
	public BSCMessage onSend(BSCMessage message) throws BSCDestinationException {
		
		logger.info("Executing onSend method - CMD Stub");

		String serviceStatus = message.getProperty("JMS_CORRELATION_ID", "Up");

		boolean isRestController = message.getMessageOwner().getComponentSubType()
				.equals(BSCComponentSubType.REST_CONTROLLER);
		
		String tcName = message.getProperty("JMS.USER.TestCase");
		if (tcName == null) {
			tcName = message.getProperty("JMS.USER.FileName");
			// Get the filename from the URI
			logger.info("tcName {}", tcName);
			String[] SplitTcName = tcName.split(".", -2);
			logger.info("Test Case Name SplitTcName {}", SplitTcName[0]);
	
			tcName = SplitTcName[0];
			
			logger.info("Test Case Name tcName {}", tcName);
		}
		
		String cmdExitStatus = message.getProperty("JMS.USER.CmdExitStatus");

		if (isRestController) {
			tcName = message.getProperty("HTTP.testcase");
			serviceStatus = message.getProperty("HTTP.servicestatus");
			serviceStatus = message.getProperty("HTTP.servicestatus");
			cmdExitStatus = message.getProperty("HTTP.cmdexitstatus");
		}

		String sep = File.separator;
		String workingDir = System.getProperty("user.dir");
		String outputDir = workingDir + sep + "test" + sep + "output" + sep;

		logger.info("Test case - {}", tcName);
		logger.info("Service status - {}", serviceStatus);
		logger.info("Cmd Exit status - {}", cmdExitStatus);
		logger.info("Output file location - {}", outputDir);
		logger.info("isRestController - {}", isRestController);
		logger.info("isRestController - {}", message);

		boolean matchFound = false;

		for (int idx = 0; idx < tcList.size(); idx++) {
			if (tcList.get(idx).equals(tcName)) {
				matchFound = true;
			}
		}

		if (!matchFound) {
			CmdDstcount.set(0);
			tcList.add(tcName);
		}

		String fileName = configName + "_" + tcName + "_" + String.format("%02d", CmdDstcount.incrementAndGet())
				+ ".txt";
		logger.info("Output file - {} ", fileName);

		String deliveryCount = message.getProperty("JMS.USER.JMSXDeliveryCount");

		logger.info("deliveryCount  - {} ", deliveryCount);

		BSCMessage outMessage = null;
						
		switch (message.getAction()) {
		
		case CMD_FILE:
					
			try {
				
				String command = message.getProperty("CMD.command");
				String args = message.getProperty("CMD.args", "");
				String path = message.getProperty("CMD.path");				
				logger.info("CMD_FILE - Command: {} Args: {} Path: {} ",command, args, path); 	

				// Wait for the command to exit and get error value
				int exitValue =Integer.parseInt(cmdExitStatus);

				outMessage = message.cloneMessage(false);
			
				message.setProperty("CMD.exit.value", String.valueOf(exitValue));
				
				if (!new File(outputDir).exists()) {
					new File(outputDir).mkdir();
				}

				String filePath = outputDir + fileName;

				if (serviceStatus.equals("Down")) {
					if (deliveryCount == null || Integer.parseInt(deliveryCount) < 3) {

						logger.error("CmdDestinationStub is down");
						writeToFile(message.getProperties(), "CmdDestinationStub is down", filePath);
						throw new BSCDestinationException(this, BSCErrorLevel.CRITICAL, "CmdDestinationStub is down");
					}
				} else {
					writeToFile(message.getProperties(), message.toString(), filePath);
				}
				
				// Check exit value and handle bad exit values
				if (exitValue == goodExit) {
					logger.debug(" Command completed successfully");
				}
				else {
					throw new  BSCDestinationException(this, BSCErrorLevel.CRITICAL, "Command failed returning: " + exitValue);
				}
			}
			catch (BSCDestinationException | IOException | BSCMessageException e) {
				logger.error("Failed to process the request" + e.toString());
				message.setException(e);
				throw new BSCDestinationException(this, e, BSCErrorLevel.CRITICAL, "Failed to process the request");
			}		
		break;
			
		case CMD_ONLY:
		//	String args = message.getProperty("CMD.Arguments");
			try {
				
				//Reset cmdExitStatus
				cmdExitStatus = "00";
				
				String command = message.getProperty("CMD.command");
		//		String args = message.getProperty("CMD.args", "");
				String args = message.getProperty("CMD.Arguments");
				String path = message.getProperty("CMD.path");				
				logger.info("CMD_ONLY - Command: {} Args: {} Path: {} ",command, args, path); 	

				// Wait for the command to exit and get error value
				int exitValue =Integer.parseInt(cmdExitStatus);

				outMessage = message.cloneMessage(false);
			
				message.setProperty("CMD.exit.value", String.valueOf(exitValue));
				
				if (!new File(outputDir).exists()) {
					new File(outputDir).mkdir();
				}

				String filePath = outputDir + fileName;

				if ((serviceStatus.equals("Down")) || tcName.contains("DOWN")) {
					if (deliveryCount == null || Integer.parseInt(deliveryCount) < 3) {

						logger.error("CmdDestinationStub is down");
						writeToFile(message.getProperties(), "CmdDestinationStub is down", filePath);
						throw new BSCDestinationException(this, BSCErrorLevel.CRITICAL, "CmdDestinationStub is down");
					}
				} else {
					writeToFile(message.getProperties(), message.toString(), filePath);
				}
				
				// Check exit value and handle bad exit values
				if (exitValue == goodExit) {
					logger.debug(" Command completed successfully");
				}
				else {
					throw new  BSCDestinationException(this, BSCErrorLevel.CRITICAL, "Command failed returning: " + exitValue);
				}
			}
			catch (BSCDestinationException | IOException | BSCMessageException e) {
				logger.error("Failed to process the request" + e.toString());
				message.setException(e);
				throw new BSCDestinationException(this, e, BSCErrorLevel.CRITICAL, "Failed to process the request");
			}		
		break;

		default:
			break;
		}
		
		return outMessage;
	}

public void writeToFile(Map<String, Object> props, String data, String filePath) throws IOException {

	BufferedWriter writer = new BufferedWriter(new FileWriter(filePath));
	Map<String, Object> httpHeaders = (Map<String, Object>) BSCPropertyHelper.getPropertiesStartingWith(props,
			"HTTP", false);
	for (Entry<String, Object> p : httpHeaders.entrySet()) {
		writer.write(p + "\n");
	}
	Map<String, Object> jmsHeaders = (Map<String, Object>) BSCPropertyHelper.getPropertiesStartingWith(props, "JMS",
			false);
	for (Entry<String, Object> p : jmsHeaders.entrySet()) {
		writer.write(p + "\n");
	}
	
	writer.write(data + "\n");
	writer.close();

}

	
	@Override
	public List<BSCMessage> onSend(List<BSCMessage> message) throws BSCDestinationException {
		return null;
	}

}
