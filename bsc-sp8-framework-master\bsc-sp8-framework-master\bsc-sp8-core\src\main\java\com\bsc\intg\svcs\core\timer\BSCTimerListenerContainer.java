package com.bsc.intg.svcs.core.timer;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;
import java.util.UUID;

import org.slf4j.Logger;

import com.bsc.intg.svcs.core.boot.BSCScheduler;
import com.bsc.intg.svcs.core.boot.BSCSchedulerRuntime;
import com.bsc.intg.svcs.core.boot.BSCSchedulerTask;
import com.bsc.intg.svcs.core.locking.LockService;
import com.bsc.intg.svcs.core.locking.LockServiceFactory;
import com.bsc.intg.svcs.core.locking.ServiceLock;
import com.bsc.intg.svcs.core.locking.ServiceLockException;

public class BSCTimerListenerContainer implements BSCSchedulerTask  {

	private 	Logger 				logger 						= null;
	private 	BSCTimerListener	timerListener 				=  null;
	private 	String 				cron 						= null;
	private 	String 				message						= null;
	private		boolean 			running 					= false;
	protected 	BSCScheduler 		scheduler					= null;	
	private 	DateFormat 			formatter 					= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
	private 	boolean				lock						= false;
	private 	String				lockDir						= null;
	private 	int					lockTimeOut					= 0;
	
	protected 	LockService			lockService					= null;
	
	protected 	ServiceLock			svcsLock					= null;
	
	
	
	public BSCTimerListenerContainer(Logger logger) {
		this.logger = logger;
	}

	public Logger getLogger() {
		return logger;
	}
	
	public void setTimerListener(BSCTimerListener timerListener) {
		this.timerListener = timerListener;
	}
	
	public void initialize() throws Throwable {

		logger.debug("Initializing Timer Listener Container " + this.hashCode());
		
		BSCSchedulerRuntime sr 		= ((BSCSchedulerRuntime)timerListener.getServiceContainer().getApplicationService().getApplicationServiceConfig().getRuntimeService(timerListener.getProperty("scheduler.run.time","quartz")));
		scheduler					=sr.getScheduler();
		scheduler.scheduleTask(timerListener.getConfigName(), timerListener.getServiceContainer().getConfigName(), cron, this, logger);
		formatter.setTimeZone(TimeZone.getTimeZone("UTC"));
		
		if(isLock()){
			initializeLock();
		}
	}

	public void process() {

		logger.debug("Timer triggered ");

		try {
			
			if(isLock()){
				logger.info("Accquiring lock before processing");
				lock();
			}
			
			
			BSCTimerMessage timerMessage = new BSCTimerMessage();
			timerMessage.write(this.getMessage().getBytes());
			UUID uuid = UUID.randomUUID();
			timerMessage.setProperty("TIMER.MESSAGEID", uuid.toString());			
			timerMessage.setProperty("TIMER.MESSAGETIMESTAMP", formatter.format(new Date()));
			
			timerListener.onMessage(timerMessage);
			
			
		} catch (Throwable e) {
			logger.error("Failure occurred in timer cycle", e);
			throw new RuntimeException(e);			
		
		} finally {
			if(isLock()){
				try {
					logger.info("Releasing lock after processing is done");
					unlock();
				} catch (Exception e) {
					logger.error("Error releasing lock. ",e);
					e.printStackTrace();
				}
			}
			
		}
		
		
	}
	
	public void start() throws Exception {
		scheduler.start();
	}

	public void stop() throws Exception {
		if (scheduler != null) {
			scheduler.stop();
		}
	}

	public String getCron() {
		return cron;
	}

	public void setCron(String cron) {
		this.cron = cron;
	}
	
	public boolean isRunning() {
		return running;
	}

	public void setRunning(boolean running) {
		this.running = running;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public BSCScheduler getScheduler() {
		return scheduler;
	}

	public void setScheduler(BSCScheduler scheduler) {
		this.scheduler = scheduler;
	}

	public boolean isLock() {
		return lock;
	}

	public void setLock(boolean lock) {
		this.lock = lock;
	}

	public String getLockDir() {
		return lockDir;
	}

	public void setLockDir(String lockDir) {
		this.lockDir = lockDir;
	}

	public int getLockTimeOut() {
		return lockTimeOut;
	}

	public void setLockTimeOut(int lockTimeOut) {
		this.lockTimeOut = lockTimeOut;
	}

	public BSCTimerListener getTimerListener() {
		return timerListener;
	}

	public void setLogger(Logger logger) {
		this.logger = logger;
	}
	
	protected void lock() throws ServiceLockException {

		logger.debug( " Locking");

		svcsLock = lockService.acquire(lockTimeOut,this.getLockDir());
		
		logger.debug( " Locked at  {}", svcsLock.toString());
	}


	protected boolean unlock() throws Exception {

		if (svcsLock != null) {

			logger.debug("Removing lock:  {}", svcsLock.toString());

			lockService.release(svcsLock);

			logger.debug(" Unlocked");

			svcsLock = null;

			return true;

		} else {
			return false;
		}

	}
	
	/**
	 * initializes the lock directory.
	 * 
	 */
	protected void initializeLock() throws Throwable {
		
		lockService = LockServiceFactory.getDefaultLockService();

	}

}
