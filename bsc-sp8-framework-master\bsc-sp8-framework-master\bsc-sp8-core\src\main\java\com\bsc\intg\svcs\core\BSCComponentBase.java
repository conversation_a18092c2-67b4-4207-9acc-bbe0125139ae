package com.bsc.intg.svcs.core;

import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.Marker;
import org.slf4j.MarkerFactory;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.ConfigurableEnvironment;

import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.util.BSCPropertyHelper;

public abstract class BSCComponentBase implements BSCComponent {
	
	protected String 									configName 			= null;
	
	protected String 									instanceName 		= null;
	
	protected String 									objectName  		= null;
	
	protected  Map<String,Object>  						properties			= new HashMap<String,Object>();
	
	
	protected ConfigurableEnvironment 					globalEnvironment	= null;

	protected ConfigurableApplicationContext			configAppContext	= null;
	
	protected  Logger 									logger 				= null;
	
	protected  BSCComponent 							parentComponent		= null;
	
	protected BSCComponentType							componentType		= null;
	
	protected BSCComponentSubType						componentSubType	= null;
	
	protected String  									configPath		    = null;
	
	protected static Marker SMTP_TRIGGER 												= MarkerFactory.getMarker("SMTP_TRIGGER");
	
	BSCComponentBase(BSCComponentType componentType , Map<String,Object> runtime, String configName) {
		
		this.componentType = componentType;
		
		configAppContext = (ConfigurableApplicationContext) runtime.get("configAppContext");
		
		this.globalEnvironment 	=	configAppContext.getEnvironment();
		this.configName			=	configName;
		this.instanceName		=	configName;
		this.configPath			=   configName;
		this.logger				=	LoggerFactory.getLogger(this.instanceName);
		

		Map<String,Object>  baseProperties	=	BSCPropertyHelper.getPropertiesStartingWith(this.globalEnvironment, componentType.name()+".", true);
		Map<String,Object>  configProperties = BSCPropertyHelper.getPropertiesStartingWith(this.globalEnvironment, configName +".", true);
		
		BSCPropertyHelper.addProperties(this.properties, baseProperties, true);
		BSCPropertyHelper.addProperties(this.properties, configProperties, true);
		
		printProperties();
	
	}

	public void printProperties() {

		StringBuffer propertyBuffer = new StringBuffer();
		propertyBuffer.append("\n");
		for (Entry<String, Object> entry : properties.entrySet()) 	{
			propertyBuffer.append("\n");
			propertyBuffer.append(entry.getKey());
			propertyBuffer.append("=");
			propertyBuffer.append(entry.getValue());
		}
		propertyBuffer.append("\n");
		logger.info("{} Properties  : {}", this.getInstanceName() , propertyBuffer.toString());
	
	}
	
	public void loadParentProperties() {

		logger.info("Loading properties from Parent Component {}", parentComponent.getConfigName() );

		Map<String,Object>  overrideProperties = BSCPropertyHelper.getPropertiesStartingWith(this.globalEnvironment, parentComponent.getConfigName() + "." + this.getConfigName()+"." , true);
		
		BSCPropertyHelper.addProperties(this.getProperties(), overrideProperties, true);
		
		printProperties();
	}

	BSCComponentBase(BSCComponentType componentType , BSCComponent parentComponent, String configName, String instance) {
		
		this.componentType = componentType;
		this.parentComponent=parentComponent;
		configAppContext = parentComponent.getConfigAppContext();
		
		this.globalEnvironment 	=	configAppContext.getEnvironment();
		this.configName			=	configName;
		this.instanceName		=	configName + "." + instance;
		this.configPath			=   parentComponent.getConfigPath() + "." + configName + "." + instance;
		this.logger				=	LoggerFactory.getLogger(this.getConfigPath());
			
		Map<String,Object>  baseProperties	=	BSCPropertyHelper.getPropertiesStartingWith(this.globalEnvironment, componentType.name()+".", true);
		Map<String,Object>  configProperties = BSCPropertyHelper.getPropertiesStartingWith(this.globalEnvironment, configName+".", true);
		
		BSCPropertyHelper.addProperties(this.properties, baseProperties, true);
		BSCPropertyHelper.addProperties(this.properties, configProperties, true);
		
		printProperties();
		
	}
	
	protected BSCComponentBase(BSCComponentType componentType , BSCComponent parentComponent, String configName) {
		
		this.componentType = componentType;
		this.parentComponent=parentComponent;
		configAppContext = parentComponent.getConfigAppContext();
		
		this.globalEnvironment 	=	configAppContext.getEnvironment();
		this.configName			=	configName;
		this.instanceName		=	configName;
		this.configPath			=   parentComponent.getConfigPath() + "." + configName ;
		this.logger				=	LoggerFactory.getLogger(this.getConfigPath());

		
		Map<String,Object>  baseProperties	=	BSCPropertyHelper.getPropertiesStartingWith(this.globalEnvironment, componentType.name()+".", true);
		Map<String,Object>  configProperties = BSCPropertyHelper.getPropertiesStartingWith(this.globalEnvironment, configName+".", true);
		
		BSCPropertyHelper.addProperties(this.properties, baseProperties, true);
		BSCPropertyHelper.addProperties(this.properties, configProperties, true);
		
		printProperties();
	}

	public String getConfigPath() {
		return configPath;
	}


	public BSCComponentType getComponentType() {
		return componentType;
	}
	

	public String getConfigName() {
		return configName;
	}

	public String getInstanceName() {
		return instanceName;
	}

	public ConfigurableEnvironment getGlobalEnvironment() {
		return globalEnvironment;
	}


	public ConfigurableApplicationContext getConfigAppContext() {
		return configAppContext;
	}
	
	@Override
	public Logger getLogger() {
		return logger;
	}
	
	
	

	public BSCComponentSubType getComponentSubType() {
		return componentSubType;
	}

	public void setComponentSubType(BSCComponentSubType componentSubType) {
		this.componentSubType = componentSubType;
	}

	public void setProperty(String key, String value) {
		properties.put(key, (String) value);
	}
	
	public String getProperty(String key) {
		return (String)properties.get((String)key);
	}
	
	public String getNonEmptyProperty(String key) throws BSCComponentInitializationException {
		
		String value = (String)properties.get((String)key);
		
		if( value == null || value.isEmpty()) {
			throw new BSCComponentInitializationException(this, BSCErrorLevel.FATAL, "Invalid value " + value + " for the property name " + key );
		}
		return value;
		
	}

	public int getIntProperty(String key) throws BSCComponentInitializationException  {
		String value = (String)properties.get((String)key);
		try {
			return Integer.parseInt(value);
		} catch(NumberFormatException e) {
			throw new BSCComponentInitializationException(this, BSCErrorLevel.FATAL, "Invalid integer value " + value + " for the property name " + key );	
		}
	}
	
	public int getIntProperty(String key,int defaultValue) throws BSCComponentInitializationException{
		
		String value = (String)properties.get((String)key);
		
		if( value == null || value.isEmpty()) {
			return defaultValue;
		}
		
		try {
			return Integer.parseInt(value);
		} catch(NumberFormatException e) {
			throw new BSCComponentInitializationException(this, BSCErrorLevel.FATAL, "Invalid integer value " + value + " for the property name " + key );	
		}
	}

	public boolean getBooleanProperty(String key) throws BSCComponentInitializationException  {
		String value = (String)properties.get((String)key);
		if(value.equals("true") || value.equals("false"))
			return Boolean.parseBoolean(value);
		else
			throw new BSCComponentInitializationException(this, BSCErrorLevel.FATAL, "Invalid boolean value " + value + " for the property name " + key );	
		
	}
	
	public boolean getBooleanProperty(String key,boolean defaultValue) throws BSCComponentInitializationException{
		
		String value = (String)properties.get((String)key);
		
		if( value == null || value.isEmpty()) {
			return defaultValue;
		}
		
		if(value.equals("true") || value.equals("false"))
			return Boolean.parseBoolean(value);
		else
			throw new BSCComponentInitializationException(this, BSCErrorLevel.FATAL, "Invalid boolean value " + value + " for the property name " + key );	

	}

	
	
	public String getProperty(String key,String defaultValue) {
		return (String)properties.getOrDefault(key, defaultValue);
	}
	
	
	public String getGlobalProperty(String key) {
		return globalEnvironment.getProperty(key);
	}
	
	public String getGlobalProperty(String key,String defaultValue) {
		return globalEnvironment.getProperty(key,defaultValue);
	}
	
	
	
	
	
public String getNonEmptyGlobalProperty(String key) throws BSCComponentInitializationException {
		
		String value = globalEnvironment.getProperty(key);
		
		if( value == null || value.isEmpty()) {
			throw new BSCComponentInitializationException(this, BSCErrorLevel.FATAL, "Invalid value " + value + " for the property name " + key );
		}
		return value;
		
	}

	public int getIntGlobalProperty(String key) throws BSCComponentInitializationException  {
		String value =globalEnvironment.getProperty(key);
		try {
			return Integer.parseInt(value);
		} catch(NumberFormatException e) {
			throw new BSCComponentInitializationException(this, BSCErrorLevel.FATAL, "Invalid integer value " + value + " for the property name " + key );	
		}
	}
	
	public int getIntGlobalProperty(String key,int defaultValue) throws BSCComponentInitializationException{
		
		String value = globalEnvironment.getProperty(key);
		
		if( value == null || value.isEmpty()) {
			return defaultValue;
		}
		
		try {
			return Integer.parseInt(value);
		} catch(NumberFormatException e) {
			throw new BSCComponentInitializationException(this, BSCErrorLevel.FATAL, "Invalid integer value " + value + " for the property name " + key );	
		}
	}

	public boolean getBooleanGlobalProperty(String key) throws BSCComponentInitializationException  {
		String value = globalEnvironment.getProperty(key);
		if(value.equals("true") || value.equals("false"))
			return Boolean.parseBoolean(value);
		else
			throw new BSCComponentInitializationException(this, BSCErrorLevel.FATAL, "Invalid boolean value " + value + " for the property name " + key );	
		
	}
	
	public boolean getBooleanGlobalProperty(String key,boolean defaultValue) throws BSCComponentInitializationException{
		
		String value = globalEnvironment.getProperty(key);
		
		if( value == null || value.isEmpty()) {
			return defaultValue;
		}
		
		if(value.equals("true") || value.equals("false"))
			return Boolean.parseBoolean(value);
		else
			throw new BSCComponentInitializationException(this, BSCErrorLevel.FATAL, "Invalid boolean value " + value + " for the property name " + key );	

	}

	
	
	@Override
	public Map<String,Object> getProperties() {
		return properties;
	}

	@Override
	public void start() throws BSCComponentInitializationException {
		
	}

	@Override
	public void stop() {
		
	}


}
