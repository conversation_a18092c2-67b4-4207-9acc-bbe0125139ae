package com.bsc.intg.svcs.core.common.services;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.vfs2.FileSystemException;
import org.apache.commons.vfs2.FileSystemOptions;

import com.bsc.intg.svcs.core.BSCComponent;
import com.bsc.intg.svcs.core.BSCComponentType;
import com.bsc.intg.svcs.core.BSCDestination;
import com.bsc.intg.svcs.core.BSCIntegrationServiceBase;
import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCDestinationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCStringUtils;
import com.bsc.intg.svcs.core.vfs.BSCDefaultVFSProvider;
import com.bsc.intg.svcs.core.vfs.BSCVFSObjectInfo;
import com.bsc.intg.svcs.core.vfs.BSCVFSProvider;



public class BSCVFS2VFSService extends BSCIntegrationServiceBase {


	
	protected Map<String, String> 	destRouteMap     	= new HashMap<String, String>();
	private String 					inEncoding 			= null;
	private String 					outEncoding 		= null;
	private String 					outBOM	 			= null;
	private String 				triggerSuffix	 		= "";
	private String 				deliveryType	 		= "";
	private boolean 				onTriggerBatch	 		= false;
	private boolean 				setTrigger	 		= false;
	private String delimiter = null;
	private String header = "";
	private String footer = "";
	List<BSCDestination> 	destList 			= new ArrayList<BSCDestination>();
	private int outMessageSize;
	
	public BSCVFS2VFSService(BSCComponentType componentType, BSCComponent parentComponent, String configName,
			String instance) {
		super(componentType, parentComponent, configName, instance);
	}


	@Override
	public BSCMessage service(BSCMessage inMessage) throws BSCServiceException {
		logger.debug("Executing FxferService service");
		
		BSCMessage outMessage = null;
		
		
		
		try {
			
			
			List<String> eventFileList = new ArrayList<String>();
			
			String destRouteKey = inMessage.getProperty("destination.route", "");
			
			
			String destNames = destRouteMap.get(destRouteKey);
			logger.info("Destination route name :"+destNames);
	        if(destNames!=null && !destNames.isEmpty()){
	        	destList 			= new ArrayList<BSCDestination>();
	        	ArrayList<String> destArrayList  = BSCStringUtils.convertCommaSeperated(destNames);
	        	for (String destObjName: destArrayList ) {
	        		destList.add(this.getDestination(destObjName));
	        	}
	        }
	        
	        switch (deliveryType) {
	        case "batch":  
	        	BSCMessage batchmsg = inMessage.cloneMessage(false);
	        	for(BSCDestination publish : destList){
	        		if(batchmsg.getProperty("VFS.BATCH.COMPLETE").equals("true")&& setTrigger)
	        			publish.setProperty("generate.trigger", "true");
	        		
	        		deliverMessage(batchmsg,outMessage,publish,eventFileList);
	        		publish.setProperty("generate.trigger", "false");
	        	}
	        	
	        	break;
	        
	        case "onTrigger": 
	        	outMessage = ontrigger(inMessage,eventFileList);
//	        	triggerService = this.getServiceContainer().getService("TriggerService");
//	        	triggerService.execute(inMessage);
	        	break;
				
	        
			case "split":
				BSCMessage splitMessage = inMessage.cloneMessage(false);
				logger.debug("Trigger file based processing started in batch mode");
				String[] matches = inMessage.getStringMessage().split(delimiter);
				for (BSCDestination publish : destList) {
					int messageCounter = 1;
					StringJoiner outJoiner = new StringJoiner(delimiter);
					for (String msg : matches) {
						outJoiner.add(msg);
						if ((messageCounter % outMessageSize) == 0) {
							splitMessage.reset();
							String outMsg = header + outJoiner.toString() + footer;
							splitMessage.loadMessage(outMsg);
							deliverMessage(splitMessage, outMessage, publish, eventFileList);
							outJoiner = new StringJoiner(delimiter);
						}
						messageCounter++;
					}
					if (outJoiner.length() > 0) {
						splitMessage.reset();
						String outMsg = header + outJoiner.toString() + footer;
						splitMessage.loadMessage(outMsg);
						deliverMessage(splitMessage, outMessage, publish, eventFileList);
					}
				}
				break;
	        	
			default:
				for(BSCDestination publish : destList){
	        	deliverMessage(inMessage,outMessage,publish,eventFileList);
				}
	        }
				
			outMessage = inMessage.cloneMessage(false);
			outMessage.setProperty("include.properties.prefix", inMessage.getMessageOwner().getConfigName() + ".Meta./" );
			outMessage.loadMessage(eventFileList.stream().map(x -> x).collect(Collectors.joining(" \n ")));
			
		} catch ( BSCMessageException | Exception e ) {
			throw new BSCServiceException(this,e,BSCErrorLevel.ROLLBACK,"Failed to execute the app service");
		}catch(BSCDestinationException e){
			if(e.getLevel()==BSCErrorLevel.ROLLBACK){
				throw new BSCServiceException(this,e,BSCErrorLevel.ROLLBACK,"Failed to execute the app service");
			}else{
				throw new BSCServiceException(this,e,BSCErrorLevel.CRITICAL,"Failed to execute the app service");
			}
		}
		
		return outMessage;
	}

	
	@Override
	public void initialize() throws BSCComponentInitializationException {

		super.initialize();
			
		valid						=	true;
		inEncoding 					= 	this.getProperty("input.encoding",null);
		outEncoding					= 	this.getProperty("output.encoding",null);
		outBOM						= 	this.getProperty("output.bom","");
		String destRouteMapProp 	= 	this.getProperty("destination.route.map","");
		deliveryType					= this.getProperty("delivery.type","");
		onTriggerBatch 						= this.getBooleanProperty("onTrigger.batch",false);
		triggerSuffix 				= this.getProperty("trigger.file.suffix",".done");
		setTrigger 						= this.getBooleanProperty("set.trigger",false);
		
		if (!destRouteMapProp.isEmpty()){
	           
            String[] destAr = destRouteMapProp.split(";");
            for(String destObject: destAr){
                String destKey = destObject.split("\\:")[0];
                String destValue = destObject.split("\\:")[1];
                destRouteMap.put(destKey, destValue);
            }
        }
		else{
			String destinations 	= 	this.getProperty("destinations");
			List<String> destList 	= 	Arrays.asList(destinations.split("\\s*,\\s*"));

			for(String dest : destList){			
				this.destList.add(this.getDestination(dest));
			}

		}
		
		delimiter = this.getProperty("delimiter",",");
		header=this.getProperty("header","");
		footer=this.getProperty("footer","");
		outMessageSize = this.getIntProperty("outmessage.size", 1);
		
		
	}
	
	
	
	private void deliverMessage(BSCMessage inMessage,BSCMessage outMessage,BSCDestination publish,List<String> eventFileList) throws BSCMessageException, BSCDestinationException, BSCServiceException{
		
		String fileName = inMessage.getProperty("VFS.FILE.NAME");
			logger.info("Delivery intiated for file {} to destination {} ", fileName,publish.getConfigName());
			
			//Encoding Logic
			if(inEncoding != null && outEncoding != null){
				boolean outBOMFlag = false;
				String mOutEncoding = outEncoding;
				
				if(outEncoding.endsWith("-BOM")){
					mOutEncoding = outEncoding.substring(0,outEncoding.indexOf("-BOM"));
					outBOMFlag = true;
				}
				if(Charset.isSupported(inEncoding) && Charset.isSupported(mOutEncoding)){
					logger.info("File encoding is changed from {} to {} ", inEncoding, outEncoding);
					
					try {
						String inEncMsg = inMessage.toString(inEncoding);
						
						byte[] bomBytes = null,combinedBytes = null;
						
						bomBytes = outBOM.getBytes();
						if(outBOMFlag &&  bomBytes != null){								
							logger.debug("Byte Order Mark needs to be added to ouput file for encoding {} and BOM {}",outEncoding, bomBytes);
							combinedBytes = ArrayUtils.addAll(bomBytes, inEncMsg.getBytes(mOutEncoding));
						}else{
							combinedBytes = inEncMsg.getBytes(mOutEncoding);
						}							
						
						inMessage.reset();
						inMessage.write(combinedBytes);
					} catch (IOException e) {
						throw new BSCServiceException(this,e,BSCErrorLevel.ROLLBACK,"Enocding not supported & Error Description: "+e.getMessage());
					}
				}else{
					throw new BSCServiceException(this,BSCErrorLevel.ROLLBACK,"Enocding not supported");
				}
			}
			
			outMessage = publish.send(inMessage);
			logger.info("Delivered file {} to destination {} ", fileName,publish.getConfigName());
			if(outMessage!=null)
				eventFileList.add(outMessage.getStringMessage());
	}
	
	private BSCMessage ontrigger(BSCMessage inMessage,List<String> eventFileList) throws BSCServiceException {
		logger.info("Executing onTrigger messages");
		
		BSCMessage outMessage = null;
		BSCVFSProvider vfs = null;
		BSCComponent listener = null;
		FileSystemOptions options = null;
		List<BSCVFSObjectInfo> matchedFiles = null;
		List<BSCVFSObjectInfo> tmpMatchedFiles = null;

		String triggerFileName = inMessage.getProperty("VFS.FILE.NAME");
		String fileURL = inMessage.getProperty("VFS.FILE.URL");
		String fileURI = fileURL.substring(0, fileURL.lastIndexOf("/") + 1);
		try {
			listener = inMessage.getMessageOwner();
			vfs = new BSCDefaultVFSProvider(logger, (String)  listener.getProperty("protocol"), listener);
			vfs.init();
			options = BSCDefaultVFSProvider.createFileSystemOptions(listener.getProperty("protocol", "file"), listener);
			
			//Trigger File initiates delivery of multiple files
			if (onTriggerBatch) {
				logger.debug("Trigger file based processing started in batch mode");
				tmpMatchedFiles = vfs.listFiles(fileURI, options);
				int publishSize = destList.size();
				int destCtr=0;
				for(BSCDestination publish : destList){	
					matchedFiles = new ArrayList<BSCVFSObjectInfo>(tmpMatchedFiles);
					destCtr++;
					for (Iterator<BSCVFSObjectInfo> fileItr = matchedFiles.iterator(); fileItr.hasNext();) {
						BSCVFSObjectInfo file = fileItr.next();
						String fileName = file.getFileName();
						//Filter out Trigger file from files to be delivered
						if (!fileName.contains(triggerFileName)) {
							fileURL = fileURI + fileName;
							inMessage.setProperty("VFS.FILE.URL", fileURL);
							inMessage.setProperty("VFS.FILE.NAME", fileName);
							if(!fileItr.hasNext()&& setTrigger)
			        			publish.setProperty("generate.trigger", "true");
							logger.info("Delivery intiated for file {} to destination {} ", fileName,publish.getConfigName());
							outMessage = publish.send(inMessage);	
							logger.info("Delivered file {} to destination {} ", fileName,publish.getConfigName());
							if(destCtr == publishSize)
								vfs.delete(fileURL, options);
							eventFileList.add(outMessage.getStringMessage());
						}	
						
						fileItr.remove();
					}
				}
			} else {
				//Trigger File initiates delivery of single file with filename part of trigger file
				logger.debug("Trigger file based processing started in non-batch mode");
				String fileName = triggerFileName.substring(0, triggerFileName.lastIndexOf(triggerSuffix));
				fileURL = fileURL.substring(0, fileURL.lastIndexOf(triggerFileName)) + fileName;
				inMessage.setProperty("VFS.FILE.URL", fileURL);
				inMessage.setProperty("VFS.FILE.NAME", fileName);
				
				for(BSCDestination publish : destList){
					logger.info("Delivery intiated for file {} to destination {} ", fileName,publish.getConfigName());
					outMessage = publish.send(inMessage);	
					logger.info("Delivered file {} to destination {} ", fileName,publish.getConfigName());
					vfs.delete(fileURL, options);
					eventFileList.add(outMessage.getStringMessage());
				}
			}

//			outMessage = inMessage.cloneMessage(false);
//			outMessage.setProperty("include.properties.prefix", inMessage.getMessageOwner().getConfigName() + ".Meta./" ); 
//			outMessage.loadMessage(eventFileList.stream().map(x -> x).collect(Collectors.joining(" \n ")));

		} catch (BSCDestinationException | BSCComponentInitializationException | Exception | BSCMessageException e) {
			throw new BSCServiceException(this, e, BSCErrorLevel.ROLLBACK, "Failed to deliver onTrigger files.");
		} finally {
			if (vfs != null) {
				try {
					vfs.close();
				} catch (FileSystemException e) {
					e.printStackTrace();
				}
			}
		}

		return outMessage;
	}

	
}
