package com.bsc.intg.svcs.core.boot;

import java.io.InputStream;

import com.bsc.intg.svcs.core.util.BSCStringUtils;



public class BSCBuildInfo {
	
	public final static int FRAMEWORK_VERSION=8; 
	
	public static void buildInfo() {
	
		InputStream framework = BSCBuildInfo.class.getClassLoader().getResourceAsStream("build.framework.info");
		
		if (framework != null) {
			System.out.println("SP8 Framework Build Info");
			System.out.println(BSCStringUtils.getStringFromInputStream(framework));
		} else {
			System.out.println("SP8 Framework Build Info is missing");
		}
		InputStream service = BSCBuildInfo.class.getClassLoader().getResourceAsStream("build.service.info");

		if (service !=null ) {
			System.out.println("SP8 Service Build Info");
			System.out.println(BSCStringUtils.getStringFromInputStream(service));
		} else {
			System.out.println("SP8 Service Build Info is missing");
		}
		
		InputStream testReport = BSCBuildInfo.class.getClassLoader().getResourceAsStream("bsc-service-test-summary.txt");
		if (testReport !=null ) {
			System.out.println("SP8 Test Report Summary");
			System.out.println(BSCStringUtils.getStringFromInputStream(testReport));
		} else {
			System.out.println("SP8 Test Report Summary is missing");
		}
	}
	
	
}
