package com.bsc.intg.svcs.core.zookeeper;

import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.CuratorFrameworkFactory;
import org.apache.curator.framework.state.ConnectionState;
import org.apache.curator.framework.state.ConnectionStateListener;
import org.apache.curator.retry.ExponentialBackoffRetry;
import org.apache.curator.utils.CloseableUtils;
import org.slf4j.LoggerFactory;
import ch.qos.logback.classic.Level;

public class ZooKeeperService{
	
	private  static CuratorFramework client = null;
	private  static boolean started = false;
	private  static ConnectionState connState;


	protected ZooKeeperService(){}
		static {
			
			switch(System.getProperty("lock.service.loglevel", "OFF")) {
				
				case "OFF":
					((ch.qos.logback.classic.Logger)LoggerFactory.getLogger("org.apache.zookeeper")).setLevel(Level.OFF);
					((ch.qos.logback.classic.Logger)LoggerFactory.getLogger("org.apache.curator")).setLevel(Level.OFF);
					break;
	
				case "INFO":
					((ch.qos.logback.classic.Logger)LoggerFactory.getLogger("org.apache.zookeeper")).setLevel(Level.INFO);
					((ch.qos.logback.classic.Logger)LoggerFactory.getLogger("org.apache.curator")).setLevel(Level.INFO);
					break;
					
				case "DEBUG":
					((ch.qos.logback.classic.Logger)LoggerFactory.getLogger("org.apache.zookeeper")).setLevel(Level.DEBUG);
					((ch.qos.logback.classic.Logger)LoggerFactory.getLogger("org.apache.curator")).setLevel(Level.DEBUG);
					break;
				
				default:
					((ch.qos.logback.classic.Logger)LoggerFactory.getLogger("org.apache.zookeeper")).setLevel(Level.OFF);
					((ch.qos.logback.classic.Logger)LoggerFactory.getLogger("org.apache.curator")).setLevel(Level.OFF);
					break;
			}
			
			client = CuratorFrameworkFactory.newClient(System.getProperty("zookeeper.ensemble"), new ExponentialBackoffRetry(1000, 3));
			
			client.start();
			client.getConnectionStateListenable().addListener(new ConnectionStateListener() {
	            @Override
	            public void stateChanged(CuratorFramework client, ConnectionState newState) {
	            	connState=newState;
	            }
	        });
			System.out.println("Successfully started ZooKeeper service");

			started=true;
		}
		
	
	/**
	 * Closes connections to Zookeeper and shuts down local monitoring threads
	 * 
	 */
	
	public static void shutdown() {
		if(started) {
			System.out.println("Shutting down the ZooKeeper service");
			CloseableUtils.closeQuietly(client);
		}
	}
	
	/**
	 * Allows you to specify the logging level for Zookeeper/Curator frameworks
	 * 
	 * @param level
	 *         	java.lang.String
	 */
	public static void setLogLevel(String level) {
	
		switch (level) {
			
			case "OFF":
				((ch.qos.logback.classic.Logger)LoggerFactory.getLogger("org.apache.zookeeper")).setLevel(Level.OFF);
				((ch.qos.logback.classic.Logger)LoggerFactory.getLogger("org.apache.curator")).setLevel(Level.OFF);
				break;

			case "INFO":
				((ch.qos.logback.classic.Logger)LoggerFactory.getLogger("org.apache.zookeeper")).setLevel(Level.INFO);
				((ch.qos.logback.classic.Logger)LoggerFactory.getLogger("org.apache.curator")).setLevel(Level.INFO);
				
				break;
				
			case "DEBUG":
				((ch.qos.logback.classic.Logger)LoggerFactory.getLogger("org.apache.zookeeper")).setLevel(Level.DEBUG);
				((ch.qos.logback.classic.Logger)LoggerFactory.getLogger("org.apache.curator")).setLevel(Level.DEBUG);
				break;
			
			default:
				((ch.qos.logback.classic.Logger)LoggerFactory.getLogger("org.apache.zookeeper")).setLevel(Level.OFF);
				((ch.qos.logback.classic.Logger)LoggerFactory.getLogger("org.apache.curator")).setLevel(Level.OFF);
				break;
		}
		
	}
	
	/**
	 * Get static instance of the CuratorFramework client
	 * 
	 */
	public static CuratorFramework getClient() {
		return client;
	}
	
	/**
	 * Get status of the zookeeper service
	 * 
	 */
	public static boolean getStatus() {
		return started;
	}
	
	public static ConnectionState getConnState() {
		return connState;
	}



}
