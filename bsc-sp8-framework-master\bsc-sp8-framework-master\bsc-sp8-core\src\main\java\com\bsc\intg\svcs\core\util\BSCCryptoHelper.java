package com.bsc.intg.svcs.core.util;

import java.io.File;
import java.io.IOException;
import java.security.GeneralSecurityException;

import com.bsc.intg.svcs.core.boot.BSCBuildInfo;
import com.bsc.intg.svcs.crypto.CryptoUtils;
import com.bsc.intg.svcs.crypto.SecretStorage;

public class BSCCryptoHelper {
	
	@SuppressWarnings("restriction")
	public static String decipher(String cypherText) throws GeneralSecurityException, IOException {
		
		  String keyFilePath=System.getenv("SP"+String.valueOf(BSCBuildInfo.FRAMEWORK_VERSION)+"_KEY_FILE_PATH");
		  String keyAlg=System.getenv("SP"+String.valueOf(BSCBuildInfo.FRAMEWORK_VERSION)+"_KEY_ALG");
		  
		  File keyFile = new File(keyFilePath);
		  return CryptoUtils.decrypt(cypherText, keyAlg, keyFile);
		
	}
	
	@SuppressWarnings("restriction")
	public static String getSecret(String secretKeyURL) throws Exception {
		
		
			if (secretKeyURL.startsWith("ss://")) {
			
				String settingsFile=System.getProperty("secret.storage.settings",System.getenv("SECRET_STORAGE_SETTINGS"));
				return  SecretStorage.getSecretStorage().loadSettings(settingsFile).getSecret(secretKeyURL);
			
			} else if (secretKeyURL.startsWith("enc:")) {
				
				  String keyFilePath=System.getenv("SP_KEY_FILE_PATH");
				  String keyAlg=System.getenv("SP_KEY_ALG");
				  File keyFile = new File(keyFilePath);
				  return CryptoUtils.decrypt(secretKeyURL, keyAlg, keyFile);
				  
			} else {
				return secretKeyURL;
			}
	}
	

}
