package com.bsc.intg.svcs.core;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.output.ByteArrayOutputStream;

import com.bsc.intg.svcs.core.exception.BSCComponentInitializationException;
import com.bsc.intg.svcs.core.exception.BSCErrorLevel;
import com.bsc.intg.svcs.core.exception.BSCMessageException;
import com.bsc.intg.svcs.core.exception.BSCServiceException;
import com.bsc.intg.svcs.core.message.BSCMessage;
import com.bsc.intg.svcs.core.util.BSCCryptoHelper;
import com.bsc.intg.svcs.core.util.BSCStringUtils;
import com.bsc.intg.svcs.crypto.BCPGPDecryptor;

public abstract class BSCListenerBase extends BSCComponentBase implements BSCListener {
	
	protected boolean scriptServiceEnabled 	= false;
	
	protected boolean preScriptEnabled 		= false; 
	protected boolean postScriptEnabled		= false;
	protected boolean errorScriptEnabled	= false;
	protected boolean 	decrypt 				=  false;

	protected String 	preScript				= null;
	protected String 	postScript				= null;
	protected String 	errorScript				= null;

	protected String 	scriptServiceName		= null;
	protected String	eventServiceName		= null;
	
	protected  ArrayList<String> services  		= null;
	
	protected boolean 	primary 				= true; 
	
	protected	String 	errorEventName			= null;
	
	public BSCListenerBase(BSCComponentType componentType ,BSCComponent parentComponent, String configName) {
		super(componentType, parentComponent, configName);
	}
	public BSCServiceContainer getServiceContainer() {
		return (BSCServiceContainer)this.parentComponent;
	}
	
	public void initialize(BSCComponentSubType componentSubType ) throws BSCComponentInitializationException {
		
		loadParentProperties();
		super.setComponentSubType(componentSubType);
		
		String serviceNames		=   getProperty("services");
		
		primary 				=   getBooleanProperty("primary",true);
		
		services				=   BSCStringUtils.convertCommaSeperated(serviceNames);
	
		scriptServiceName		=   this.getServiceContainer().getProperty("script.service.name","");
		scriptServiceEnabled	=	scriptServiceName.isEmpty() ? false : true ;
		
		eventServiceName		=  ((BSCServiceContainerBase)this.getServiceContainer()).getProperty("event.service.name");
		
		preScript				=   getProperty("pre.script","");
		postScript				=   getProperty("post.script","");
		errorScript				=   getProperty("error.script","");
		decrypt					=	getBooleanProperty("decrypt",false);
		
		if(!preScript.isEmpty()) 	preScriptEnabled=true;
		if(!postScript.isEmpty()) 	postScriptEnabled=true;
		if(!errorScript.isEmpty()) 	errorScriptEnabled=true;
		
		
		if ( services.size() == 0 ) {
			throw new BSCComponentInitializationException(this, BSCErrorLevel.CRITICAL,  "No services are defined for the listener " + getInstanceName() );
		}
	
		errorEventName 		=   this.getConfigName() + ".Event.Error";

	}
	
	@Override
	public void onMessage(BSCMessage message) throws BSCServiceException {
		
		BSCMessage response=null;
		
		if (preScriptEnabled) message.executeScript(preScript);
		
		Map<String, String> serviceRouteMap 	= new HashMap<String, String>();
		String serviceRouteProp = this.getProperty("service.route.map","");
		
		String key = this.getProperty("service.route","");
		
		if (!serviceRouteProp.isEmpty()){
			
			String[] serviceList = serviceRouteProp.split(";");
			for(String sericeObject: serviceList){
				String type = sericeObject.split("\\:")[0];
				String service = sericeObject.split("\\:")[1];
				serviceRouteMap.put(type, service);
			}
		}
		
		String serviceNames = serviceRouteMap.get(key);
		if(serviceNames!=null && !serviceNames.isEmpty()){
			services = BSCStringUtils.convertCommaSeperated(serviceNames);
		}

		for (String serviceObjName: services ) {
			
			BSCService service=null;

			try {
				service=this.getServiceContainer().getService(serviceObjName);
				
				if (service != null ) {
					
					if(decrypt){
						logger.info("Decrypting the message in listener base");
						message = decrypt(message);
						
					}
					
					logger.info("Executing the service {}", serviceObjName);
					response=service.execute(message);
					
				} else {
					logger.info("Unable to get the service {}", serviceObjName);
				}
		
			} finally {
				
				if(service != null) {
					try {
						this.getServiceContainer().releaseService(serviceObjName, service);
					} catch (Exception e) {
						logger.error("Failed to release the service {} to the pool", serviceObjName);
					}
				}
				
				if(response != null && postScriptEnabled ) {
					response.executeScript(postScript);
					response.close();
					response=null;
				}
			}
		}	
		
		

	}
	
	@SuppressWarnings("restriction")
	public BSCMessage decrypt(BSCMessage message)throws BSCServiceException{
		
		BCPGPDecryptor decryptor = new BCPGPDecryptor();
		BSCMessage outMessage = null;
		try {

			outMessage = message.cloneMessage(false);

			// Access decryption properties
			logger.info("Fetching decryption properties.");
			String privateKey = this.getProperty("dec.private.key");
			String signingKey = this.getProperty("dec.sign.verify.key");

			if (privateKey == null) {
				if (signingKey == null) {
					throw new BSCServiceException(this, null, BSCErrorLevel.ROLLBACK,
							"Properties not available for decryption.");
				}
			}

			String password = this.getProperty("dec.private.key.password");
		
			Boolean signingDone = false;

			if (signingKey!=null && signingKey.length() > 3) {
				signingDone = true;
			}

			// setting decryptor parameters
			decryptor.setPassphrase(BSCCryptoHelper.getSecret(password));
			decryptor.setPrivateKeyFilePath(privateKey);
			decryptor.setSigned(signingDone);
			decryptor.setSigningPublicKeyFilePath(signingKey);

			InputStream in = new ByteArrayInputStream(message.toByteArray());
			ByteArrayOutputStream fileOutStream = new ByteArrayOutputStream();
			logger.info("Calling Decrypt function");
			decryptor.decryptFile(in,fileOutStream);
			outMessage.loadMessage(fileOutStream.toByteArray());
			logger.info("Decryption is done ");

		} catch (BSCMessageException | Exception e) {

			
				throw new BSCServiceException(this, e, BSCErrorLevel.ROLLBACK,
						"Failed decryption of message.");

			
		}


		
		return outMessage;
	}

	
	public List<String> getServiceNames() {
		return services;
	}
	
	public String getLockDir() {
        return "";
    }
	
}